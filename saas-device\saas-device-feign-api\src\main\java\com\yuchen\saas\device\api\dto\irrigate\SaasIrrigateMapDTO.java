package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SaasIrrigateMapDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID")
    private Long id;

    /**
     * 点位ID
     */
    @ApiModelProperty(name = "irrigatePointId", value = "点位ID")
    private String irrigatePointId;
    /**
     * 点位名称
     */
    @ApiModelProperty(name = "irrigatePointName", value = "点位名称")
    private String irrigatePointName;
    /**
     * 区域id
     */
    @ApiModelProperty(name = "irrigateAreaId", value = "区域id")
    private Long irrigateAreaId;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remarks", value = "备注信息")
    private String remarks;
    /**
     * x坐标
     */
    @ApiModelProperty(name = "x", value = "x坐标")
    private String x;
    /**
     * y坐标
     */
    @ApiModelProperty(name = "y", value = "y坐标")
    private String y;
    /**
     * 地理位置
     */
    @ApiModelProperty(name = "address", value = "地理位置")
    private String address;
    /**
     * 园区点位ID
     */
    @ApiModelProperty(name = "parkPointLocationId", value = "园区点位ID")
    private Long parkPointLocationId;
    /**
     * 园区ID（项目ID）
     */
    @ApiModelProperty(name = "projectId", value = "园区ID（项目ID）")
    private Long projectId;

}





















































































































































































































































































































































































































































































































































































































































































































































































































































































































































































