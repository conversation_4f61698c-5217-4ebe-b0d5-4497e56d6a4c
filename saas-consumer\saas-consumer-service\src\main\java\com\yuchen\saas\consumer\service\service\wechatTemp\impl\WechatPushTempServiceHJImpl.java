package com.yuchen.saas.consumer.service.service.wechatTemp.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.thinkunion.park.service.common.cache.CacheNames;
import com.thinkunion.park.service.common.enums.parking.AppEnum;
import com.yuchen.saas.consumer.api.dto.wechat.SendTemplateReqDTO;
import com.yuchen.saas.consumer.api.dto.wechat.SendTemplateRespDTO;
import com.yuchen.saas.consumer.api.entity.ConfigTempInfo;
import com.yuchen.saas.consumer.service.config.WeChatConfig;
import com.yuchen.saas.consumer.service.service.WeChatService;
import com.yuchen.saas.consumer.service.service.wechatTemp.IConfigTempInfoService;
import com.yuchen.saas.consumer.service.service.wechatTemp.WechatPushTempReportService;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * 厦门火炬智慧平台公众号模板消息发送
 * <AUTHOR>
 * @create 2024/1/5 14:45
 */

@Slf4j
@Component("wechatPushTempServiceHJImpl")
public class WechatPushTempServiceHJImpl implements WechatPushTempReportService {

    @Resource
    private WeChatConfig weChatConfig;

    @Resource
    private WeChatService weChatService;

    @Resource
    private IConfigTempInfoService configTempInfoService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private NestRedis nestRedis;

    private static final String SEND_URL = "%s/cgi-bin/message/template/send?access_token=%s";

    /**
     * 通用模板发送
     * @param reqDTO 模版消息实体类
     * @return
     */
    @Override
    public void sendWechatTempMessage(SendTemplateReqDTO reqDTO) {
        if (StrUtil.isBlank(reqDTO.getTouser())) {
            return;
        }
        log.info("发送{}公众号模板消息", AppEnum.getDescByCode(reqDTO.getOfficialAppId()));
        String url = String.format(SEND_URL, weChatConfig.getDomainByAppId(reqDTO.getOfficialAppId()), getWxToken(reqDTO.getOfficialAppId()));
        // 模板数据设置
        SendTemplateReqDTO sendTemplateReqDTO = setOfficialTempConfigData(reqDTO);
        // 发送模板
        ResponseEntity<SendTemplateRespDTO> responseEntity = restTemplate.postForEntity(url, JSONObject.toJSONString(sendTemplateReqDTO), SendTemplateRespDTO.class);
        SendTemplateRespDTO response = Optional.ofNullable(responseEntity.getBody()).orElse(SendTemplateRespDTO.getInstance());
        log.info("{}模板消息推送结果{}", reqDTO, response);
        if (Objects.requireNonNull(response).isSuccess()) {
            log.info("发送{}模板消息推送成功", AppEnum.getDescByCode(reqDTO.getOfficialAppId()));
            return;
        } else if  (response.getErrcode() == 41001 ||response.getErrcode() == 40001) {
            Long count = nestRedis.incrBy("HJ" + Thread.currentThread().getId(),1L);
            log.info("{}token失效重新获取token====重试次数为:{}", AppEnum.getDescByCode(reqDTO.getOfficialAppId()), count);
            if (count > 3 ) {
                log.info("重试次数为：{}====结束方法", count);
                return;
            }
            nestRedis.del(String.format(CacheNames.WECHAT_ACCESS_TOKEN_NAME, reqDTO.getOfficialAppId()));
            sendWechatTempMessage(reqDTO);
        }
        log.info("{}公众号推送失败：{} {}", AppEnum.getDescByCode(reqDTO.getOfficialAppId()), response.getErrcode(), response.getErrmsg());
    }

    @Override
    public String type() {
        return AppEnum.HJZHPT_APP_OFFICIAL.getCode();
    }


    @Override
    public String getWxToken(String appId) {
        return weChatService.getWXAccessToken(appId, weChatConfig.getAppSecretByAppId(appId));
    }

    @Override
    public SendTemplateReqDTO setOfficialTempConfigData(SendTemplateReqDTO reqDTO) {
        // 获取模板id
        ConfigTempInfo configTempInfo = configTempInfoService.getConfigTempInfoByTempCodeAndAppId(reqDTO.getTemplateCode(), reqDTO.getOfficialAppId());
        reqDTO.setTemplate_id(configTempInfo.getTempId());
        if (Objects.nonNull(reqDTO.getMiniprogram())) {
            JSONObject miniprogram = reqDTO.getMiniprogram();
            if (StrUtil.isBlank(miniprogram.getString("pagepath"))) {
                miniprogram.put("pagepath", configTempInfo.getTempUrl());
            }
            reqDTO.setMiniprogram(miniprogram);
        }
        return reqDTO;
    }
}
