package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.entity.NetAlarmType;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface NetAlarmTypeMapper extends BaseMapper<NetAlarmType> {

    @Select("SELECT * from net_alarm_type ${ew.customSqlSegment}")
    Page<NetAlarmType> selectPageAlarmType(Page<NetAlarmType> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<NetAlarmType> wrapper);


}
