package com.yuchen.saas.device.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;

@Data
public class NetDutyRecordsPageQryDto extends Query {

    /**
     * 所属项目id
     */
    private Long parkId;
    /**
     * 人员姓名
     */
    private String userName;
    /**
     * 工号
     */
    private String jobNum;
    /**
     * 事件类型
     */
    private Integer eventType;
    /**
     * 上报开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startReportTime;
    /**
     * 上报结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endReportTime;

}
