package com.yuchen.saas.device.api.dto.operatingEnd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/23 14:20
 */
@Data
public class NetDeviceModelQryReqDTO extends Query {

    /**
     * 设备品牌id
     */
    @ApiModelProperty(name = "deviceBrandId", value = "设备品牌id")
    private Long deviceBrandId;
    /**
     * 型号名称
     */
    @ApiModelProperty(name = "modelName", value = "型号名称")
    private String modelName;
    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "deviceCategoryId", value = "设备类型")
    private Long deviceCategoryId;
    /**
     * 业务状态 1开启，0关闭
     */
    @ApiModelProperty("业务状态 业务状态 1开启，0关闭")
    private Integer status;

    @ApiModelProperty(name = "ids", value = "ids")
    private List<Long> ids;
}
