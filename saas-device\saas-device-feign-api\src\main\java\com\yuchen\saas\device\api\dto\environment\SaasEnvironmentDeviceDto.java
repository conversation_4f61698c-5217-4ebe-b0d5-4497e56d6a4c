package com.yuchen.saas.device.api.dto.environment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SaasEnvironmentDeviceDto {
    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID")
    private Long id;
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 场景类型(1:环境监测 3:智慧浇灌 4:智慧消防)
     */
    @ApiModelProperty(name = "sceneType", value = "场景类型(1:环境监测 3:智慧浇灌 4:智慧消防)")
    private Integer sceneType;
    /**
     * 设备分类(2:硬件设备 3:传感设备)
     */
    @ApiModelProperty(name = "deviceCategory", value = "设备分类(2:硬件设备 3:传感设备)")
    private Integer deviceCategory;
    /**
     * 所属产品
     */
    @ApiModelProperty(name = "productKey", value = "所属产品")
    private String productKey;
    /**
     * 设备类型ID
     */
    @ApiModelProperty(name = "deviceTypeId", value = "设备类型ID")
    private Long deviceTypeId;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 品牌型号
     */
    @ApiModelProperty(name = "brandModel", value = "品牌型号")
    private String brandModel;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceLocation", value = "设备位置")
    private String deviceLocation;
    /**
     * 经度
     */
    @ApiModelProperty(name = "longitude", value = "经度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(name = "latitude", value = "经度")
    private String latitude;
    /**
     * 安装空间ID
     */
    @ApiModelProperty(name = "installSpaceId", value = "安装空间ID")
    private Long installSpaceId;
    /**
     * 安装空间名称
     */
    @ApiModelProperty(name = "installSpaceName", value = "安装空间名称")
    private String installSpaceName;
    /**
     * 设备图片
     */
    @ApiModelProperty(name = "deviceImageUrl", value = "设备图片")
    private String deviceImageUrl;
    /**
     * 设备描述
     */
    @ApiModelProperty(name = "deviceDesc", value = "设备描述")
    private String deviceDesc;

    /**
     * 所在点位：x,y
     */
    @ApiModelProperty(name = "coordinates", value = "所在点位：x,y")
    private String coordinates;

    /**
     * 所在点位：x,y
     */
    @ApiModelProperty(name = "coordinateName", value = "点位：名称")
    private String coordinateName;

}
