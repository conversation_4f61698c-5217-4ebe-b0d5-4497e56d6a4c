package com.yuchen.saas.auth.granter;

import cn.hutool.core.lang.Validator;
import com.yuchen.saas.manage.user.api.entity.UserInfo;
import com.yuchen.saas.manage.user.api.enums.AccountStatusEnum;
import com.yuchen.saas.manage.user.api.enums.UserEnum;
import com.yuchen.saas.manage.user.api.feign.IUserClient;
import org.apache.commons.lang.StringUtils;
import org.nest.springwrap.core.launch.constant.AppConstant;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.nest.springwrap.core.tool.utils.Func;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

public class SmsCodeTokenGranter extends AbstractTokenGranter {
    private static final String SMS_GRANT_TYPE = "sms_code";

    private UserDetailsService userDetailsService;

    private NestRedis nestRedis;

    private IUserClient userClient;


    public SmsCodeTokenGranter(AuthorizationServerTokenServices tokenServices,
                               ClientDetailsService clientDetailsService,
                               OAuth2RequestFactory requestFactory,
                               UserDetailsService userDetailsService,
                               NestRedis nestRedis,
                               IUserClient userClient) {
        super(tokenServices, clientDetailsService, requestFactory, SMS_GRANT_TYPE);
        this.userDetailsService = userDetailsService;
        this.nestRedis = nestRedis;
        this.userClient = userClient;
    }


    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client,
                                                           TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<String, String>(tokenRequest.getRequestParameters());

        // 客户端提交的手机号码
        String phoneNumber = parameters.get("phone");
        if (StringUtils.isBlank(phoneNumber)) {
            throw new UserDeniedAuthorizationException("电话号码是空的！");
        }
        //验证手机号格式
        if (!Validator.isMobile(phoneNumber)) {
            throw new UserDeniedAuthorizationException("请输入正确的手机号码");
        }

        String redisCode = nestRedis.get(phoneNumber);
        //String redisCode = "6666";
        // 客户端提交的验证码
        //测试环境跟开发环境验证码改为6666
        Properties props = System.getProperties();
        String profile =props.getProperty("nest.env");
        if(AppConstant.DEV_CODE.equals(profile)||AppConstant.TEST_CODE.equals(profile)||AppConstant.PRE_CODE.equals(profile)){
            redisCode = "6666";
        }
        String smsCode = parameters.get("code");
        if (StringUtils.isBlank(smsCode)) {
            throw new UserDeniedAuthorizationException("短信验证码过期了");
        }
        if (!smsCode.equals(redisCode)) {
            throw new UserDeniedAuthorizationException("验证码错误,请重新输入");
        }
        String tenantId = parameters.get("tenantId");
        String deptId = parameters.get("deptId");
        Long orgUserId=null;
        if(Func.isNotEmpty(parameters.get("orgUserId"))){
            orgUserId = Long.valueOf(parameters.get("orgUserId"));
        }
    /*    UserInfoEntity userInfo = userClient.userEntityInfo(tenantId,phoneNumber).getData();
        if(userInfo == null){
            throw new UserDeniedAuthorizationException("没有此用户");
        }
        if(Objects.equals(userInfo.getIsDisable(),1)){
            throw new UserDeniedAuthorizationException("此用户被禁用了");
        }*/
      //  User account = userClient.accountInfo(userInfo.getId().toString(),tenantId).getData();
     //   User account = userClient.userByPhone(tenantId,phoneNumber,deptId).getData();
        UserInfo account = userClient.userInfoByPhone(tenantId,phoneNumber,deptId,"","", UserEnum.WEB.getName(),-1,orgUserId).getData();
        if(account == null){
            throw new UserDeniedAuthorizationException("此用户还没绑定账号");
        }
        if(Objects.equals(account.getIsDisable(), AccountStatusEnum.DISABLE.getType())){
            throw new UserDeniedAuthorizationException("此用户绑定账号被禁用了");
        }
       // UserDetails user = userDetailsService.loadUserByUsername(account.getAccount());
        UserDetails user = userDetailsService.loadUserByUsername(account.getPhone());
        AbstractAuthenticationToken userAuth = new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());

        userAuth.setDetails(parameters);
        OAuth2Request oAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(oAuth2Request, userAuth);
    }
}
