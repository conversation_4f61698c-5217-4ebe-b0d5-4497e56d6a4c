package com.yuchen.saas.device.api.dto.operatingEnd.product;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/27 16:41
 */
@Data
public class NetProductFunctionSubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;

    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    @NotNull(message = "产品id不能为空")
    private Long productId;
    /**
     * 功能url
     */
    @ApiModelProperty(name = "url", value = "功能url")
    private String url;
    /**
     * 功能id
     */
    @ApiModelProperty(name = "functionId", value = "功能id")
    @NotBlank(message = "功能id不能为空")
    private String functionId;
    /**
     * 功能标识
     */
    @ApiModelProperty(name = "functionKey", value = "功能标识")
    @NotBlank(message = "功能标识不能为空")
    private String functionKey;
    /**
     * 功能名称
     */
    @ApiModelProperty(name = "functionName", value = "功能名称")
    @NotBlank(message = "功能名称不能为空")
    private String functionName;
    /**
     /**
     * 调用方式(1:同步，2：异步)
     */
    @ApiModelProperty(name = "invokeModel", value = "调用方式(1:同步，2：异步)")
    private String invokeModel;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 输入参数Json字符串
     */
    @ApiModelProperty(name = "inputJsonParam", value = "输出参数Json字符串")
    private List<Param> inputJsonParam;

    /**
     * 输出参数Json字符串
     */
    @ApiModelProperty(name = "outputJsonParam", value = "输出参数Json字符串")
    private List<Param> outputJsonParam;

    @Data
    public static class Param {
        /**
         * 参数标识
         */
        private String paramKey;

        /**
         * 参数名称
         */
        private String paramName;

        /**
         * NetDataTypeEnum
         * 属性类型
         * 1：int(整数型)
         * 2：long(长整数型)
         * 3：float(单精度浮点型)
         * 4：double(双精度浮点型)
         * 5：text(字符串)
         * 6：bool(布尔型)
         * 7：time(时间类型)
         */
        private Integer paramType;
    }
}
