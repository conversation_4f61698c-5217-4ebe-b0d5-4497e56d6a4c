package com.yuchen.saas.auth.support;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.DelegatingPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.crypto.password.Pbkdf2PasswordEncoder;
import org.springframework.security.crypto.scrypt.SCryptPasswordEncoder;

import java.util.HashMap;
import java.util.Map;


public class NestPasswordEncoderFactories {


    public static PasswordEncoder createDelegatingPasswordEncoder() {
        String encodingId = "nest";
        Map<String, PasswordEncoder> encoders = new HashMap<>(16);
        encoders.put(encodingId, new NestPasswordEncoder());
        encoders.put("bcrypt", new BCryptPasswordEncoder());
        encoders.put("noop", NestNoOpPasswordEncoder.getInstance());
        encoders.put("pbkdf2", new Pbkdf2PasswordEncoder());
        encoders.put("scrypt", new SCryptPasswordEncoder());

        return new DelegatingPasswordEncoder(encodingId, encoders);
    }

    private NestPasswordEncoderFactories() {
    }

}
