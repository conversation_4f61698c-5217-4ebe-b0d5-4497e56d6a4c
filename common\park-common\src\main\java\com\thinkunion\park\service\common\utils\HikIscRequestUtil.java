package com.thinkunion.park.service.common.utils;

import com.alibaba.fastjson.JSON;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.thinkunion.park.service.common.pojo.HikIscResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName HikIscRequestUtil
 * @Description 海康请求工具类
 * <AUTHOR>
 * @Date 2022年10月31日 16:20:38
 */
public class HikIscRequestUtil {

    /**
     * 构造方法，实例化的时候必须存在
     *
     * @param host      平台的ip端口
     * @param appKey    密钥appkey
     * @param appSecret 密钥appSecret
     */
    public HikIscRequestUtil(String host, String appKey, String appSecret) {
        ArtemisConfig.host = host;
        ArtemisConfig.appKey = appKey;
        ArtemisConfig.appSecret = appSecret;
    }

    /**
     * 开始请求
     *
     * @param body    请求参数
     * @param apiPath 请求路径
     * @return
     */
    public HikIscResponse getDataInfo(Object body, String apiPath) {
        System.out.println("RequestJson:" + JSON.toJSONString(body));
        String reqBody = JSON.toJSONString(body);
        final String reqApiPath = "/artemis" + apiPath;
        Map<String, String> path = new HashMap<>(1);
        path.put("https://", reqApiPath);
        HikIscResponse response = JSON.parseObject(ArtemisHttpUtil.doPostStringArtemis(path,
                reqBody, null, null, "application/json"), HikIscResponse.class);
        return response;
    }
}
