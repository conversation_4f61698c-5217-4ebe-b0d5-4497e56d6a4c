package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/30 15:53
 * @Description
 */
@Data
@ApiModel(value = "GreenPlantsSaveDTO")
public class SaasIrrigateGreenPlantsSaveDTO {

    /**
     * 绿植编码（id）
     */
    @ApiModelProperty(name = "greenPlantsCode", value = "绿植编码（id）")
    @NotNull(message = "绿植编码不能为空")
    private String greenPlantsCode;
    /**
     * 绿植名称
     */
    @ApiModelProperty(name = "greenPlantsName", value = "绿植名称")
    @NotNull(message = "绿植名称不能为空")
    private String greenPlantsName;
    /**
     * 备注（描述信息）
     */
    @ApiModelProperty(name = "describeContents", value = "备注（描述信息）")
    private String describeContents;
    /**
     * 园区ID（项目id）
     */
    @ApiModelProperty(name = "parkId", value = "园区ID（项目id）")
    @NotNull(message = "项目id不能为空")
    private Long parkId;

}
