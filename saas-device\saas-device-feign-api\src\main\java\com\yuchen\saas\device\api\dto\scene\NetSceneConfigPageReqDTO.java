package com.yuchen.saas.device.api.dto.scene;

import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

@Data
public class NetSceneConfigPageReqDTO extends Query {

    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 场景名称
     */
    private String sceneName;
    /**
     * 触发类型，1-设备触发，2-定时触发，3-手动触发
     */
    private Integer triggerMode;

    /**
     * 启用状态; 1-成功，0-失败
     */
    private Integer status;


}
