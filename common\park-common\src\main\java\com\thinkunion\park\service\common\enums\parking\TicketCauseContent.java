package com.thinkunion.park.service.common.enums.parking;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/14 16:20
 * 车牌类型（0：标准民用车，1：02式民用车牌，2：武警车车牌，3：警车车牌，4：民用车双行尾牌，5：使馆车牌，6：农用车车牌，7：摩托车，8：新能源车，13：军车车牌）
 */
public enum TicketCauseContent {

    IDENTIFICATION_PASS_FORBID("未识别不放行"),
    TEMPORARY_CAR_PASS_FORBID("临停不放行"),
    VISITOR_CAR_PASS_FORBID("访客车不放行"),
    BLACK_CAR_PASS_FORBID("黑名单不放行"),
    BLACK_CAR_PASS("黑名单放行"),
    TEMPORARY_CAR_PARKING_SPACE_IS_FULL_PASS_FORBID("临停车位满不放行"),
    TEMPORARY_CAR_PARKING_FIXED_SPACE_IS_FULL_PASS_FORBID("固定车位满不放行"),
    TEMPORARY_CAR_CHARGE_OF_0_PASS_FORBID("临停收费为0不放行"),
    TEMPORARY_CAR_DID_NOT_PAY_PASS_FORBID("临停未缴费不放行"),
    TEMPORARY_TIME_FREE_NEED_PAY_PASS_FORBID("错时临停需缴费%s元"),
    TEMPORARY_TIME_FREE_NOT_PAY_PASS_FORBID("错时临停未缴费不放行"),
    ON_THE_TRUCK_PARKING_SPACE_IS_FULL_PASS_FORBID("月卡车位满不放行"),
    ON_THE_TRUCK_CHARGE_OF_0_PASS_FORBID("月卡收费为0不放行"),
    NEED_TO_PAY_YUAN("需缴费%s元"),
    MONTHLY_REMAINING_DAYS("有效期: 剩余%s天"),
    HOLIDAYS_FREE_PASS_ALLOWD("节假日免费放行"),
    SPECIAL_CAR_PASS_ALLOWED("特殊车牌放行"),
    VEHICLE_GROUP_PASS_ALLOWED("车组放行"),
    SHARE_PLACE_PASS_ALLOWED("AB车位放行"),
    VEHICLE_GROUP_PASS_FORBID("车组禁止放行"),
    SPC_BLACK_CAR_PASS_FORBID("电站黑名单不放行"),
    /**
     * 请在公众号入园放行
     */
    ANONYMOUS_PLATE_NO_ENTER_FORBID_CONTENT("请在公众号入园放行"),
    ANONYMOUS_PLATE_NO_EXIT_FORBID_CONTENT("请在公众号出园放行"),
    TEMP_PASS("临停放行"),
    PUBLIC_ACCOUNT_AUTOMATIC_RELEASE("公众号放行"),
    TEMP_NOT_PAY("临停未缴费"),
    TEMP_PAYED("临停已缴费"),
    TEMP_FREE_TIME("临停免费时间"),
    MONTH_CAR("有效月卡"),

    TIME_FREE_CAR("有效错时卡"),
    GROUP_FREE("固定车免费"),
    NOT_IN_EVENT("无入场时间"),
    GLOBAL_FREE_PASS("暂未收费免费放行"),
    LOGIC_ABNORMAL("业务异常自动放行"),

    TEAM_PASS_ALLOWD("团队车放行"),
    SHARE_PLACE_PASS_ALLOWD("AB车位放行"),
    TEAM_PLACE_PASS_ALLOWD("团队车位放行"),
    TIME_FREE_PASS_ALLOWD("错时车放行"),
    PROPERTY_RIGHT_PASS_ALLOWD("产权车放行"),
    INSIDE_CAR_PASS_ALLOWD("内部车放行"),
    FREE_PASS_ALLOWD("免费放行"),
    VISITOR_PASS_ALLOWD("访客车放行"),
    ;

    private String value;


    TicketCauseContent(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }


}
