package com.yuchen.saas.device.api.dto.operatingEnd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;
import java.util.List;

@Data
public class NetDevicePageQryDTO extends Query {

    /**
     * 所属客户(组织)
     */
    @ApiModelProperty(name = "customerManageId", value = "所属客户(组织)")
    private Long customerManageId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;

    /**
     * iot设备标识
     */
    @ApiModelProperty(name = "deviceName", value = "iot设备标识")
    private String deviceName;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;

    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "deviceCategoryId", value = "设备类型")
    private Long deviceCategoryId;
    /**
     * 大类（1：视频监控，2：智能硬件，3：传感设备）
     */
    @ApiModelProperty(name = "broadCategory", value = "大类（1：视频监控，2：智能硬件，3：传感设备）")
    private String broadCategory;

    /**
     * 设备状态；0-离线，1-在线
     */
    @ApiModelProperty(name = "isOnline", value = "是否在线；0-离线，1-在线")
    private Integer isOnline;

    /**
     * 地理位置
     */
    @ApiModelProperty(name = "location", value = "地理位置")
    private String location;

    /**
     * 更新开始时间
     */
    @ApiModelProperty(name = "updateStartTime", value = "更新开始时间")
    private Date updateStartTime;
    /**
     * 更新结束时间
     */
    @ApiModelProperty(name = "updateEndTime", value = "更新结束时间")
    private Date updateEndTime;

    /**
     * 已选择设备ID
     */
    @ApiModelProperty(name = "selectDeviceIdList", value = "已选择设备ID")
    private List<Long> selectDeviceIdList;

    /**
     * 已选择设备编码
     */
    @ApiModelProperty(name = "selectDeviceNameList", value = "已选择设备编码")
    private List<String> selectDeviceNameList;

    @ApiModelProperty(name = "thirdPlatformDeviceName", value = "第三方编码")
    private String thirdPlatformDeviceName;


    @ApiModelProperty(name = "deviceModelId", value = "设备型号id")
    private Long deviceModelId;


}
