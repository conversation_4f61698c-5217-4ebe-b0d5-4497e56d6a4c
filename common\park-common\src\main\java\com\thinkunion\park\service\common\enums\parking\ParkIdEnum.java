package com.thinkunion.park.service.common.enums.parking;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 *
 *
 */
@AllArgsConstructor
public enum ParkIdEnum {


    TXYQ(1371400533973180417L, "同翔一期-地面"),
    TXYQ_DM(1371400533973170001L, "同翔一期-地库"),
    BFTY(1405484512917372929L, "八方通用厂房"),
    JGZX(1407312494715768834L, "火炬加工中心"),
   ;

    private Long code;

    public Long getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Long code) {
        ParkIdEnum[] enumArray = ParkIdEnum.values();
        for (ParkIdEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }

    public static List<Long> getCodeList() {
        ParkIdEnum[] enumArray = ParkIdEnum.values();
        List<Long> list= Lists.newArrayList();
        for (ParkIdEnum e : enumArray) {
            list.add(e.code);
        }
        return list;
    }
}
