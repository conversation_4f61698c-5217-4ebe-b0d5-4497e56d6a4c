package com.yuchen.saas.consumer.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @description: 全电发票新增参数订单子表
 * <AUTHOR>
 * @date 2023/5/22 9:44
 * @version 1.0
 */
@Data
@ApiModel(value = "全电发票新增参数订单子表", description = "全电发票新增参数订单子表")
public class QdPmgBatParkInvoiceOrderItem extends PmgBatParkInvoiceOrderItem {

    /**
     * (全电发票)商品单价
     */
    @TableField(exist = false)
    private String price;

    /**
     * (全电发票)数量
     */
    @TableField(exist = false)
    private String quantity;
}
