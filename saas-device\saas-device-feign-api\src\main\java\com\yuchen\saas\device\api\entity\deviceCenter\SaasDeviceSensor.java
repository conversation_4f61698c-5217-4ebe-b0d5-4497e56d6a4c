package com.yuchen.saas.device.api.entity.deviceCenter;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

import java.util.Date;

/**
 * 传感器设备表
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_device_sensor")
@ApiModel(value="SaasDeviceSensor对象", description="传感器设备表")
public class SaasDeviceSensor extends BaseEntity {

    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    private Long customerId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 空间id
     */
    @ApiModelProperty(name = "spaceId", value = "空间id")
    private Long spaceId;
    /**
     * 传感器类型
     */
    @ApiModelProperty(name = "sensorType", value = "传感器类型")
    private Long sensorType;
    /**
     * 产品key
     */
    @ApiModelProperty(name = "productKey", value = "产品key")
    private String productKey;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;
    /**
     * 设备图片地址
     */
    @ApiModelProperty(name = "deviceImages", value = "设备图片地址")
    private String deviceImages;
    /**
     * 设备状态
     */
    @ApiModelProperty(name = "deviceState", value = "设备状态")
    private String deviceState;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceAddress", value = "设备位置")
    private String deviceAddress;
    /**
     * 经度
     */
    @ApiModelProperty(name = "longitude", value = "经度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(name = "latitude", value = "纬度")
    private String latitude;
    /**
     * 最后连接时间
     */
    @ApiModelProperty(name = "lastConnectionTime", value = "最后连接时间")
    private Date lastConnectionTime;
    /**
     * 最后断开时间
     */
    @ApiModelProperty(name = "lastBreakTime", value = "最后断开时间")
    private Date lastBreakTime;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;



}
