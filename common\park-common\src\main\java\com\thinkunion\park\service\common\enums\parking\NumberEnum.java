package com.thinkunion.park.service.common.enums.parking;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 19:43
 */
public enum NumberEnum {
    ZERO(0),

    ONE(1),

    TWO(2),

    THRE<PERSON>(3),

    FOUR(4),

    <PERSON>IVE(5),

    <PERSON><PERSON>(6),

    <PERSON><PERSON><PERSON>(7),

    <PERSON><PERSON><PERSON>(8),

    <PERSON>IN<PERSON>(9),

    TEN(10),

    FOURTE<PERSON>(14),

    FIF<PERSON><PERSON>(15),

    TWENTY_NINE(29),

    THIRTY(30),;


    private int value;

    NumberEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

}
