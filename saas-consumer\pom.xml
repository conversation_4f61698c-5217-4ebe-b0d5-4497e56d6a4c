<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>smart-park-integration</artifactId>
        <groupId>com.yuchen.saas</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>saas-consumer</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>saas-consumer-service</module>
        <module>saas-consumer-api</module>
    </modules>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>park-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.nest.springwrap</groupId>
                    <artifactId>core-launch</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>starter-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>core-cloud</artifactId>
        </dependency>

        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>starter-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>core-boot</artifactId>
        </dependency>

    </dependencies>


</project>