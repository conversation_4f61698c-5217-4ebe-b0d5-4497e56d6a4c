<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.irrigate.SaasIrrigateAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.irrigate.SaasIrrigateArea">
        <id column="id" property="id" />
        <result column="irrigate_area_code" property="irrigateAreaCode" />
        <result column="irrigate_area_name" property="irrigateAreaName" />
        <result column="describe_contents" property="describeContents" />
        <result column="green_plants_id" property="greenPlantsId" />
        <result column="park_id" property="parkId" />
        <result column="status" property="status" />
        <result column="create_org_user" property="createOrgUser" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, irrigate_area_code, irrigate_area_name, describe, green_plants_id, park_id, tenant_id, status, create_org_user, create_user, create_dept, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
