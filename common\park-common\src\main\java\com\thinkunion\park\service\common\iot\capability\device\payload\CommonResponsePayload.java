package com.thinkunion.park.service.common.iot.capability.device.payload;

public class CommonResponsePayload<T> {
    protected int code;
    protected String id;
    protected String message;
    protected T data;

    public CommonResponsePayload() {
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public boolean isSuccess() {
        return this.code >= 200 && this.code < 300;
    }
}
