package com.yuchen.saas.device.api.dto.environment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SaasEnvironmentAlarmDispatchDto {
    /**
     * 工单单号
     */
    @ApiModelProperty(name = "workOrderNo", value = "工单单号")
    private String workOrderNo;
    /**
     * 告警ID
     */
    @ApiModelProperty(name = "alarmId", value = "告警ID")
    private Long alarmId;
    /**
     * 告警名称
     */
    @ApiModelProperty(name = "alarmName", value = "告警名称")
    private String alarmName;
    /**
     * 告警分类
     */
    @ApiModelProperty(name = "alarmCategory", value = "告警分类")
    private Integer alarmCategory;
    /**
     * 告警内容
     */
    @ApiModelProperty(name = "alarmContent", value = "告警内容")
    private String alarmContent;
    /**
     * 告警等级
     */
    @ApiModelProperty(name = "alarmLevel", value = "告警等级")
    private Integer alarmLevel;
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 告警时间
     */
    @ApiModelProperty(name = "alarmTime", value = "告警时间")
    private Date alarmTime;
    /**
     * 告警设备
     */
    @ApiModelProperty(name = "alarmDevice", value = "告警设备")
    private String alarmDevice;
    /**
     * 设备类型ID
     */
    @ApiModelProperty(name = "deviceTypeId", value = "设备类型ID")
    private Long deviceTypeId;
    /**
     * 设备地址
     */
    @ApiModelProperty(name = "deviceAddr", value = "设备地址")
    private String deviceAddr;
    /**
     * 故障类型
     */
    @ApiModelProperty(name = "faultType", value = "故障类型")
    private Integer faultType;
    /**
     * 故障等级
     */
    @ApiModelProperty(name = "faultLevel", value = "故障等级")
    private Integer faultLevel;
    /**
     * 绑定空间
     */
    @ApiModelProperty(name = "bindSpace", value = "绑定空间")
    private String bindSpace;
    /**
     * 图片/视频
     */
    @ApiModelProperty(name = "alarmImage", value = "图片/视频")
    private String alarmImage;
    /**
     * 工单时限ID
     */
    @ApiModelProperty(name = "timeLimitId", value = "工单时限ID")
    private Long timeLimitId;
    /**
     * 工单处理时限(单位:小时)
     */
    @ApiModelProperty(name = "timeLimitCount", value = "工单处理时限(单位:小时)")
    private Integer timeLimitCount;
    /**
     * 工单大类(1:报事报修 2:设备报修 3:告警运维)
     */
    @ApiModelProperty(name = "workOrderCategory", value = "工单大类(1:报事报修 2:设备报修 3:告警运维)")
    private Integer workOrderCategory;
    /**
     * 工单类型ID
     */
    @ApiModelProperty(name = "workOrderTypeId", value = "工单类型ID")
    private Long workOrderTypeId;
    /**
     * 派工类型(1:有偿服务 2:无偿服务)
     */
    @ApiModelProperty(name = "dispatchType", value = "派工类型(1:有偿服务 2:无偿服务)")
    private Integer dispatchType;
    /**
     * 指派人
     */
    @ApiModelProperty(name = "dispatchUserIds", value = "指派人")
    private String dispatchUserIds;
    /**
     * 指派人
     */
    @ApiModelProperty(name = "dispatchUserNames", value = "指派人")
    private String dispatchUserNames;
    /**
     * 派工单号
     */
    @ApiModelProperty(name = "dispatchNo", value = "派工单号")
    private String dispatchNo;
    /**
     * 派工时间
     */
    @ApiModelProperty(name = "dispatchTime", value = "派工时间")
    private Date dispatchTime;
    /**
     * 派工备注
     */
    @ApiModelProperty(name = "dispatchRemark", value = "派工备注")
    private String dispatchRemark;
}
