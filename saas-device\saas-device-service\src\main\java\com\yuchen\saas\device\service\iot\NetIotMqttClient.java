package com.yuchen.saas.device.service.iot;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yuchen.saas.device.api.enums.NetProductEnum;
import com.yuchen.saas.device.service.event.ReceiveConnectDataEvent;
import com.yuchen.saas.device.service.event.ReceiveEventDataEvent;
import com.yuchen.saas.device.service.event.ReceivePropertyDataEvent;
import com.yuchen.saas.device.service.iot.input.DeviceOnlineInput;
import com.yuchen.saas.device.service.iot.input.DevicePropertyInput;
import com.yuchen.saas.device.service.rule.RunRuleAsyncService;
import com.yuchen.saas.device.service.utils.SignUtils;
import com.yuchen.saas.device.service.utils.TextHelper;
import com.yuchen.saas.park.api.dto.park.IoTEvent;
import com.yuchen.saas.park.api.entity.fault.ConnectEvent;
import com.yuchen.saas.park.api.vo.fault.DeviceProperty;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MqttDefaultFilePersistence;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class NetIotMqttClient {
    @Resource
    private IotMqttConfig iotMqttConfig;
    @Resource
    private ApplicationContext applicationContext;
    private MqttAsyncClient mqttAsyncClient = null;
    @Resource
    private DevicePropertyInput devicePropertyInput;

    @Resource
    private DeviceOnlineInput deviceOnlineInput;

    @Resource
    private RunRuleAsyncService runRuleAsyncService;
    private MqttConnectOptions connOpts =null;
    private int maxReconnectAttempts = 100;  // 最大重连尝试次数
    private long reconnectInterval = 5000; // 重连间隔时间（单位：毫秒）
    private int reconnectAttempts = 0;    // 当前重连尝试次数

    @PostConstruct
    public void init() {
        try {
            log.info("NetIotMqttClientInitIotMqttConfig = {}", iotMqttConfig.toString());
            long timestamp = System.currentTimeMillis() / 1000L;
            String clientId = "app_" + iotMqttConfig.getAccessKey() + "_" + IdUtil.fastSimpleUUID();
            String userNameStr = clientId + "|" + TextHelper.getRandomString() + "|" + timestamp;
            String password = new String(SignUtils.getSignature(userNameStr, iotMqttConfig.getAccessSecret()).toCharArray());
            log.info("NetIotMqttClientInitClientId={},userNameStr={},password={}", clientId, userNameStr, password);
            // 配置 mqtt 连接配置
            connOpts = new MqttConnectOptions();
            connOpts.setUserName(userNameStr);
            connOpts.setPassword(password.toCharArray());
            connOpts.setMqttVersion(MqttConnectOptions.MQTT_VERSION_3_1_1);
            connOpts.setMaxInflight(30);
            connOpts.setAutomaticReconnect(true);
            connOpts.setCleanSession(true);
            connOpts.setKeepAliveInterval(60);
            // 声明 mqtt 异步连接
            mqttAsyncClient = new MqttAsyncClient(iotMqttConfig.getUri(), clientId, new MqttDefaultFilePersistence());
            // 声明 mqtt 事件
            setCallback();
            // 连接 mqtt 服务
            connect(connOpts);
        } catch (Exception e) {
            log.error("NetIotMqttClientInitError", e);
        }
    }

    private void setCallback() {
        mqttAsyncClient.setCallback(new MqttCallbackExtended() {
            @Override
            public void connectComplete(boolean reconnect, String serverURI) {
                if (reconnect) {
                    log.info("MQTT重连成功Callback");
                }
                subscribe();
            }

            /**
             * 连接丢失
             * @param throwable
             */
            @SneakyThrows
            @Override
            public void connectionLost(Throwable throwable) {
                log.info("MQTT连接丢失，开始重连，原因:{}",throwable.getMessage());
                if (reconnectAttempts < maxReconnectAttempts) {
                    try {
                        TimeUnit.MILLISECONDS.sleep(reconnectInterval);
                        reconnect();
                    } catch (InterruptedException | MqttException e) {
                        log.info("重连过程出现异常", e);
                    }
                } else {
                    log.info("达到最大重连次数，放弃重连");
                }
            }

            /**
             * 收到消息
             * @param s 订阅的 Topic
             * @param mqttMessage 消息数据
             * @throws Exception
             */
            @Override
            public void messageArrived(String s, MqttMessage mqttMessage) throws Exception {

                log.info("NetIotMqttClientMessageArrived s={}, payload={}", s, new String(mqttMessage.getPayload()));
                try {
                    // 获取 Payload
                    JSONObject jsonObjectPayload = JSONObject.parseObject(new String(mqttMessage.getPayload()));
                    // 获取 Payload 中的 content
                    JSONObject jsonObjectContent = jsonObjectPayload.getJSONObject("content");
                    // 获取 Payload 中的 type
                    String payloadType = jsonObjectPayload.getString("type");
                    // 根据 订阅的 Topic 来处理 对应的数据
                    switch (payloadType) {
                        case "event":
                            // 收到事件数据
                            IoTEvent ioTEvent = JSON.parseObject(jsonObjectContent.toJSONString(), IoTEvent.class);
                            log.info("NetIotMqttClientMessageArrivedEvent , jsonObjectContent = {}",jsonObjectContent.toJSONString());
                            // 告警
                            applicationContext.publishEvent(new ReceiveEventDataEvent(ioTEvent));
                            // 触发场景联动
                            runRuleAsyncService.runRule("event", jsonObjectContent.toJSONString());
                            break;
                        case "property":
                            // 收到属性事件数据
                            DeviceProperty deviceProperty = JSON.parseObject(jsonObjectContent.toJSONString(),DeviceProperty.class);
                            log.info("NetIotMqttClientMessageArrivedProperty , jsonObjectContent = {}",jsonObjectContent.toJSONString());
                            // 告警
                            applicationContext.publishEvent(new ReceivePropertyDataEvent(deviceProperty));
                            devicePropertyInput.inputData(deviceProperty);
                            // 触发场景联动
                            runRuleAsyncService.runRule("property", jsonObjectContent.toJSONString());
                            break;
                        case "connect":
                        case "disconnect":
                            // 收到断开连接事件数据
                            ConnectEvent connectEvent = JSON.parseObject(jsonObjectContent.toJSONString(), ConnectEvent.class);
                            if(payloadType.equals("connect")){
                                connectEvent.setOnline(true);
                            }else {
                                connectEvent.setOnline(false);
                            }
                            connectEvent.setOnline("connect".equals(payloadType));
                            log.info("NetIotMqttClientMessageArrivedConnectOrDisconnect , jsonObjectContent = {}",jsonObjectContent.toJSONString());
                            // 告警
                            applicationContext.publishEvent(new ReceiveConnectDataEvent(connectEvent));
                            // 存储设备上下线状态
                            deviceOnlineInput.inputData(connectEvent);
                            // 触发场景联动
                            runRuleAsyncService.runRule("deviceStatusChange", jsonObjectContent.toJSONString());
                            break;
                        default:
                            // 默认还未找到匹配的数据
                    }
                } catch (Exception e) {
                    log.info("NetIotMqttClientMessageArrivedError1", e);
                }

            }

            /**
             * 交付完成
             * @param iMqttDeliveryToken
             */
            @Override
            public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {

            }

        });

    }


    private void connect(MqttConnectOptions connOpts) {
        try {
            mqttAsyncClient.connect(connOpts, null, new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    log.info("NetIotMqttClientConnect onSuccess");
                }
                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    log.info("NetIotMqttClientConnect onFailure exception = {}", exception.toString());
                }
            });
            log.info("NetIotMqttClientConnect serverUrl = {}", iotMqttConfig.getUri());
        } catch (Exception e) {
            log.info("NetIotMqttClientConnect error", e);
        }
    }

    private void subscribe() {
        for (String product : NetProductEnum.productKeyList()) {
            try {
                String topic = "";
                if (StringUtils.isNotBlank(iotMqttConfig.getShareGroupName())) {
                    //如果配置文件设置了共享订阅group则走共享订阅逻辑
                    //共享订阅topic：$share/default-group/app/{productKey}/event
                    topic = "$share/" + iotMqttConfig.getShareGroupName() + "/app/" + product + "/event";
                } else {
                    //没设置共享订阅group按原本规则走
                    topic = "$app/" + product + "/notification";
                }
                String finalTopic = topic;
                mqttAsyncClient.subscribe(topic, 0, null,
                        new IMqttActionListener() {
                            @Override
                            public void onSuccess(IMqttToken iMqttToken) {
                                log.info("NetIotMqttClientSubscribeOnSuccess, topic={}", finalTopic);
                            }

                            @Override
                            public void onFailure(IMqttToken iMqttToken, Throwable throwable) {
                                log.info("NetIotMqttClientSubscribeOnFailure, topic={}", finalTopic);
                            }
                        });
            } catch (Exception e) {
                log.info("NetIotMqttClientSubscribeError", e);
            }
        }
    }

    private void reconnect() throws MqttException {
        reconnectAttempts++;
        log.info("第 " + reconnectAttempts + " 次重连尝试");
        mqttAsyncClient.connect(connOpts, null, new IMqttActionListener() {
            @Override
            public void onSuccess(IMqttToken asyncActionToken) {
                log.info("MQTT重连成功");
                reconnectAttempts = 0;
            }

            @Override
            public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                log.info("MQTT重连失败", exception);
            }
        });
    }

}
