package com.yuchen.saas.device.api.dto.scene;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yuchen.saas.device.api.vo.scene.DeviceSceneLinkageRuleDetailActionVo;
import com.yuchen.saas.device.api.vo.scene.DeviceSceneLinkageRuleDetailConditionVo;
import com.yuchen.saas.device.api.vo.scene.DeviceSceneLinkageRuleDetailTriggerVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class DeviceSceneLinkageRuleConfigReqDTO implements Serializable {
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty(name = "id",value = "开关组规则id")
    @NotNull(message="id不能为空")
    private Long id;
    /**
     * 所属项目id
     */
    private Long projectId;

    /**
     * 业务服务id
     */
    @ApiModelProperty(name = "businessId", value = "业务服务id")
    private Long businessId;

    /**
     * 场景名称
     */
    private String sceneName;


    /**
     * 场景类型，1-设备触发，2-定时触发，3-手动触发
     */
    private Integer triggerMode;

    /**
     * xxl定时任务cron表达式（定时触发有值）
     */
    private String cron;

    /**
     * xxl任务id
     */
    private Integer jobId;

    /**
     * 规则描述
     */
    @ApiModelProperty(name = "description", value = "规则描述")
    private String description;

    /**
     * 执行规则
     */
    @ApiModelProperty(name = "childList", value = "执行规则")
    private String childList;

    /**
     * 规则详情
     */
    @ApiModelProperty(name = "ruleContentObj", value = "规则详情")
    private RuleContent ruleContentObj;

    /**
     * 规则详情
     */
    @ApiModelProperty(name = "ruleContent", value = "规则详情json字符串")
    private String ruleContent;

    public RuleContent getRuleContentObj() {
        return ruleContentObj!=null?ruleContentObj:StringUtils.isNotBlank(ruleContent)?JSON.parseObject(ruleContent,RuleContent.class):null;
    }

    public String getRuleContent() {
        return StringUtils.isNotBlank(ruleContent)?ruleContent:ruleContentObj==null?null: JSON.toJSONString(ruleContent);
    }

    @Data
    public static class RuleContent{
        @ApiModelProperty(name = "triggers", value = "触发器")
        private List<DeviceSceneLinkageRuleDetailTriggerVo> triggers;
        @ApiModelProperty(name = "conditions", value = "执行条件")
        private List<DeviceSceneLinkageRuleDetailConditionVo> conditions;
        @ApiModelProperty(name = "actions", value = "执行动作")
        private List<DeviceSceneLinkageRuleDetailActionVo> actions;
    }

}
