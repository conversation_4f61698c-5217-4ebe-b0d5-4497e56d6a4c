# 车位管理源码级分析

## 1. 主要涉及的实体类

- **PmgBatParkingPlace**：车位信息表，记录车位编号、类型、状态、归属等。
- **PmgBatParkingPlaceType**：车位类型表，区分固定、临时、共享等类型。
- **PmgBatParkingPlaceBind**：车位绑定表，记录车位与车辆、用户的绑定关系及有效期。
- **PmgBatParkingPlaceShare**：共享车位表，记录共享设置、审核、收益分配等。
- **PmgBatParkingPlaceFeeRule**：车位占用费规则表。
- **PmgBatParkingPlaceFeeBill**：车位占用费账单表。

## 2. 主要Controller

- `PmgBatParkingPlaceController`：车位信息注册、维护、类型管理接口
- `PmgBatParkingPlaceBindController`：固定车位分配、绑定、续期接口
- `PmgBatParkingPlaceShareController`：共享车位设置、审核、收益分配接口
- `PmgBatParkingPlaceFeeController`：车位占用费规则、账单、支付、统计接口

## 3. 主要Service接口与实现

- `PmgBatParkingPlaceService`：车位信息管理
- `PmgBatParkingPlaceTypeService`：车位类型管理
- `PmgBatParkingPlaceBindService`：车位分配、绑定、续期
- `PmgBatParkingPlaceShareService`：共享车位管理、审核、收益分配
- `PmgBatParkingPlaceFeeService`：占用费规则、账单、支付、统计

## 4. 典型方法调用链与数据流转

### 4.1 车位信息注册、维护、类型管理

- 通过`PmgBatParkingPlaceController`进行车位新增、编辑、删除、批量导入等操作
- 车位类型通过`PmgBatParkingPlaceTypeController`维护
- 车位信息存储于`pmg_bat_parking_place`、类型存储于`pmg_bat_parking_place_type`

### 4.2 固定车位分配、绑定、续期

- 通过`PmgBatParkingPlaceBindController`分配车位给用户/车辆，生成绑定关系
- 绑定信息存储于`pmg_bat_parking_place_bind`，支持续期、解绑等操作
- 绑定到期自动提醒、释放

### 4.3 共享车位设置、审核、收益分配

- 通过`PmgBatParkingPlaceShareController`设置车位共享，提交审核
- 审核通过后，车位可被他人预约、使用
- 收益分配规则配置，收益结算、分账
- 相关信息存储于`pmg_bat_parking_place_share`等表

### 4.4 车位占用费管理（规则、账单、支付、统计）

- 占用费规则通过`PmgBatParkingPlaceFeeController`配置，存储于`pmg_bat_parking_place_fee_rule`
- 车位被占用时自动生成账单（`PmgBatParkingPlaceFeeService.createFeeBill`）
- 账单支付、催缴、统计等通过`PmgBatParkingPlaceFeeController`和相关Service实现
- 账单存储于`pmg_bat_parking_place_fee_bill`

## 5. 关键业务流程图示

### 5.1 车位注册与类型管理

```mermaid
graph TD
A[新增/导入车位] --> B[PmgBatParkingPlaceController.save]
B --> C[写入pmg_bat_parking_place]
A --> D[选择类型]
D --> E[PmgBatParkingPlaceTypeController.list]
```

### 5.2 固定车位分配与绑定

```mermaid
graph TD
A[用户申请固定车位] --> B[PmgBatParkingPlaceBindController.bind]
B --> C[生成绑定关系]
C --> D[写入pmg_bat_parking_place_bind]
D --> E[绑定到期自动提醒/释放]
```

### 5.3 共享车位设置与收益分配

```mermaid
graph TD
A[车位主设置共享] --> B[PmgBatParkingPlaceShareController.submit]
B --> C[审核]
C --> D[通过后可预约]
D --> E[收益分配结算]
```

### 5.4 车位占用费管理

```mermaid
graph TD
A[车位被占用] --> B[PmgBatParkingPlaceFeeService.createFeeBill]
B --> C[生成占用费账单]
C --> D[用户支付/催缴]
D --> E[统计与报表]
```

## 6. 其他说明

- 车位管理支持多类型、多场景（固定、共享、临时等）
- 绑定、共享、收益、占用费等流程均有详细状态流转与异常处理
- 支持批量操作、自动提醒、收益分账等扩展能力

---
如需对某一具体方法、类或流程进一步深入源码解读，请进一步指定需求！ 