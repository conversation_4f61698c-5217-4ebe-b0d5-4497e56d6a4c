package com.yuchen.saas.device.api.entity.product;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 产品标签
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_product_tag")
@ApiModel(value="NetProductTag对象", description="产品标签")
public class NetProductTag extends BaseEntity {

    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 标签id
     */
    @ApiModelProperty(name = "tagId", value = "标签id")
    private String tagId;
    /**
     * 标签标识
     */
    @ApiModelProperty(name = "tagKey", value = "标签标识")
    private String tagKey;
    /**
     * 标签名称
     */
    @ApiModelProperty(name = "tagName", value = "标签名称")
    private String tagName;
    /**
     * 标签值
     */
    @ApiModelProperty(name = "tagValue", value = "标签值")
    private String tagValue;
    /**
     * 数据类型
     */
    @ApiModelProperty(name = "dataType", value = "数据类型")
    private Integer dataType;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;



}
