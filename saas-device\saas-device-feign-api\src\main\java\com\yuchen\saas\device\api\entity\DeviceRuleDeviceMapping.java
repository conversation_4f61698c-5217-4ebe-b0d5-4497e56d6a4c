package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: 宋少挺
 * @CreateTime: 2022/10/13 15:24
 * @Description: 设备规则关联表
 * @Version: 1.0
 */
@Data
@ApiModel(value = "设备规则关联表 数据表", description = "设备规则关联表数据表")
@TableName("device_rule_device_mapping")
public class DeviceRuleDeviceMapping implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("主键id")
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 规则类型 1 开关组规则
     */
    @ApiModelProperty(name = "type", value = "规则类型 1 开关组规则")
    private Integer type;
    /**
     * 规则id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(name = "ruleId", value = "规则id")
    private Long ruleId;
    /**
     * 设备id
     */
    @ApiModelProperty(name = "deviceId", value = "设备id")
    private String deviceId;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceName", value = "设备名称")
    private String deviceName;
    /**
     * 设备别名
     */
    @ApiModelProperty(name = "deviceNickName", value = "设备别名")
    private String deviceNickName;
    /**
     * 产品key
     */
    @ApiModelProperty(name = "productKey", value = "产品key")
    private String productKey;

}