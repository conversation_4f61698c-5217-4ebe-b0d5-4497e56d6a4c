package com.yuchen.saas.device.api.dto.assets;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "SaasAssetsAddressDeleteDTO", description = "SaasAssetsAddressDeleteDTO")
public class SaasAssetsAddressDeleteDTO {

    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "ids", value = "id集合")
    private List<Long> ids;

}
