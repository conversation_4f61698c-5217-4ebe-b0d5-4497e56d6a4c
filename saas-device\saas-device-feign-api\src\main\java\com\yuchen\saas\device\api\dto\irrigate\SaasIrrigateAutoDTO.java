package com.yuchen.saas.device.api.dto.irrigate;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class SaasIrrigateAutoDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID")
    private Long id;
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 浇灌名称
     */
    @ApiModelProperty(name = "irrigateName", value = "浇灌名称")
    private String irrigateName;
    /**
     * 触发方式(1:定时触发 2:设备触发)
     */
    @ApiModelProperty(name = "triggerMode", value = "触发方式(1:定时触发 2:设备触发)")
    private Integer triggerMode;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remark", value = "备注信息")
    private String remark;
    /**
     * 浇灌开始日期
     */
    @ApiModelProperty(name = "irrigateStartDate", value = "浇灌开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date irrigateStartDate;
    /**
     * 浇灌结束日期
     */
    @ApiModelProperty(name = "irrigateEndDate", value = "浇灌结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date irrigateEndDate;
    /**
     * 浇灌点
     */
    @ApiModelProperty(name = "irrigatePointList", value = "浇灌点")
    private List<IrrigatePoint> irrigatePointList;
    /**
     * 浇灌规则选择周期类型（1：天，2：周，3：月，4：年，5：自定义）
     */
    @ApiModelProperty(name = "irrigateRuleCycle", value = "浇灌规则选择周期类型（1：天，2：周，3：月，4：年，5：自定义）")
    private Integer irrigateRuleCycle;
    /**
     * 浇灌规则周期值
     */
    @ApiModelProperty(name = "irrigateRuleCycleValue", value = "浇灌规则周期值")
    private String irrigateRuleCycleValue;
    /**
     * 浇灌时间列表(逗号隔开，格式如08:00,18:00)
     */
    @ApiModelProperty(name = "irrigateTimes", value = "浇灌时间列表(逗号隔开，格式如08:00,18:00)")
    private String irrigateTimes;
    /**
     * 产品key
     */
    @ApiModelProperty(name = "productKey", value = "产品key")
    private String productKey;
    /**
     * 参数名称
     */
    @ApiModelProperty(name = "paramName", value = "参数名称")
    private String paramName;
    /**
     * 参数值
     */
    @ApiModelProperty(name = "paramValue", value = "参数值")
    private Float paramValue;

    @ApiModelProperty(name = "condition", value = "结束条件(> = <)")
    private String sCondition;

    /**
     * 产品key
     */
    @ApiModelProperty(name = "productKeySecond", value = "产品key")
    private String productKeySecond;
    /**
     * 参数名称
     */
    @ApiModelProperty(name = "paramNameSecond", value = "参数名称")
    private String paramNameSecond;
    /**
     * 参数值
     */
    @ApiModelProperty(name = "paramValueSecond", value = "参数值")
    private Float paramValueSecond;

    @ApiModelProperty(name = "conditionSecond", value = "结束条件(> = <)")
    private String sConditionSecond;
    /**
     * 结束条件(1:浇灌时长 2:土壤温湿度)
     */
    @ApiModelProperty(name = "endCondition", value = "结束条件(1:浇灌时长 2:土壤温湿度)")
    private Integer endCondition;

    /**
     * 浇灌时长(分钟)
     */
    @ApiModelProperty(name = "irrigateDuration", value = "浇灌时长(分钟)")
    private Integer irrigateDuration;
    /**
     * 土壤温湿度
     */
    @ApiModelProperty(name = "humidity", value = "土壤温湿度")
    private Float humidity;

    @Data
    public static class IrrigatePoint {

        /**
         * 浇灌点ID
         */
        @ApiModelProperty(name = "irrigatePointId", value = "浇灌点ID")
        private Long irrigatePointId;
        /**
         * 浇灌点名称
         */
        @ApiModelProperty(name = "irrigatePointName", value = "浇灌点名称")
        private String irrigatePointName;
        /**
         * 绑定阀门设备ID
         */
        @ApiModelProperty(name = "valveDeviceId", value = "绑定阀门设备ID")
        private Long valveDeviceId;
        /**
         * 绑定阀门设备编码
         */
        @ApiModelProperty(name = "valveDeviceName", value = "绑定阀门设备编码")
        private String valveDeviceName;
        /**
         * 绑定阀门设备名称
         */
        @ApiModelProperty(name = "valveDeviceAlias", value = "绑定阀门设备名称")
        private String valveDeviceAlias;
        /**
         * 绑定传感设备ID(多个，逗号隔开)
         */
        @ApiModelProperty(name = "sensorDeviceId", value = "绑定传感设备ID")
        private Long sensorDeviceId;
        /**
         * 绑定传感设备编码
         */
        @ApiModelProperty(name = "irrigateDeviceName", value = "绑定传感设备编码")
        private String sensorDeviceName;
        /**
         * 绑定传感设备名称
         */
        @ApiModelProperty(name = "irrigateDeviceName", value = "绑定传感设备名称")
        private String sensorDeviceAlias;

    }





}





























































































































































































































































































































































