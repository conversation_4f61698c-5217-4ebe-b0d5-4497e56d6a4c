package com.yuchen.saas.device.api.entity.assets;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 资产分类信息表
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_assets_category")
@ApiModel(value="SaasAssetsCategory对象", description="资产分类信息表")
public class SaasAssetsCategory extends BaseEntity {

    /**
     * 资产分类编码
     */
    @ApiModelProperty(name = "categoryCode", value = "资产分类编码")
    private String categoryCode;
    /**
     * 资产分类名称
     */
    @ApiModelProperty(name = "categoryName", value = "资产分类名称")
    private String categoryName;
    /**
     * 父级id
     */
    @ApiModelProperty(name = "parentId", value = "父级id")
    private Long parentId;
    /**
     * 预计使用期限（月）
     */
    @ApiModelProperty(name = "usagePeriod", value = "预计使用期限（月）")
    private Integer usagePeriod;
    /**
     * 计量单位
     */
    @ApiModelProperty(name = "measureUnit", value = "计量单位")
    private String measureUnit;
    /**
     * 排序
     */
    @ApiModelProperty(name = "sort", value = "排序")
    private Integer sort;
    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    private Long customerId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;



}
