package com.yuchen.saas.consumer.service.service;

import com.yuchen.saas.consumer.api.entity.*;

/**
 * @ClassName WeChatTempMessageService
 * @Description 微信模版消息接口
 * <AUTHOR>
 * @Date 2022/3/1 15:35
 */
public interface WeChatTempMessageService {

    /**
     * 发送-审核结果-模版消息
     * @param tempMessageReviewResultDTO
     */
    void sendReviewMessage(TempMessageReviewResultDTO tempMessageReviewResultDTO);

    /**
     * 发送-停车月卡到期提醒-模版消息
     * @param tempMessageMonthCardExpiredDTO
     */
    void sendMonthCardExpiredMessage(TempMessageMonthCardExpiredDTO tempMessageMonthCardExpiredDTO);

    /**
     * 发送-支付成功-模版消息
     * @param tempMessagePaySuccessDTO
     */
    void sendPaySuccessMessage(TempMessagePaySuccessDTO tempMessagePaySuccessDTO);

    /**
     * 发送-发票结果通知-模版消息
     * @param tempMessageInvoiceNoticeDTO
     */
    void sendInvoiceNoticeMessage(TempMessageInvoiceNoticeDTO tempMessageInvoiceNoticeDTO);

    /**
     * 发送-退款结果通知-模版消息
     * @param tempMessageRefundNoticeDTO
     */
    void sendRefundNoticeMessage(TempMessageRefundNoticeDTO tempMessageRefundNoticeDTO);
}
