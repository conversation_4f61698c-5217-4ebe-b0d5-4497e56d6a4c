package com.yuchen.saas.auth.granter;


import com.yuchen.saas.manage.user.api.entity.SaasWechatUser;
import com.yuchen.saas.manage.user.api.entity.User;
import com.yuchen.saas.manage.user.api.entity.UserDetail;
import com.yuchen.saas.manage.user.api.entity.UserInfo;
import com.yuchen.saas.manage.user.api.enums.UserEnum;
import com.yuchen.saas.manage.user.api.feign.IUserClient;
import io.jsonwebtoken.Claims;
import org.nest.springwrap.core.jwt.JwtUtil;
import org.nest.springwrap.core.launch.constant.TokenConstant;
import org.nest.springwrap.core.log.exception.ServiceException;
import org.nest.springwrap.core.mp.intercept.IgnoreTenant;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.nest.springwrap.core.secure.utils.AuthUtil;
import org.nest.springwrap.core.tool.api.ResultCode;
import org.nest.springwrap.core.tool.utils.Func;
import org.nest.springwrap.core.tool.utils.StringUtil;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 切换token
 */
public class HandoverTokenGranter extends AbstractTokenGranter {
    private static final String GRANT_TYPE = "toggle";

    private UserDetailsService userDetailsService;

    private NestRedis nestRedis;

    private IUserClient userClient;




    public HandoverTokenGranter(AuthorizationServerTokenServices tokenServices,
                                ClientDetailsService clientDetailsService,
                                OAuth2RequestFactory requestFactory,
                                UserDetailsService userDetailsService,
                                NestRedis nestRedis,
                                IUserClient userClient) {
        super(tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
        this.userDetailsService = userDetailsService;
        this.nestRedis = nestRedis;
        this.userClient = userClient;
    }


    @Override
    @IgnoreTenant
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client,
                                                           TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<String, String>(tokenRequest.getRequestParameters());
        String phoneNumber = parameters.get("phone");

        //旧的需要清除掉的token
        //todo token的key加应用id,不同的应用token隔离开
        String auth = parameters.get("token");
        if(Func.isEmpty(auth)){
            throw new ServiceException(ResultCode.UN_AUTHORIZED);
        }
        String token = JwtUtil.getToken(auth);
        Claims claims = JwtUtil.parseJWT(token);
        String tenantId ="";
        String userId = "";
        String accessToken = "";
        if (JwtUtil.getJwtProperties().getState()) {
             tenantId = Func.toStr(claims.get(TokenConstant.TENANT_ID));
             userId = Func.toStr(claims.get(TokenConstant.USER_ID));
             accessToken = JwtUtil.getAccessToken(tenantId, userId, token);
            if (!token.equalsIgnoreCase(accessToken)) {
                throw new ServiceException(ResultCode.UN_AUTHORIZED);
            }
            //以旧token的账号信息为准
            phoneNumber=Func.toStr(claims.get(TokenConstant.USER_NAME));
            if (StringUtil.isNotBlank(token)) {
                JwtUtil.removeAccessToken(tenantId, userId, token);
            }
        }
        UserDetails user = userDetailsService.loadUserByUsername(phoneNumber);
        AbstractAuthenticationToken userAuth = new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());
        userAuth.setDetails(parameters);
        OAuth2Request oAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        if (StringUtil.isNotBlank(token)) {
            JwtUtil.removeAccessToken(tenantId, userId, token);
        }
        return new OAuth2Authentication(oAuth2Request, userAuth);
    }
}
