package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SaasHandleAutoIrrigationDTO {

    /**
     * 产品Key
     */
    @ApiModelProperty(name = "productKey", value = "产品Key")
    private String productKey;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;
    /**
     * 温湿度
     */
    @ApiModelProperty(name = "humidity", value = "温湿度")
    private Float humidity;

    /**
     * 温度
     */
    @ApiModelProperty(name = "temperature", value = "温度")
    private Float temperature;

}
