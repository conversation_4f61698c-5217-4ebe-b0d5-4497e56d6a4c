<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.net.NetAlarmStaffDeployMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.NetAlarmStaffDeploy">
        <id column="id" property="id" />
        <result column="status" property="status" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="project_id" property="projectId" />
        <result column="is_staff_deployment" property="isStaffDeployment" />
        <result column="is_auto_deployment" property="isAutoDeployment" />
        <result column="auto_user_id" property="autoUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, status, create_user, create_dept, create_time, update_user, update_time, is_deleted, project_id, is_staff_deployment, is_auto_deployment, auto_user_id
    </sql>

</mapper>
