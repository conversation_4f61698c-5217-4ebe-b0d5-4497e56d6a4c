package com.thinkunion.park.service.common.utils;

import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;

public class AmtUtil {

    /**
     * 元转分
     * @param yuan
     * @return
     */
    public static long yuanToFen(BigDecimal yuan) {
        if (yuan == null) {
            return 0L;
        }
        return NumberUtil.mul(yuan, new BigDecimal(100)).longValue();
    }

    /**
     * 分转元
     * @param fen
     * @return
     */
    public static BigDecimal fenToYuan(long fen) {
        return NumberUtil.round(NumberUtil.div(new BigDecimal(fen), new BigDecimal(100)), 2);
    }


    /**
     * 元转分
     * @param yuan
     * @return
     */
    public static Long yuanToFenObj(BigDecimal yuan) {
        if (yuan == null) {
            return null;
        }
        return NumberUtil.mul(yuan, new BigDecimal(100)).longValue();
    }

    /**
     * 分转元
     * @param fen
     * @return
     */
    public static BigDecimal fenToYuanObj(Long fen) {
        if (fen == null) {
            return null;
        }
        return NumberUtil.round(NumberUtil.div(new BigDecimal(fen), new BigDecimal(100)), 2);
    }


}
