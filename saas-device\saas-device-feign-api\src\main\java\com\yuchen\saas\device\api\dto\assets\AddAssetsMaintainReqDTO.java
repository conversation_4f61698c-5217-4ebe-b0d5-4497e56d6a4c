package com.yuchen.saas.device.api.dto.assets;

import com.yuchen.saas.device.api.entity.assets.SaasAssetsMaintain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2024/4/19 15:39
 */
@Data
public class AddAssetsMaintainReqDTO {

    /**
     * 维修数据
     */
    private SaasAssetsMaintain saasAssetsMaintain;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "equipmentCode", value = "设备编码")
    @NotBlank(message = "设备编码不能为空")
    private String equipmentCode;

    /**
     * 设备资产维修明细,跟后台存一样的数组数据，字段也一样，方便统一查询
     */
    @NotBlank(message = "设备数据不能为空")
    private String assetsData;

}
