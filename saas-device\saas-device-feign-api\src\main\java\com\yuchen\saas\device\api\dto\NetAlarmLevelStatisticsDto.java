package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/9/24 10:01
 * @Description TODO
 */
@Data
public class NetAlarmLevelStatisticsDto implements Serializable {
    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerManageId", value = "所属客户")
    private Long customerManageId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 开始时间
     */
    @ApiModelProperty(name = "beginTime", value = "开始时间")
    private Date beginTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(name = "endTime", value = "结束时间")
    private Date endTime;
}
