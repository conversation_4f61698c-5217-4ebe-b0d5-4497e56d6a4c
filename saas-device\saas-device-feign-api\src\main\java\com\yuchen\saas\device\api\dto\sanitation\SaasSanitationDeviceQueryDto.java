package com.yuchen.saas.device.api.dto.sanitation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nest.springwrap.core.mp.support.Query;

/**
 * <AUTHOR>
 * @Date 2024/7/9 16:43
 * @Description TODO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaasSanitationDeviceQueryDto extends Query {
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;
    /**
     * 所属产品
     */
    @ApiModelProperty(name = "productKey", value = "所属产品")
    private String productKey;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 设备类型ID
     */
    @ApiModelProperty(name = "deviceTypeId", value = "设备类型ID")
    private Long deviceTypeId;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceLocation", value = "设备位置")
    private String deviceLocation;
    /**
     * 安装空间ID
     */
    @ApiModelProperty(name = "installSpaceId", value = "安装空间ID")
    private Long installSpaceId;
    /**
     * 最后连接开始时间
     */
    @ApiModelProperty(name = "lastConnectStartTime", value = "最后连接开始时间")
    private String lastConnectStartTime;
    /**
     * 最后连接结束时间
     */
    @ApiModelProperty(name = "lastConnectEndTime", value = "最后连接结束时间")
    private String lastConnectEndTime;
    /**
     * 最后断开开始时间
     */
    @ApiModelProperty(name = "lastDisconnectStartTime", value = "最后断开开始时间")
    private String lastDisconnectStartTime;
    /**
     * 最后断开结束时间
     */
    @ApiModelProperty(name = "lastDisconnectEndTime", value = "最后断开结束时间")
    private String lastDisconnectEndTime;
}
