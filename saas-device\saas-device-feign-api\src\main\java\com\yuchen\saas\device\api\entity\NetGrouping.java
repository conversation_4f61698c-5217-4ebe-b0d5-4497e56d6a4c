package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 物联分组表
 */
@Data
@TableName("net_grouping")
public class NetGrouping {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属客户，运营端传 0
     */
    @NotNull(message = "所属客户不能为空")
    private Long customerManageId;

    /**
     * 父节点pid，空值请传 0
     */
    @NotNull(message = "父节点pid不能为空")
    private Long pid;

    /**
     * 分组名称
     */
    @NotNull(message = "分组名称不能为空")
    private String groupingName;

    /**
     * 分组类型：1-运营端设备分组，2-客户端设备分组
     */
    @NotNull(message = "分组类型不能为空")
    private Integer groupingType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
