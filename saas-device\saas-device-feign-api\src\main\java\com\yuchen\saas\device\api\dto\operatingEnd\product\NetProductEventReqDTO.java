package com.yuchen.saas.device.api.dto.operatingEnd.product;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.util.ArrayList;
import java.util.List;

@Data
public class NetProductEventReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;

    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    @NotNull(message = "产品id不能为空")
    private Long productId;
    /**
     * 事件id
     */
    @ApiModelProperty(name = "eventId", value = "事件id")
    @NotBlank(message = "事件id不能为空")
    private String eventId;
    /**
     * 功能url
     */
    @ApiModelProperty(name = "url", value = "功能url")
    private String url;
    /**
     * 事件标识
     */
    @ApiModelProperty(name = "eventKey", value = "事件标识")
    @NotBlank(message = "事件标识不能为空")
    private String eventKey;
    /**
     * 事件名称
     */
    @ApiModelProperty(name = "eventName", value = "事件名称")
    @NotBlank(message = "事件名称不能为空")
    private String eventName;
    /**
     * 事件类型
     */
    @ApiModelProperty(name = "eventType", value = "事件类型")
    @NotNull(message = "事件类型不能为空")
    private Integer eventType;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remark", value = "备注信息")
    private String remark;

    /**
     * 参数列表
     */
    private List<Param> paramList = new ArrayList<>();

    @Data
    public static class Param {
        /**
         * 参数标识
         */
        private String paramKey;

        /**
         * 参数名称
         */
        private String paramName;

        /**
         * NetDataTypeEnum
         * 属性类型
         * 1：int(整数型)
         * 2：long(长整数型)
         * 3：float(单精度浮点型)
         * 4：double(双精度浮点型)
         * 5：text(字符串)
         * 6：bool(布尔型)
         * 7：time(时间类型)
         */
        private Integer paramType;
    }

}
