package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.dto.NetAlarmQryDto;
import com.yuchen.saas.device.api.entity.NetAlarm;
import com.yuchen.saas.device.api.vo.NetAlarmPageVo;
import com.yuchen.saas.device.api.vo.NetAlarmVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface NetAlarmMapper extends BaseMapper<NetAlarm> {

//    @Select("SELECT a.*, IF(a.device_name = 0, '全部', nat.device_alias) AS device_alias,np.product_name from net_alarm a" +
//            " left join net_operate_device nat on a.device_name = nat.device_name" +
//            " left join net_product np on a.product_key = np.product_key" +
//            " ${ew.customSqlSegment}")
    @Select("SELECT *from net_alarm a ${ew.customSqlSegment}")
    Page<NetAlarmPageVo> selectAlarmPage(Page<NetAlarmPageVo> page, @Param(Constants.WRAPPER) QueryWrapper<String> wrapper);


    @Select("SELECT a.*,p.product_key,od.device_name from net_alarm a" +
            " left join net_product p on a.product_id = p.id" +
            " left join net_alarm_device ad on a.id = ad.alarm_id" +
            " left join net_client_device cd on ad.client_device_id = cd.id" +
            " left join net_operate_device od on cd.operate_device_id = od.id" +
            " where p.product_key=#{productKey} and a.trigger_mode=#{triggerMode} and a.`status`=1 and a.is_deleted=0")
    List<NetAlarmVo> qryAlarmVoByProductKey(@Param("productKey") String productKey, @Param("triggerMode") Integer triggerMode);

    @Select("SELECT * from net_alarm where product_key=#{productKey} and trigger_mode=#{triggerMode} and `status`=1 and is_deleted=0")
    List<NetAlarmVo> qryAlarmVoByProductKeyAndDeviceName(@Param("productKey") String productKey,@Param("triggerMode") Integer triggerMode);

    @Select("SELECT * from net_alarm where scene_id=#{sceneId} and `status`=1 and is_deleted=0")
    List<NetAlarmVo> qryAlarmVoBySceneLinkage(@Param("sceneId") Long sceneId);

    @Select("SELECT * from net_alarm where service_id=#{serviceId} and `status`=1 and is_deleted=0")
    List<NetAlarmVo> qryAlarmVoByService(@Param("serviceId") Long serviceId);

    List<NetAlarmVo> qryAlarmVo(NetAlarmQryDto qryDto);

}
