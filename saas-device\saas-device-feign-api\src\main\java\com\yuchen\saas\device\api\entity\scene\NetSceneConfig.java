package com.yuchen.saas.device.api.entity.scene;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_scene_config")
@ApiModel(value="NetSceneConfig对象", description="场景联动设置表")
public class NetSceneConfig extends BaseEntity {

    /**
     * 所属客户
     */
    private Long customerId;

    /**
     * 所属项目id
     */
    private Long projectId;

    /**
     * 业务服务id
     */
    @ApiModelProperty(name = "businessId", value = "业务服务id")
    private Long businessId;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 场景类型，1-设备触发，2-定时触发，3-手动触发
     */
    private Integer triggerMode;

    /**
     * xxl定时任务cron表达式（定时触发有值）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String cron;

    /**
     * xxl任务id
     */
    private Integer jobId;

    /**
     * 触发条件json
     */
    private String triggerConditionJson;

    /**
     * 执行动作json
     */
    private String executeActionJson;

    /**
     * 执行条件json
     */
    private String executeConditionJson;

    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;


}
