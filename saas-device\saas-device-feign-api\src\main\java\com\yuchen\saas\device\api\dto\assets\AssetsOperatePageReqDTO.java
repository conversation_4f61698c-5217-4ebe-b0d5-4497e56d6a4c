package com.yuchen.saas.device.api.dto.assets;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/4/16 14:34
 */
@Data
public class AssetsOperatePageReqDTO extends Query {

    /**
     * 查询项
     */
    @ApiModelProperty(name = "fieldsDTOList", value = "查询项")
    private List<AssetDataFieldsDTO> fieldsDTOList;


}
