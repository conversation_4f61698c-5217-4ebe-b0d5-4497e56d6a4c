package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("net_alarm_record")
@ApiModel(value="NetAlarmRecord对象", description="告警记录")
public class NetAlarmRecord {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerManageId", value = "所属客户")
    private Long customerManageId;
    /**
     * 所属客户名称
     */
    @ApiModelProperty(name = "customerManageName", value = "所属客户名称")
    private String customerManageName;

    /**
     * 告警id
     */
    @ApiModelProperty(name = "alarmId", value = "告警id")
    private Long alarmId;

    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 服务应用id
     */
    @ApiModelProperty(name = "serviceId", value = "服务应用id")
    private Long serviceId;

    /**
     * 告警名称
     */
    @ApiModelProperty(name = "alarmName", value = "告警名称")
    private String alarmName;

    /**
     * 告警时间
     */
    @ApiModelProperty(name = "alarmTime", value = "告警时间")
    private Date alarmTime;
    /**
     * 告警来源; 1-设备触发，2-场景联动，3-业务规则，4-第三方
     */
    @ApiModelProperty(name = "alarmSource", value = "告警来源; 1-设备触发，2-场景联动，3-业务规则，4-第三方")
    private Integer alarmSource;

    /**
     * 告警等级
     */
    @ApiModelProperty(name = "alarmLevel", value = "告警等级")
    private Integer alarmLevel;

    /**
     * 告警级别名称
     */
    @ApiModelProperty(name = "alarmLevelName", value = "告警级别名称")
    private String alarmLevelName;

    /**
     * 状态; 1-待处理，2-已关闭，3-已派工
     */
    @ApiModelProperty(name = "status", value = "状态; 1-待处理，2-已关闭，3-已处理")
    private Integer status;

    /**
     * 告警设备标识
     */
    @ApiModelProperty(name = "deviceName", value = "告警设备标识")
    private String deviceName;
    /**
     * 产品名称
     */
    @ApiModelProperty(name = "productName", value = "产品名称")
    @TableField(exist = false)
    private String productName;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    @TableField(exist = false)
    private String deviceAlias;

//    /**
//     * 告警设备标识
//     */
//    private String deviceKey;

    /**
     * 告警位置
     */
    @ApiModelProperty(name = "spatialPosition", value = "告警位置")
    private String spatialPosition;

    /**
     * 告警内容
     */
    @ApiModelProperty(name = "alarmContent", value = "告警内容")
    private String alarmContent;

    /**
     * 设备地址
     */
    @ApiModelProperty(name = "location", value = "设备地址")
    private String location;

    /**
     * 告警照片/视频
     */
    @ApiModelProperty(name = "path", value = "告警照片/视频")
    private String path;

    /**
     * 管理区ids（逗号拼接）
     */
    @ApiModelProperty(name = "manageAreaIds", value = "管理区ids（逗号拼接）")
    private String manageAreaIds;
    /**
     * 管理区名称
     */
    @ApiModelProperty(name = "manageAreaNames", value = "管理区名称")
    private String manageAreaNames;

    /**
     * 关闭时间
     */
    @ApiModelProperty(name = "closeTime", value = "关闭时间")
    private Date closeTime;

    /**
     * 关闭人员
     */
    @ApiModelProperty(name = "closeUserId", value = "关闭人员")
    private Long closeUserId;
    /**
     * 关闭原因
     */
    @ApiModelProperty(name = "closeReason", value = "关闭原因")
    private String closeReason;
    /**
     * 关闭备注
     */
    @ApiModelProperty(name = "closeRemark", value = "关闭备注")
    private String closeRemark;

    /**
     * 关闭人员名称
     */
    @ApiModelProperty(name = "closeUserName", value = "关闭人员名称")
    private String closeUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateTime", value = "更新时间")
    private Date updateTime;

}
