package com.yuchen.saas.device.api.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Data
public class NetIdStatusDto {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 状态值：0-停用，1-启用
     */
    @NotNull(message = "状态值不能为空")
    @Range(max = 1, min = 0, message = "请输入0或1")
    private Integer status;


}
