package com.yuchen.saas.device.api.dto.scene;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * 执行条件对象
 */
@Data
public class SceneExecuteCondition {

    /**
     * 执行条件类型：property-属性，event-事件，timeRange-时间范围
     */
    @NotNull(message = "执行条件类型不能为空")
    @Pattern(regexp = "property|event|timeRange", message = "执行条件类型：property，event，timeRange")
    private String uri;

    /**
     * 产品key
     */
    @NotNull(message = "产品不能为空", groups = {Property.class,Event.class})
    private String productKey;

    /**
     * 设备名称
     */
    @NotNull(message = "设备名称不能为空", groups = {Property.class,Event.class})
    private String deviceName;

    /**
     * 属性名称
     */
    @NotNull(message = "属性名称不能为空", groups = {Property.class,Event.class})
    private String propertyName;

    /**
     * 事件
     */
    @NotNull(message = "事件不能为空", groups = {Event.class})
    private String eventCode;

    /**
     * NetDataTypeEnum
     * 参数类型
     * 1：int(整数型)
     * 2：long(长整数型)
     * 3：float(单精度浮点型)
     * 4：double(双精度浮点型)
     * 5：text(字符串)
     * 6：bool(布尔型)
     * 7：time(时间类型)
     */
    @NotNull(message = "参数类型不能为空", groups = {Property.class,Event.class})
    private Integer propMode;

    /**
     * NetCompareTypeEnum
     * 比较符
     * 1：等于
     * 2：不等于
     * 3：大于
     * 4：大于等于
     * 5：小于
     * 6：小于等于
     */
    @NotNull(message = "比较符不能为空", groups = {Property.class,Event.class})
    private Integer compareType;

    /**
     * 比较值
     */
    @NotNull(message = "比较值不能为空", groups = {Property.class,Event.class})
    private String compareValue;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空", groups = {Time.class})
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空", groups = {Time.class})
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    public interface Property {
    }

    public interface Event {
    }

    public interface Time {
    }

}
