package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 申请来源（0后台添加、1用户申请）
 * <AUTHOR>
 */
@AllArgsConstructor
public enum ApplySourceEnum {


    BACKGROUND_ADD(0, "后台添加"),
    USER_APPLICATION(1, "用户申请"),
    IMPORT(2, "后台导入"),
   ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        ApplySourceEnum[] enumArray = ApplySourceEnum.values();
        for (ApplySourceEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
