package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@TableName("net_electronic_fence")
public class NetElectronicFence {
    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 所属项目id
     */
    @NotNull(message = "所属项目不能为空")
    private Long parkId;
    /**
     * 电子围栏名称
     */
    @NotNull(message = "电子围栏名称不能为空")
    private String fenceName;
    /**
     * 绑定设备
     */
    @NotNull(message = "绑定设备不能为空")
    private String deviceName;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 空间id
     */
    private Long spaceId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
}
