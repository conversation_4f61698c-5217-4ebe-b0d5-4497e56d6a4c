<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.irrigate.SaasIrrigateMapMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.irrigate.SaasIrrigateMap">
        <id column="id" property="id" />
        <result column="irrigate_point_id" property="irrigatePointId" />
        <result column="irrigate_point_name" property="irrigatePointName" />
        <result column="irrigate_area_id" property="irrigateAreaId" />
        <result column="remarks" property="remarks" />
        <result column="x" property="x" />
        <result column="y" property="y" />
        <result column="address" property="address" />
        <result column="park_point_location_id" property="parkPointLocationId" />
        <result column="project_id" property="projectId" />
        <result column="status" property="status" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, irrigate_point_id, irrigate_point_name, irrigate_area_id, remarks, x, y, address, park_point_location_id, project_id, status, create_user, create_dept, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
