package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("net_device_connect_data")
public class NetDeviceConnectData {


    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设备产品key
     */
    private String productKey;

    /**
     * 设备key
     */
    private String deviceName;

    /**
     * 是否在线：1-是，0-否
     */
    private Integer isOnline;

    /**
     * 创建时间
     */
    private Date createTime;



}
