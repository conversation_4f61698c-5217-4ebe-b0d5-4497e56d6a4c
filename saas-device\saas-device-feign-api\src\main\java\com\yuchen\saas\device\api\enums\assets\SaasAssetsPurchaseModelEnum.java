package com.yuchen.saas.device.api.enums.assets;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/3/19 19:16
 */
@Getter
@AllArgsConstructor
public enum SaasAssetsPurchaseModelEnum {

    PURCHASE("1", "采购"),
    IN_PURCHASE("2", "内购"),
    LEASE("3", "租赁"),
    SELF_BUILD("4", "自建"),
    INVENTORY_PROFIT("5", "盘盈"),
    ACCEPT_INVESTMENT("6", "接受投资"),
    ACCEPT_DONATIONS("7", "接受捐赠"),
    OTHER("8", "其他"),
    ;

    private final String code;
    private final String desc;

}
