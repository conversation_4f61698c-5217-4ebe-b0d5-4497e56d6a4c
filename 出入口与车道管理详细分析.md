# 出入口与车道管理详细分析

## 1. 概述

出入口与车道管理是智慧停车系统的重要组成部分，负责停车场物理结构的数字化管理，包括出入口信息注册与维护、车道信息管理、设备绑定等核心功能。

## 2. 出入口管理

### 2.1 出入口信息注册与维护

#### 2.1.1 功能描述
- **出入口注册**：新建停车场出入口基础信息
- **出入口维护**：修改出入口名称、类型等信息
- **出入口删除**：删除不再使用的出入口

#### 2.1.2 核心实体类
```java
@TableName("pmg_bat_park_parking_entrance")
public class PmgBatParkParkingEntrance extends TenantEntity {
    // 基础信息
    private String parkId;          // 园区ID
    private Long parkingId;         // 停车场ID
    private String name;            // 出入口名称
    private Integer type;           // 出入口类型：0-进口，1-出口
    
    // 扩展属性
    private String description;     // 出入口描述
    private Integer status;         // 状态：0-禁用，1-启用
    private String remark;          // 备注信息
}
```

#### 2.1.3 主要接口
- `POST /parking/PmgBatParkParking/saveEntrance` - 新增出入口
- `PUT /parking/PmgBatParkParking/updateEntrance` - 修改出入口
- `DELETE /parking/PmgBatParkParking/delEntrance` - 删除出入口
- `GET /parking/PmgBatParkParking/getEntrance` - 获取出入口详情
- `GET /parking/PmgBatParkParking/getEntranceList` - 获取出入口列表

### 2.2 出入口类型管理

#### 2.2.1 类型定义
- **进口（0）**：车辆进入停车场的通道
- **出口（1）**：车辆离开停车场的通道
- **双向口**：既可进入也可离开的通道（通过配置实现）

#### 2.2.2 类型特性
```java
public enum EntranceType {
    ENTRANCE(0, "进口"),
    EXIT(1, "出口");
    
    private final Integer code;
    private final String description;
}
```

### 2.3 出入口与停车场关联

#### 2.3.1 关联关系
- **一对多关系**：一个停车场可以有多个出入口
- **层级关系**：出入口属于特定停车场，继承停车场的园区信息
- **约束条件**：删除停车场时需要先删除所有出入口

#### 2.3.2 关联实现
```java
public Boolean saveEntrance(PmgBatParkParkingEntrance entrance) {
    // 验证停车场是否存在
    PmgBatParkParking parking = parkingService.getById(entrance.getParkingId());
    if (parking == null) {
        throw new ServiceException("停车场不存在");
    }
    
    // 继承园区信息
    entrance.setParkId(parking.getParkId());
    
    return entranceService.save(entrance);
}
```

## 3. 车道管理

### 3.1 车道信息注册与维护

#### 3.1.1 功能描述
- **车道注册**：在出入口下创建车道信息
- **车道维护**：修改车道名称、类型、设备配置等
- **车道删除**：删除不再使用的车道（需检查设备绑定）

#### 3.1.2 核心实体类
```java
@TableName("pmg_bat_park_parking_roadway")
public class PmgBatParkParkingRoadway extends TenantEntity {
    // 基础信息
    private String parkId;          // 园区ID
    private Long entranceId;        // 出入口ID
    private String rowName;         // 车道名称
    private Integer type;           // 车道类型：0-进道，1-出道
    
    // 设备配置
    private Boolean ledEnable;      // LED显示屏启用
    private Boolean voiceEnable;    // 语音播报启用
    private Boolean gateEnable;     // 道闸启用
    private Boolean cameraEnable;   // 摄像头启用
    
    // 业务配置
    private Integer menuType;       // 菜单类型
    private String remark;          // 备注信息
}
```

#### 3.1.3 主要接口
- `POST /parking/PmgBatParkParking/saveRoadway` - 新增车道
- `PUT /parking/PmgBatParkParking/updateRoadway` - 修改车道
- `DELETE /parking/PmgBatParkParking/delRoadway` - 删除车道
- `GET /parking/PmgBatParkParking/getRoadway` - 获取车道详情
- `GET /parking/PmgBatParkParking/getRoadwayList` - 获取车道列表

### 3.2 车道类型管理

#### 3.2.1 类型定义
- **进道（0）**：车辆进入停车场的车道
- **出道（1）**：车辆离开停车场的车道

#### 3.2.2 类型约束
```java
public Boolean saveRoadway(PmgBatParkParkingRoadway roadway) {
    // 获取出入口信息
    PmgBatParkParkingEntrance entrance = entranceService.getById(roadway.getEntranceId());
    
    // 车道类型必须与出入口类型一致
    if (!roadway.getType().equals(entrance.getType())) {
        throw new ServiceException("车道类型必须与出入口类型一致");
    }
    
    return roadwayService.save(roadway);
}
```

### 3.3 车道与出入口关联

#### 3.3.1 关联关系
- **一对多关系**：一个出入口可以有多个车道
- **类型继承**：车道类型继承出入口类型
- **级联删除**：删除出入口时级联删除所有车道

#### 3.3.2 关联管理
```java
public Boolean deleteByEntranceId(Long entranceId) {
    // 查询出入口下的所有车道
    List<PmgBatParkParkingRoadway> roadways = roadwayService.list(
        Wrappers.<PmgBatParkParkingRoadway>lambdaQuery()
            .eq(PmgBatParkParkingRoadway::getEntranceId, entranceId)
    );
    
    // 检查车道是否有绑定设备
    for (PmgBatParkParkingRoadway roadway : roadways) {
        if (hasBindDevice(roadway.getId())) {
            throw new ServiceException("车道存在绑定设备，无法删除");
        }
    }
    
    // 删除所有车道
    return roadwayService.removeByIds(
        roadways.stream().map(PmgBatParkParkingRoadway::getId).collect(Collectors.toList())
    );
}
```

## 4. 设备绑定与解绑

### 4.1 设备绑定管理

#### 4.1.1 功能描述
- **设备绑定**：将物理设备与车道进行关联
- **设备解绑**：解除设备与车道的关联关系
- **设备状态监控**：实时监控设备在线状态和工作状态

#### 4.1.2 核心实体类
```java
@TableName("pmg_bat_park_parking_connect_device")
public class PmgBatParkParkingConnectDevice extends TenantEntity {
    // 设备信息
    private Long productId;         // 产品ID
    private String productKey;      // 产品Key
    private String deviceName;      // 设备名称
    private String deviceAlias;     // 设备别名
    
    // 关联信息
    private Long roadwayId;         // 车道ID
    private Long entranceId;        // 出入口ID
    private Long parkingId;         // 停车场ID
    
    // 设备类型
    private Integer barrierGateType; // 道闸类型：1-抓拍一体机，2-普通道闸
    private Integer deviceType;      // 设备类型
    
    // 状态信息
    private Integer status;          // 绑定状态
    private String remark;           // 备注
}
```

#### 4.1.3 设备类型
- **道闸设备**：控制车辆通行的闸机设备
- **摄像头设备**：车牌识别和监控设备
- **LED显示屏**：信息显示设备
- **语音播报设备**：语音提示设备
- **地感线圈**：车辆检测设备

### 4.2 设备绑定流程

#### 4.2.1 绑定验证
```java
public R bundingDevice(List<PmgBatParkParkingConnectDevice> devices) {
    for (PmgBatParkParkingConnectDevice device : devices) {
        // 验证车道是否存在
        PmgBatParkParkingRoadway roadway = roadwayService.getById(device.getRoadwayId());
        if (roadway == null) {
            return R.error("车道不存在");
        }
        
        // 验证设备是否已绑定
        if (isDeviceBound(device.getDeviceName())) {
            return R.error("设备已被绑定");
        }
        
        // 设置关联信息
        device.setEntranceId(roadway.getEntranceId());
        device.setParkingId(roadway.getParkingId());
    }
    
    return R.status(deviceService.saveBatch(devices));
}
```

#### 4.2.2 解绑验证
```java
public R unboundDevice(String ids) {
    List<Long> deviceIds = Arrays.stream(ids.split(","))
        .map(Long::valueOf)
        .collect(Collectors.toList());
    
    // 检查设备是否正在使用
    for (Long deviceId : deviceIds) {
        if (isDeviceInUse(deviceId)) {
            return R.error("设备正在使用中，无法解绑");
        }
    }
    
    return R.status(deviceService.removeByIds(deviceIds));
}
```

## 5. 数据库表结构

### 5.1 出入口信息表 (pmg_bat_park_parking_entrance)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| park_id | varchar(64) | 园区ID |
| parking_id | bigint | 停车场ID |
| name | varchar(100) | 出入口名称 |
| type | int | 出入口类型（0-进口，1-出口） |
| status | int | 状态（0-禁用，1-启用） |
| description | varchar(500) | 描述 |
| remark | varchar(500) | 备注 |

### 5.2 车道信息表 (pmg_bat_park_parking_roadway)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| park_id | varchar(64) | 园区ID |
| entrance_id | bigint | 出入口ID |
| row_name | varchar(100) | 车道名称 |
| type | int | 车道类型（0-进道，1-出道） |
| led_enable | boolean | LED显示屏启用 |
| voice_enable | boolean | 语音播报启用 |
| gate_enable | boolean | 道闸启用 |
| camera_enable | boolean | 摄像头启用 |
| menu_type | int | 菜单类型 |
| remark | varchar(500) | 备注 |

### 5.3 设备绑定表 (pmg_bat_park_parking_connect_device)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| product_id | bigint | 产品ID |
| product_key | varchar(100) | 产品Key |
| device_name | varchar(100) | 设备名称 |
| device_alias | varchar(100) | 设备别名 |
| roadway_id | bigint | 车道ID |
| entrance_id | bigint | 出入口ID |
| parking_id | bigint | 停车场ID |
| barrier_gate_type | int | 道闸类型 |
| device_type | int | 设备类型 |
| status | int | 绑定状态 |
| remark | varchar(500) | 备注 |

## 6. 业务规则

### 6.1 出入口管理规则
1. **唯一性约束**：同一停车场内出入口名称不能重复
2. **类型约束**：出入口类型只能是进口或出口
3. **删除约束**：删除出入口前必须先删除所有关联车道
4. **状态管理**：禁用出入口会影响所有关联车道的使用

### 6.2 车道管理规则
1. **类型一致性**：车道类型必须与所属出入口类型一致
2. **设备依赖**：删除车道前必须先解绑所有关联设备
3. **命名规范**：车道名称建议包含出入口名称便于识别
4. **功能配置**：车道的设备功能配置影响实际业务流程

### 6.3 设备绑定规则
1. **唯一绑定**：一个设备只能绑定到一个车道
2. **类型匹配**：设备类型必须与车道功能配置匹配
3. **状态检查**：只有在线且正常的设备才能进行绑定
4. **解绑限制**：正在使用中的设备不能直接解绑

## 7. 技术实现

### 7.1 核心服务类
- `ParkingService`：出入口和车道管理的主要服务接口
- `ParkingEntranceService`：出入口专门管理服务
- `ParkingRoadwayService`：车道专门管理服务
- `DeviceConnectService`：设备绑定管理服务

### 7.2 主要控制器
- `ParkingController`：统一的停车场管理接口
- 路径：`/parking/PmgBatParkParking`
- 包含出入口、车道、设备绑定的所有操作接口

### 7.3 数据传输对象
- `PmgBatParkParkingRoadwayPageVO`：车道分页查询返回对象
- `BindDeviceInfoVO`：设备绑定信息视图对象
- `RoadwayDeviceQueryDTO`：车道设备查询参数对象
