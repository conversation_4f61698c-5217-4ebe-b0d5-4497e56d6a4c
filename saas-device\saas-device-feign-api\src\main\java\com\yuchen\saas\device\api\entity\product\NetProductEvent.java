package com.yuchen.saas.device.api.entity.product;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 产品事件
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_product_event")
@ApiModel(value="NetProductEvent对象", description="产品事件")
public class NetProductEvent extends BaseEntity {

    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 事件id
     */
    @ApiModelProperty(name = "eventId", value = "事件id")
    private String eventId;
    /**
     * 功能url
     */
    @ApiModelProperty(name = "url", value = "功能url")
    private String url;
    /**
     * 事件标识
     */
    @ApiModelProperty(name = "eventKey", value = "事件标识")
    private String eventKey;
    /**
     * 事件名称
     */
    @ApiModelProperty(name = "eventName", value = "事件名称")
    private String eventName;
    /**
     * 事件类型
     * 1：信息
     * 2：告警
     * 3：故障
     */
    @ApiModelProperty(name = "eventType", value = "事件类型")
    private Integer eventType;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remark", value = "备注信息")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    /**
     * 输出参数Json字符串
     */
    @ApiModelProperty(name = "jsonParam", value = "输出参数Json字符串")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String jsonParam;



}
