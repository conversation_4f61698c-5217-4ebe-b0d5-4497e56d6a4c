package com.yuchen.saas.device.api.dto.operatingEnd.product;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @create 2024/5/27 16:51
 */
@Data
public class NetProductTagSubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;

    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    @NotNull(message = "产品id不能为空")
    private Long productId;
    /**
     * 标签id
     */
    @ApiModelProperty(name = "tagId", value = "标签id")
    @NotBlank(message = "标签id不能为空")
    private String tagId;
    /**
     * 标签标识
     */
    @ApiModelProperty(name = "tagKey", value = "标签标识")
    @NotBlank(message = "标签标识不能为空")
    private String tagKey;
    /**
     * 标签名称
     */
    @ApiModelProperty(name = "tagName", value = "标签名称")
    @NotBlank(message = "标签名称不能为空")
    private String tagName;

    /**
     * 标签值
     */
    @ApiModelProperty(name = "tagValue", value = "标签值")
    @NotBlank(message = "标签值不能为空")
    private String tagValue;
    /**
     * 属性类型
     * 1：int(整数型)
     * 2：long(长整数型)
     * 3：float(单精度浮点型)
     * 4：double(双精度浮点型)
     * 5：text(字符串)
     * 6：bool(布尔型)
     * 7：time(时间类型)
     */
    @ApiModelProperty(name = "dataType", value = "数据类型")
    private Integer dataType;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

}
