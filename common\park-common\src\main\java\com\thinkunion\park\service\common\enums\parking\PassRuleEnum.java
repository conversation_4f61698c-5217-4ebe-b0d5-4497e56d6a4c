package com.thinkunion.park.service.common.enums.parking;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/27 18:45
 * <p>
 * 放行状态码
 */
@Deprecated
public enum PassRuleEnum {


    /**
     * 固定车自动放行
     */
    TIME_PERIOD_PASS(101),
    /**
     * 临时车自动放行
     */
    TEMPORARY_PASS(202),
    /**
     * 节假日自动放行
     */
    HOLIDAYS_PASS(303),
    /**
     * 特殊车辆自动放行
     */
    SPECIAL_PASS(404),
    /**
     * 黑名单不放行
     */
    FIVE(505),
    /**
     * 群组车自动放行
     */
    SIX(606),
    /**
     * 车位满了禁止放行
     */
    SEVEN(707),
    /**
     * 收费为0禁止放行
     */
    EIGHT(808),
    /**
     * 未知原因
     */
    NINE(909),
    /**
     * 无牌车自动放行
     */
    TEN(1010),
    /**
     * 未交费不放行
     */
    ELEVEN(1111);


    private int value;

    PassRuleEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
