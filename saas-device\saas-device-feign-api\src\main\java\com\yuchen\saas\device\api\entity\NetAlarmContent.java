package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 告警内容设置
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_alarm_content")
@ApiModel(value="NetAlarmContent对象", description="告警内容设置")
public class NetAlarmContent extends BaseEntity {

    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerId", value = "所属客户")
    private Long customerId;
    /**
     * 告警内容
     */
    @ApiModelProperty(name = "alarmContent", value = "告警内容")
    private String alarmContent;
    /**
     * 告警内容编码
     */
    @ApiModelProperty(name = "alarmContentCode", value = "告警内容编码")
    private String alarmContentCode;
    /**
     * 告警内容分类（1：物联告警、2：设备告警、3：业务告警）
     */
    @ApiModelProperty(name = "alarmContentCategory", value = "告警内容分类（1：物联告警、2：设备告警、3：业务告警）")
    private String alarmContentCategory;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "deviceCategoryId", value = "设备类型")
    private Long deviceCategoryId;
    /**
     * 产品标识
     */
    @ApiModelProperty(name = "productKey", value = "产品标识")
    private String productKey;
    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;



}
