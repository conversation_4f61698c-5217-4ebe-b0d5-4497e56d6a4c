package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nest.springwrap.core.mp.support.Query;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/9/24 15:19
 * @Description TODO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NetAlarmStatisticsPageDto extends Query {
    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerManageId", value = "客户id")
    private Long customerManageId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 开始时间
     */
    @ApiModelProperty(name = "beginTime", value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private Date beginTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(name = "endTime", value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;
}
