package com.yuchen.saas.consumer.service.util.wechat;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2024/1/15 14:18
 */
public class WeChatCustomMessageUtil {

    // 客服消息请求url http请求方式: POST https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=ACCESS_TOKEN
    private final String postUrl;

    private final RestTemplate restTemplate;

    public WeChatCustomMessageUtil(String postUrl, RestTemplate restTemplate) {
        this.postUrl = postUrl;
        this.restTemplate = restTemplate;
    }

    /**
     * 发送客服消息
     */
    public String doSendMessage(String touser, String content) {
        JSONObject paramJson = new JSONObject();
        paramJson.put("touser", touser);
        paramJson.put("msgtype", "text");
        JSONObject text = JSONObject.parseObject("{}");
        text.put("content", content);
        paramJson.put("text", text);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(postUrl, paramJson, JSONObject.class);
        JSONObject json = Optional.ofNullable(responseEntity.getBody()).orElse(new JSONObject());
        return json.toJSONString();
    }

    /**
     * 发送图文消息
     */
    public String doSendImgtext(String touser, String title, String description, String url, String picurl) {
        JSONObject paramJson = new JSONObject();
        paramJson.put("touser", touser);
        paramJson.put("msgtype", "news");
        JSONObject news = new JSONObject();
        JSONArray articles = JSONArray.parseArray("[{\"title\":\"" + title + "\",\"description\":\"" + description + "\",\"url\":\"" + url + "\",\"picurl\":\"" + picurl + "\"}]");
        news.put("articles", articles);
        paramJson.put("news", news);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(postUrl, paramJson, JSONObject.class);
        JSONObject json = Optional.ofNullable(responseEntity.getBody()).orElse(new JSONObject());
        return json.toJSONString();
    }

    /**
     * 发送图片消息
     */
    public String doSendImg(String touser, String MEDIA_ID) {
        JSONObject paramJson = new JSONObject();
        paramJson.put("touser", touser);
        paramJson.put("msgtype", "image");
        JSONObject image = new JSONObject();
        image.put("media_id", MEDIA_ID);
        paramJson.put("image", image);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(postUrl, paramJson, JSONObject.class);
        JSONObject json = Optional.ofNullable(responseEntity.getBody()).orElse(new JSONObject());
        return json.toJSONString();
    }

    /**
     * 小程序卡片发送
     */
    public String doSendMiniProgram(String touser, String appId, String miniProgramAppId,String miniProgramTitle,String pagepath, String thumb_media_id) {
        JSONObject paramJson = new JSONObject();
        paramJson.put("touser", touser);
        paramJson.put("msgtype", "miniprogrampage");
        // 小程序信息
        JSONObject miniprogrampage = new JSONObject();
        miniprogrampage.put("title", miniProgramTitle);
        miniprogrampage.put("appid", miniProgramAppId);
        miniprogrampage.put("pagepath", pagepath);
        miniprogrampage.put("thumb_media_id", thumb_media_id);
        paramJson.put("miniprogrampage", miniprogrampage);

        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(postUrl, paramJson, JSONObject.class);
        JSONObject json = Optional.ofNullable(responseEntity.getBody()).orElse(new JSONObject());
        return json.toJSONString();
    }
}
