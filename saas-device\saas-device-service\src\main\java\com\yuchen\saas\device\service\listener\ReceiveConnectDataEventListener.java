package com.yuchen.saas.device.service.listener;

import com.yuchen.saas.device.service.event.ReceiveConnectDataEvent;
import com.yuchen.saas.device.service.service.alarm.NetAlarmRunRuleService;
import com.yuchen.saas.device.service.service.net.NetDeviceConnectDataService;
import com.yuchen.saas.device.service.service.net.NetOperateDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class ReceiveConnectDataEventListener {
    @Resource
    private NetDeviceConnectDataService netDeviceConnectDataService;
    @Resource
    private NetOperateDeviceService netOperateDeviceService;

    @Resource
    private NetAlarmRunRuleService netAlarmRunRuleService;

//    /**
//     * 保存设备属性上报数据
//     *
//     * @param event
//     * @throws Exception
//     */
//    @TransactionalEventListener
//    public void saveConnectData(ReceiveConnectDataEvent event) {
//        ConnectEvent connectEvent = event.getConnectEvent();
//        NetDeviceConnectData connectData = BeanUtil.copyProperties(connectEvent, NetDeviceConnectData.class);
//        connectData.setIsOnline(connectEvent.getOnline() ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue());
//        connectData.setCreateTime(new Date());
//        netDeviceConnectDataService.save(connectData);
//        netOperateDeviceService.updateDeviceOnlineStatus(connectEvent);
//    }

    /**
     * 执行报警规则
     *
     * @param event
     * @throws Exception
     */
    @EventListener
    public void alarmRunRule(ReceiveConnectDataEvent event) {
        netAlarmRunRuleService.receiveConnectData(event.getConnectEvent());
    }

//
//    /**
//     * 设备状态上报处理各产品业务
//     *
//     * @param event
//     * @throws Exception
//     */
//    @TransactionalEventListener
//    public void doConnectBiz(ReceiveConnectDataEvent event) {
//        ConnectEvent connectEvent = event.getConnectEvent();
//        NetProductEnum productEnum = NetProductEnum.convert(connectEvent.getProductKey());
//        ConnectBizService connectBizService = SpringUtil.getBean(productEnum.getServiceNamePrefix() + "ConnectBizServiceImpl");
//        if (connectBizService != null) {
//            connectBizService.doBiz(connectEvent);
//        }
//    }

}
