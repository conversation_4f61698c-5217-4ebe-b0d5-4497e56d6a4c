package com.yuchen.saas.consumer.api.entity;

import cn.hutool.extra.spring.SpringUtil;
import lombok.Data;

/**
 * 获取微信小程序Scheme码
 *
 * 	jump_wxa	object	否	跳转到的目标小程序信息。
 * 属性	类型	必填	说明
 * path	string	否	通过 scheme 码进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query。path 为空时会跳转小程序主页。
 * query	string	否	通过 scheme 码进入小程序时的 query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：`!#$&'()*+,/:;=?@-._~%``
 * env_version	string	否	默认值"release"。要打开的小程序版本。正式版为"release"，体验版为"trial"，开发版为"develop"，仅在微信外打开时生效。
 * is_expire	boolean	否	默认值false。生成的 scheme 码类型，到期失效：true，30天有效：false。
 * expire_time	number	否	到期失效的 scheme 码的失效时间，为 Unix 时间戳。生成的到期失效 scheme 码在该时间前有效。最长有效期为30天。is_expire 为 true 且 expire_type 为 0 时必填
 * expire_type	number	否	默认值0，到期失效的 scheme 码失效类型，失效时间：0，失效间隔天数：1
 * expire_interval	number	否	到期失效的 scheme 码的失效间隔天数。生成的到期失效 scheme 码在该间隔时间到达前有效。最长间隔天数为30天。is_expire 为 true 且 expire_type 为 1 时必填
 * 返回参数
 * 属性	类型	说明
 * errcode	number	错误码
 * errmsg	string	错误信息
 * openlink	string	生成的小程序 scheme 码
 * 错误码
 * 错误码	错误码取值	解决方案
 * -1	system error	系统繁忙，此时请开发者稍候再试
 * 40001	invalid credential  access_token isinvalid or not latest	获取 access_token 时 AppSecret 错误，或者 access_token 无效。请开发者认真比对 AppSecret 的正确性，或查看是否正在为恰当的公众号调用接口
 * 85079	miniprogram has no online release	小程序没有线上版本，不能进行灰度
 * 40165	invalid weapp pagepath	参数path填写错误，更正后重试
 * 40212	invalid query	参数query填写错误 ，query格式遵循URL标准，即k1=v1&k2=v2
 * 85401	time limit between 1min and 1year	参数expire_time填写错误，时间间隔大于1分钟且小于1年，更正后重试
 * 85402	invalid env_version	参数env_version填写错误，更正后重试
 * 44990	reach max api second frequence limit	频率过快，超过100次/秒；降低调用频率
 * 44993	reach max api day frequence limit	单天生成Scheme+URL Link数量超过上限50万
 */
@Data
public class WeChatSchemeParam {


    @Data
    public static class JumpWxa{
        /**
         * 通过 scheme 码进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query。path 为空时会跳转小程序主页。
         */
        private String 	path;

        /**
         * 通过 scheme 码进入小程序时的 query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：`!#$&'()*+,/:;=?@-._~%``
         */
        private String query;

        /**
         * 	默认值"release"。要打开的小程序版本。正式版为"release"，体验版为"trial"，开发版为"develop"，仅在微信外打开时生效。
         */
        private String env_version;

        public String getEnv_version(){
            String environment= SpringUtil.getActiveProfile();
            if ("dev".equals(environment)){
                return "develop";
            } else if ("test".equals(environment)){
                return "trial";
            } else if ("prod".equals(environment)){
                return "release";
            }else{
                return "release";
            }
        }
    }

    /**
     * 默认值false。生成的 scheme 码类型，到期失效：true，30天有效：false。
     */
    private Boolean is_expire;
    /**
     * 到期失效的 scheme 码的失效时间，为 Unix 时间戳。生成的到期失效 scheme 码在该时间前有效。最长有效期为30天。is_expire 为 true 且 expire_type 为 0 时必填
     */
    private Integer expire_time;
    /**
     * 所需下发的订阅模板id
     */
    private Integer expire_type;
    /**
     * 到期失效的 scheme 码的失效间隔天数。生成的到期失效 scheme 码在该间隔时间到达前有效。最长间隔天数为30天。is_expire 为 true 且 expire_type 为 1 时必填
     */
    private Integer expire_interval;

    private JumpWxa	jump_wxa;

}
