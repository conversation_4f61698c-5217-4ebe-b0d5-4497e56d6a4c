package com.yuchen.saas.device.api.dto.operatingEnd;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @create 2024/5/21 10:02
 */
@Data
public class NetProductCategorySubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;

    /**
     * 父节点id
     */
    @ApiModelProperty(name = "parentId", value = "父节点id")
    private Long parentId;
    /**
     * 分类名称
     */
    @NotBlank(message = "产品分类名称不能为空")
    @ApiModelProperty(name = "categoryName", value = "分类名称")
    private String categoryName;
    /**
     * 分类标识
     */
    @NotBlank(groups = GroupAction.InsertAction.class, message = "分类标识不能为空")
    @ApiModelProperty(name = "categoryCode", value = "分类标识")
    private String categoryCode;

    /**
     * 排序
     */
    @ApiModelProperty(name = "sort", value = "排序")
    private Integer sort;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 业务状态
     */
    @ApiModelProperty("业务状态")
    private Integer status;

}
