package com.thinkunion.park.service.common.iot.capability.api;

import com.thinkunion.park.service.common.iot.util.TextHelper;

public class DeviceBasicData implements Cloneable {

    private String modelType;
    private String pk;
    private String name;
    private transient String desc;
    private transient String addr;
    private transient String deviceModelJson;
    private transient String iotId;
    private transient int port;
    private transient boolean isLocal;

    public DeviceBasicData() {
        this.isLocal = false;
    }

    public DeviceBasicData(boolean isLocal) {
        this.isLocal = isLocal;
    }

    public DeviceBasicData(DeviceBasicData basicData) {
        this.modelType = basicData.modelType;
        this.pk = basicData.getPk();
        this.name = basicData.getName();
        this.desc = basicData.getDesc();
        this.addr = basicData.getAddr();
        this.deviceModelJson = basicData.getDeviceModelJson();
        this.iotId = basicData.getIotId();
        this.port = basicData.getPort();
        this.isLocal = basicData.isLocal;
    }

    /**
     * @deprecated
     */
    @Deprecated
    public String getProdKey() {
        return this.getPk();
    }

    /**
     * @deprecated
     */
    @Deprecated
    public void setProdKey(String prodKey) {
        this.setPk(prodKey);
    }

    public int getPort() {
        return this.port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public boolean isLocal() {
        return this.isLocal;
    }

    public void setLocal(boolean local) {
        this.isLocal = local;
    }

    public String getDeviceModelJson() {
        return this.deviceModelJson;
    }

    public void setDeviceModelJson(String deviceModelJson) {
        this.deviceModelJson = deviceModelJson;
    }

    public String getAddr() {
        return this.addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public String getModelType() {
        return this.modelType;
    }

    public void setModelType(String model) {
        this.modelType = model;
    }

    public String getPk() {
        return this.pk;
    }

    public void setPk(String pk) {
        this.pk = pk;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDevId() {
        return TextHelper.combineStr(this.getPk(), this.getName());
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIotId() {
        return this.iotId;
    }

    public void setIotId(String iotId) {
        this.iotId = iotId;
    }
}
