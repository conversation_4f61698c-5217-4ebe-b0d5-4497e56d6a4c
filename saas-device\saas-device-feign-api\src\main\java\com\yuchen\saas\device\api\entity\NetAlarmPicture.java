package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.tenant.mp.TenantEntity;

import java.util.Map;

/**
 * <p>
 * 抓拍记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_alarm_picture")
@ApiModel(value="NetAlarmPicture对象", description="抓拍记录")
public class NetAlarmPicture extends TenantEntity {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "parkId", value = "项目ID")
    private Long parkId;
    /**
     * 平台设备ID
     */
    @ApiModelProperty(name = "deviceId", value = "平台设备ID")
    private Long deviceId;
    /**
     * 平台设备编码
     */
    @ApiModelProperty(name = "deviceNo", value = "平台设备编码")
    private String deviceNo;

    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 设备类型id
     */
    @ApiModelProperty(name = "deviceCategoryId", value = "设备类型id")
    private Long deviceCategoryId;
    /**
     * 设备productKey
     */
    @ApiModelProperty(name = "productKey", value = "设备productKey")
    private String productKey;
    /**
     * 设备deviceName
     */
    @ApiModelProperty(name = "deviceName", value = "设备deviceName")
    private String deviceName;
    /**
     * 人员类型
     */
    @ApiModelProperty(name = "personType", value = "人员类型")
    private String personType;
    /**
     * 人员名称
     */
    @ApiModelProperty(name = "personName", value = "人员名称")
    private String personName;
    /**
     * 时间(iot属性)
     */
    @ApiModelProperty(name = "timeStamp", value = "时间(iot属性)")
    private Long timeStamp;
    /**
     * 告警类型(iot属性)
     */
    @ApiModelProperty(name = "alarmType", value = "告警类型(iot属性)")
    private String alarmType;
    /**
     * 性别：0-未知，1-男，2-女(iot属性)
     */
    @ApiModelProperty(name = "gender", value = "性别：0-未知，1-男，2-女(iot属性)")
    private Integer gender;
    /**
     * 年龄段（枚举值：0-未知，1-儿童，2-少年，3-青年，4-中年，5-老年）(iot属性)
     */
    @ApiModelProperty(name = "ageRange", value = "年龄段（枚举值：0-未知，1-儿童，2-少年，3-青年，4-中年，5-老年）(iot属性)")
    private Integer ageRange;
    /**
     * 是否佩戴眼镜（枚举值：0-未知，1-不戴，2-戴）(iot属性)
     */
    @ApiModelProperty(name = "glassFlag", value = "是否佩戴眼镜（枚举值：0-未知，1-不戴，2-戴）(iot属性)")
    private Integer glassFlag;
    /**
     * 眼镜款式（枚举值：0-未知，1-普通眼镜，2-太阳镜，99-其他）(iot属性)
     */
    @ApiModelProperty(name = "glassesStyle", value = "眼镜款式（枚举值：0-未知，1-普通眼镜，2-太阳镜，99-其他）(iot属性)")
    private Integer glassesStyle;
    /**
     * 是否佩戴口罩（枚举值：0-未知，1-不戴，2-戴）(iot属性)
     */
    @ApiModelProperty(name = "maskFlag", value = "是否佩戴口罩（枚举值：0-未知，1-不戴，2-戴）(iot属性)")
    private Integer maskFlag;
    /**
     * 是否戴帽子（枚举值：0-未知，1-不戴，2-戴）(iot属性)
     */
    @ApiModelProperty(name = "hatFlag", value = "是否戴帽子（枚举值：0-未知，1-不戴，2-戴）(iot属性)")
    private Integer hatFlag;
    /**
     * 图片路径(iot属性)
     */
    @ApiModelProperty(name = "alarmPicture", value = "图片路径(iot属性)")
    private String alarmPicture;

    /**
     * iot相关基础消息字段
     */
    private String name;

    private String identifier;

    private Long eventTime;

    private Long serverTime;

    private Long deviceTime;

}
