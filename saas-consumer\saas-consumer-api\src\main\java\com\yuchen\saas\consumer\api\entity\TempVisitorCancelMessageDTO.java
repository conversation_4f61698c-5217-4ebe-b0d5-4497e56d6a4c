package com.yuchen.saas.consumer.api.entity;

import lombok.Data;

/**
 * @description: 类目模版消息-火炬访客取消预约消息通知模板
 * <AUTHOR>
 * @date 2023/7/13
 * @version 1.0
 */
@Data
public class TempVisitorCancelMessageDTO {

    /**
     * 模版ID
     */
    private String TEMPLATE_ID;

    /**
     * 支付平台用户ID
     */
    private String payUserId;

    /**
     * appId
     */
    private String appId;

    /**
     * 访客姓名
     */
    private String thing2;

    /**
     * 访客电话
     */
    private String phone_number3;

    /**
     * 来访起始时间
     */
    private String time7;

    /**
     * 来访事由
     */
    private String thing5;

    /**
     * 详情url
     */
    private String url;
}
