package com.yuchen.saas.consumer.service.task;

import com.yuchen.saas.consumer.service.service.PmgBatParkInvoiceOrderService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: 张逸飞
 * @Date: 2021/3/9 14:22
 * @Description: 定时任务类
 */
@Component
public class ConsumerTimedTask {

    /**
     * 发票订单操作接口
     */
    @Resource
    private PmgBatParkInvoiceOrderService pmgBatParkInvoiceOrderService;

    /**
     * 定时任务 每五分钟一次
     */
    @Scheduled(cron = "0 0/5 * * * ? ")
    public void timeEveryMinute() {
        // 定时更新数据库内未开票成功的发票记录
        pmgBatParkInvoiceOrderService.queryInvoiceResult();
    }
}
