package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 车辆类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
public enum PlaceUsedTypeEnum {



//    MONTH_CAR_PLACE("1", "月卡车车位"),
//    STAGGERED_CAR_PLACE("2", "错时车位"),
//    CHANGE_CAR_PLACE("3", "动态车位"),
//    AB_CAR_PLACE("4", "AB车位"),
//    PROPERTY_CAR_PLACE("4", "产权车"),
//    FIXED_CAR_PLACE("5", "内部车"),
    NULL("-1", "车辆收费类型置空",-1),
    MONTH_CAR_PLACE("1", "月卡车位",1),
    STAGGERED_CAR_PLACE("2", "错时车位",2),
    FIXED_CAR_PLACE("3", "内部车",3),
    PROPERTY_CAR_PLACE("4", "产权车",4),
    TEMPORARY_CAR_PLACE("5", "临时车",5),
   ;

    private String code;



    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    private Integer Type;
    public Integer getType() {
        return Type;
    }
    public static String getDescByCode(String code) {
        PlaceUsedTypeEnum[] enumArray = PlaceUsedTypeEnum.values();
        for (PlaceUsedTypeEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }

    public static String getCodeByType(Integer type) {
        PlaceUsedTypeEnum[] enumArray = PlaceUsedTypeEnum.values();
        for (PlaceUsedTypeEnum e : enumArray) {
            if (e.getType().equals(type)) {
                return e.getCode();
            }
        }
        return "";
    }

    /**
     * 校验是否是停车场限制车辆类型的唯一收费性
     * @param type
     * @return
     */
    public static Boolean checkIsCharge(Integer type) {
        if(List.of(MONTH_CAR_PLACE.getType(),STAGGERED_CAR_PLACE.getType(),FIXED_CAR_PLACE.getType(),PROPERTY_CAR_PLACE.getType()).contains(type)){
            return true;
        }
        return false;
    }

    public static String getDescByType(Integer type) {
        PlaceUsedTypeEnum[] enumArray = PlaceUsedTypeEnum.values();
        for (PlaceUsedTypeEnum e : enumArray) {
            if (e.getCode().equals(type)) {
                return e.getDescription();
            }
        }
        return "";
    }


}
