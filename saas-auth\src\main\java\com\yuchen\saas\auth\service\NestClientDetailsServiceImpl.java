package com.yuchen.saas.auth.service;

import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;

import javax.sql.DataSource;

public class NestClientDetailsServiceImpl extends JdbcClientDetailsService {

	public NestClientDetailsServiceImpl(DataSource dataSource) {
		super(dataSource);
	}

	@Override
	public ClientDetails loadClientByClientId(String clientId) {
		try {
			return super.loadClientByClientId(clientId);
		} catch (Exception ex) {
			ex.printStackTrace();
			return null;
		}
	}
}
