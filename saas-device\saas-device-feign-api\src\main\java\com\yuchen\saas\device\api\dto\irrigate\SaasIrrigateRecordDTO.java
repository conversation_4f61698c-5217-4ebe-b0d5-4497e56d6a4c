package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SaasIrrigateRecordDTO {

    /**
     * 浇灌区域ID
     */
    @ApiModelProperty(name = "irrigateAreaId", value = "浇灌区域ID")
    private Long irrigateAreaId;
    /**
     * 浇灌类型（1：自动浇灌，2：定时浇灌，3：手动浇灌，4：临时浇灌）
     */
    @ApiModelProperty(name = "irrigateType", value = "浇灌类型（1：自动浇灌，2：定时浇灌，3：手动浇灌，4：临时浇灌）")
    private Integer irrigateType;
    /**
     * 浇灌设备ID
     */
    @ApiModelProperty(name = "irrigateDeviceId", value = "浇灌设备ID")
    private Long irrigateDeviceId;
    /**
     * 浇灌任务ID
     */
    @ApiModelProperty(name = "irrigateTaskId", value = "浇灌任务ID")
    private Long irrigateTaskId;
    /**
     * 浇灌时间
     */
    @ApiModelProperty(name = "irrigateTime", value = "浇灌时间")
    private Date irrigateTime;
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;

}
