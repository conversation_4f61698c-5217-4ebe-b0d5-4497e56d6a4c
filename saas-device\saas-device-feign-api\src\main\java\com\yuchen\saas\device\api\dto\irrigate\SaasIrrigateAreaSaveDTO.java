package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/30 15:53
 * @Description
 */
@Data
@ApiModel(value = "IrrigateAreaSaveDTO")
public class SaasIrrigateAreaSaveDTO {

    /**
     * 区域编码（id）
     */
    @ApiModelProperty(name = "irrigateAreaCode", value = "区域编码（id）")
    @NotBlank(message = "区域编码不能为空")
    private String irrigateAreaCode;
    /**
     * 区域名称
     */
    @ApiModelProperty(name = "irrigateAreaName", value = "区域名称")
    @NotBlank(message = "区域名称不能为空")
    private String irrigateAreaName;
    /**
     * 区域描述
     */
    @ApiModelProperty(name = "describeContents", value = "区域描述")
    private String describeContents;
    /**
     * 绿植id（多个逗号隔开）
     */
    @ApiModelProperty(name = "greenPlantsId", value = "绿植id（多个逗号隔开）")
    private String greenPlantsId;
    /**
     * 园区ID（项目id）
     */
    @ApiModelProperty(name = "parkId", value = "园区ID（项目id）")
    @NotNull(message = "项目id不能为空")
    private Long parkId;

}
