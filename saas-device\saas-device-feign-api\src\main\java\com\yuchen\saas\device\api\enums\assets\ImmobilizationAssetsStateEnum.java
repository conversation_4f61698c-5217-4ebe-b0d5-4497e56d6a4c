package com.yuchen.saas.device.api.enums.assets;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/3/7 10:28
 */
@Getter
@AllArgsConstructor
public enum ImmobilizationAssetsStateEnum {

    LEISURE("1", "空闲", 1),
    RECEIVE("2", "在用", 2),
//    BORROW(3, "借用", 3),
    DISPOSED("4", "已处置", 4),

    REPAIR_ING("5", "报修中", 5),
    MAINTAIN_ING("6", "维修中", 6),
//    HANDOVER_ING(7, "交接中", 7),
//    ALLOT_ING(8, "调拨中", 8),
//    ALTER_ING(9, "变更中", 9),
//    RETURN_ING(10, "退还中", 10),

//    RECEIVE_ING(11, "领用待确认", 11),
//    CANCELLING_STOCKS_ING(12, "退库待确认", 12),
//    BORROW_ING(13, "借用待确认", 13),
//    RESTORE_ING(14, "归还待确认", 14),
//    DISPOSE_ING(15, "处置待确认", 15),
    ;

    private final String code;
    private final String desc;
    private final int sort;

    public static String getDescByCode(String code) {
        if (Objects.nonNull(code)) {
            for (ImmobilizationAssetsStateEnum e : ImmobilizationAssetsStateEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e.getDesc();
                }
            }
        }
        return "";
    }

}
