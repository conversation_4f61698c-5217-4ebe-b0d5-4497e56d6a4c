package com.yuchen.saas.device.api.dto.assets;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.tool.validator.group.AddGroup;
import org.nest.springwrap.core.tool.validator.group.UpdateGroup;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "SaasAssetsCategoryAddDTO", description = "SaasAssetsCategoryAddDTO")
public class SaasAssetsCategoryAddDTO {
    @ApiModelProperty(name = "id", value = "id")
    @NotNull(message="id不能为空",groups = {UpdateGroup.class})
    private Long id;
    /**
     * 资产分类编码
     */
    @ApiModelProperty(name = "categoryCode", value = "资产分类编码")
    @NotBlank(message="资产分类编码不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private String categoryCode;
    /**
     * 资产分类名称
     */
    @ApiModelProperty(name = "categoryName", value = "资产分类名称")
    @NotBlank(message="资产分类名称不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private String categoryName;
    /**
     * 父级id
     */
    @ApiModelProperty(name = "parentId", value = "父级id")
    @NotNull(message="父级id不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private Long parentId;
    /**
     * 预计使用期限（月）
     */
    @ApiModelProperty(name = "usagePeriod", value = "预计使用期限（月）")
    private Integer usagePeriod;
    /**
     * 计量单位
     */
    @ApiModelProperty(name = "measureUnit", value = "计量单位")
    private String measureUnit;
    /**
     * 排序
     */
    @ApiModelProperty(name = "sort", value = "排序")
    private Integer sort;
    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    @NotNull(message="客户id不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private Long customerId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    @NotNull(message="项目id不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private Long projectId;

}
