package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/23 17:34
 * @Description TODO
 */
@Data
public class SceneLinkageEvent implements Serializable {
    /**
     * 场景id
     */
    @ApiModelProperty(name = "sceneId", value = "场景id")
    private Long sceneId;
    /**
     * 时间
     */
    @ApiModelProperty(name = "eventTime", value = "时间")
    private Long eventTime;
}
