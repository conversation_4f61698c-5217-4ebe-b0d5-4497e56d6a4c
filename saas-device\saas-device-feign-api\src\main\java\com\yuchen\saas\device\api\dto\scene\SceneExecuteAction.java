package com.yuchen.saas.device.api.dto.scene;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Map;

/**
 * 执行动作对象
 */
@Data
public class SceneExecuteAction {

    /**
     * 动作类型：setProperty-设备输出，trigger-规则输出，triggerAlarm-告警输出
     */
    @NotNull(message = "动作类型不能为空")
    @Pattern(regexp = "setProperty|trigger|triggerAlarm", message = "执行条件类型：setProperty，trigger，triggerAlarm")
    private String uri;

    /**
     * 产品key
     */
    @NotNull(message = "产品不能为空", groups = {Device.class})
    private String productKey;

    /**
     * 设备名称
     */
    @NotNull(message = "设备不能为空", groups = {Device.class})
    private String deviceName;

    /**
     * 属性名 (设备输出，该参数与serviceName，二选一)
     */
    private String propertyName;

    /**
     * 服务名 (设备输出，该参数与propertyName，二选一)
     */
    private String serviceName;

    /**
     * 参数（设备输出）
     */
    @NotNull(message = "参数不能为空", groups = {Device.class})
    private Map<String,Object> propertyItems;

    /**
     * 参数类型 （设备输出，值在NetDataTypeEnum定义）
     */
    @NotNull(message = "参数类型不能为空", groups = {Device.class})
    private Map<String,Object> propertyItemsType;

    /**
     * 场景规则id （规则输出，net_scene表id，该id不能为当前规则id）
     */
    @NotNull(message = "场景规则不能为空", groups = {Rule.class})
    private String netSceneId;

    /**
     * 规则输出动作，0-停止，1-启用，2-触发
     */
    @NotNull(message = "执行操作不能为空", groups = {Rule.class})
    private Integer ruleActionType;

    /**
     * 告警规则id （告警输出）
     */
    @NotNull(message = "告警规则不能为空", groups = {Alarm.class})
    private Long netAlarmId;

    public interface Device {
    }

    public interface Rule {
    }

    public interface Alarm {
    }



}
