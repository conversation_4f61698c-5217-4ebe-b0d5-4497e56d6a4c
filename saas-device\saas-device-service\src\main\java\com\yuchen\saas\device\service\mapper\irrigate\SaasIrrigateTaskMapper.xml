<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.irrigate.SaasIrrigateTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.irrigate.SaasIrrigateTask">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="task_no" property="taskNo" />
        <result column="irrigate_auto_id" property="irrigateAutoId" />
        <result column="task_name" property="taskName" />
        <result column="irrigate_date" property="irrigateDate" />
        <result column="irrigate_start_time" property="irrigateStartTime" />
        <result column="irrigate_end_time" property="irrigateEndTime" />
        <result column="irrigate_area_id" property="irrigateAreaId" />
        <result column="irrigate_point_id" property="irrigatePointId" />
        <result column="task_status" property="taskStatus" />
        <result column="close_reason" property="closeReason" />
        <result column="remark" property="remark" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, task_no, irrigate_auto_id, task_name, irrigate_date, irrigate_start_time, irrigate_end_time, irrigate_area_id, irrigate_point_id, task_status, close_reason, remark, create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

</mapper>
