package com.yuchen.saas.auth.constant;

public interface AuthConstant {

    String ENCRYPT = "{nest}";

    String CLIENT_FIELDS = "client_id, CONCAT('{noop}',client_secret) as client_secret, resource_ids, scope, " +
            "authorized_grant_types, " +
            "web_server_redirect_uri, authorities, access_token_validity, " +
            "refresh_token_validity, additional_information, autoapprove";

    String BASE_STATEMENT = "select " + CLIENT_FIELDS + " from nest_client";

    String DEFAULT_FIND_STATEMENT = BASE_STATEMENT + " order by client_id";

    String DEFAULT_SELECT_STATEMENT = BASE_STATEMENT + " where client_id = ?";
}
