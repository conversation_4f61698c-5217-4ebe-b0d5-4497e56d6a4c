package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 能源类型（0：其他类型、1：汽油车、2：新能源）
 * <AUTHOR>
 */
@AllArgsConstructor
public enum VehicleTypeEnum {


    OTHER(0, "其他类型"),
    GASOLINE_CAR(1, "汽油车"),
    NEW_ENERGY(2, "新能源"),
   ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        VehicleTypeEnum[] enumArray = VehicleTypeEnum.values();
        for (VehicleTypeEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
