package com.thinkunion.park.service.common.enums.parking;

/**
 * <AUTHOR>
 * @date 2021/11/16 18:22
 * @Description 物业分成比例枚举
 */
public enum ProportionEnum {

    ZERO("", 0.0, "0%"),
    /**
     * 艾德物业分成
     */
    AIDE("艾德停车场", 0.3, "30%"),
    /**
     * 通用厂房-地面
     */
    GROUND_PLANT("通用厂房-地面", 0.0, "0%"),
    /**
     * 通用厂房-地库
     */
    UNDER_PLANT("通用厂房-地库", 0.3, "30%"),
    /**
     * 五州停车场
     */
    WU("五洲停车场", 0.3, "30%");
    /**
     * 停车场名称
     */
    private String parkingName;

    /**
     * 分成比率，不带%
     */
    private double rate;

    /**
     * 分成比率，带%
     */
    private String ratePercentage;

    public String getParkingName() {
        return parkingName;
    }

    public double getRate() {
        return rate;
    }

    public String getRatePercentage() {
        return ratePercentage;
    }

    ProportionEnum(String parkingName, double rate, String ratePercentage) {
        this.parkingName = parkingName;
        this.rate = rate;
        this.ratePercentage = ratePercentage;
    }

}
