package com.yuchen.saas.device.api.dto.assets;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2024/2/27 20:19
 */
@Data
public class AssetsDataReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;

    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    private Long customerId;

    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    /**
     * 资产分类id
     */
    @ApiModelProperty(name = "assetsSortId", value = "资产分类id")
    @NotNull(message = "请选择资产分类")
    private Long assetsSortId;
    /**
     * 资产分类名称
     */
    @ApiModelProperty(name = "assetsSort", value = "资产分类名称")
    private String assetsSort;

    /**
     * 资产名称
     */
    @ApiModelProperty(name = "assetsName", value = "资产名称")
    @NotBlank(message = "资产名称不能为空")
    private String assetsName;
    /**
     * 资产管理员id
     */
    @ApiModelProperty(name = "assetsAdminUserId", value = "资产管理员id")
    private Long assetsAdminUserId;

    /**
     * 资产管理员名称
     */
    @ApiModelProperty(name = "assetsAdminUser", value = "资产管理员名称")
    private String assetsAdminUser;

    /**
     * 资产品牌
     */
    @ApiModelProperty(name = "assetsBrand", value = "资产品牌")
    private String assetsBrand;
    /**
     * 资产型号
     */
    @ApiModelProperty(name = "assetsModel", value = "资产型号")
    private String assetsModel;
    /**
     * 所属位置id
     */
    @ApiModelProperty(name = "assetsLocationId", value = "所属位置id")
    private Long assetsLocationId;
    /**
     * 所属位置
     */
    @ApiModelProperty(name = "assetsLocation", value = "所属位置")
    private String assetsLocation;
    /**
     * 购置/起租日期
     */
    @ApiModelProperty(name = "assetsPurchaseDate", value = "购置/起租日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsPurchaseDate;
    /**
     * 购置方式编码
     */
    @ApiModelProperty(name = "assetsPurchaseModelCode", value = "购置方式编码")
    private String assetsPurchaseModelCode;
    /**
     * 购置方式名称
     */
    @ApiModelProperty(name = "assetsPurchaseModel", value = "购置方式名称")
    private String assetsPurchaseModel;
    /**
     * 预计使用期限
     */
    @ApiModelProperty(name = "assetsPredictUseMonth", value = "预计使用期限")
    private Integer assetsPredictUseMonth;
    /**
     * 资产金额
     */
    @ApiModelProperty(name = "assetsAmount", value = "资产金额")
    private Long assetsAmount;
    /**
     * 计量单位
     */
    @ApiModelProperty(name = "assetsMeasureUnit", value = "计量单位")
    private String assetsMeasureUnit;
    /**
     * 设备序列号
     */
    @ApiModelProperty(name = "assetsEquipmentSerialNumber", value = "设备序列号")
    private String assetsEquipmentSerialNumber;
    /**
     * 生产厂商
     */
    @ApiModelProperty(name = "manufacturer", value = "生产厂商")
    private String manufacturer;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "assetsRemark", value = "备注信息")
    private String assetsRemark;
    /**
     * 资产图片
     */
    @ApiModelProperty(name = "assetsImagesUrl", value = "资产图片")
    private String assetsImagesUrl;
    /**
     * 绑定智能硬件（0：否，1：是）
     */
    @ApiModelProperty(name = "bindIntelligentHardware", value = "绑定智能硬件（0：否，1：是）")
    private String bindIntelligentHardware;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "equipmentCode", value = "设备编码")
    private String equipmentCode;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "equipmentType", value = "设备类型")
    private String equipmentType;
    /**
     * 设备类型id
     */
    @ApiModelProperty(name = "equipmentTypeId", value = "设备类型id")
    private Long equipmentTypeId;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "equipmentName", value = "设备名称")
    private String equipmentName;
    /**
     * 使用状态编码
     */
    @ApiModelProperty(name = "assetsUseStatusCode", value = "使用状态编码")
    private String assetsUseStatusCode;
    /**
     * 使用状态
     */
    @ApiModelProperty(name = "assetsUseStatus", value = "使用状态")
    private String assetsUseStatus;
    /**
     * 使用人
     */
    @ApiModelProperty(name = "assetsUserName", value = "使用人")
    private String assetsUserName;
    /**
     * 使用人id
     */
    @ApiModelProperty(name = "assetsUserId", value = "使用人id")
    private Long assetsUserId;
    /**
     * 使用部门
     */
    @ApiModelProperty(name = "assetsUseDept", value = "使用部门")
    private String assetsUseDept;
    /**
     * 使用部门id
     */
    @ApiModelProperty(name = "assetsUseDeptId", value = "使用部门id")
    private Long assetsUseDeptId;
    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "assetsSupplierName", value = "供应商名称")
    private String assetsSupplierName;
    /**
     * 联系人
     */
    @ApiModelProperty(name = "assetsLinkman", value = "联系人")
    private String assetsLinkman;
    /**
     * 联系方式
     */
    @ApiModelProperty(name = "assetsLinkPhone", value = "联系方式")
    private String assetsLinkPhone;
    /**
     * 维保到期日期
     */
    @ApiModelProperty(name = "assetsMaintenanceExpireDate", value = "维保到期日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsMaintenanceExpireDate;
    /**
     * 维保说明
     */
    @ApiModelProperty(name = "assetsMaintenanceExplain", value = "维保说明")
    private String assetsMaintenanceExplain;



    /**
     * 订单号
     */
    @ApiModelProperty(name = "assetsOrderNo", value = "订单号")
    private String assetsOrderNo;
    /**
     * 供应商编码
     */
    @ApiModelProperty(name = "assetsSupplierCode", value = "供应商编码")
    private String assetsSupplierCode;
    /**
     * 邮箱
     */
    @ApiModelProperty(name = "assetsLinkEmail", value = "邮箱")
    private String assetsLinkEmail;
    /**
     * 领用日期
     */
    @ApiModelProperty(name = "assetsClaimingDate", value = "领用日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsClaimingDate;
    /**
     * 硬盘
     */
    @ApiModelProperty(name = "assetsDisk", value = "硬盘")
    private String assetsDisk;
    /**
     * 内存
     */
    @ApiModelProperty(name = "assetsInternal", value = "内存")
    private String assetsInternal;
    /**
     * CPU型号
     */
    @ApiModelProperty(name = "assetsCpuModel", value = "CPU型号")
    private String assetsCpuModel;


}
