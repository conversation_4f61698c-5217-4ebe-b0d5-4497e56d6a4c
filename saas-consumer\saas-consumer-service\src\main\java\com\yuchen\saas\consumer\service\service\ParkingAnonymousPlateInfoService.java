package com.yuchen.saas.consumer.service.service;

import com.yuchen.saas.consumer.service.entity.PmgBatParkNoPlateInfo;
import org.nest.springwrap.core.mp.base.BaseService;
import org.nest.springwrap.core.tool.api.R;

/**
 * @Author: 张逸飞
 * @Date: 2021/4/2 11:04
 * @Description:
 */
public interface ParkingAnonymousPlateInfoService extends BaseService<PmgBatParkNoPlateInfo> {

    /**
     * 无牌车-登记
     * @param pmgBatParkNoPlateInfo 无牌车信息
     * @return
     */
    R register(PmgBatParkNoPlateInfo pmgBatParkNoPlateInfo);

    /**
     * 根据用户ID获取无牌车登记列表
     * @param payUserId 支付平台用户ID
     * @return
     */
    R getListByUserId(String payUserId);
}
