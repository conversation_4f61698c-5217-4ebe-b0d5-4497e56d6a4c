<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>smart-park-integration</artifactId>
        <groupId>com.yuchen.saas</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>saas-auth</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <!--Base-->
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>launcher</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>constant-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>core-db</artifactId>
        </dependency>
        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>core-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>starter-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>starter-social</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-user-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-system-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
            <version>2.3.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>2.2.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-jwt</artifactId>
        </dependency>
        <!-- 验证码 -->
        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>
        <!-- 链路追踪、服务监控 -->
        <!--<dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>starter-trace</artifactId>
        </dependency>-->
        <!-- 解决Java11无法运行的问题 -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>