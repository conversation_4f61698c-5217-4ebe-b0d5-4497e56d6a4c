package com.yuchen.saas.device.api.dto.scene;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;

@Data
public class NetSceneRecordQryDto extends Query {

    /**
     * 客户id
     */
    private Long customerManageId;
    /**
     * 场景类型，1-设备触发，2-定时触发，3-手动触发
     */
    private Integer triggerMode;

    /**
     * 执行状态; 1-成功，0-失败
     */
    private Integer status;
    /**
     * 执行开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startCreateTime;
    /**
     * 执行结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCreateTime;

}
