package com.yuchen.saas.device.service.factory;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/9/29 17:11
 */
@Component
public class DeviceApplicationContextUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    private static Environment environment;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        DeviceApplicationContextUtil.applicationContext = applicationContext;
        DeviceApplicationContextUtil.environment = applicationContext.getEnvironment();
    }

    public static ApplicationContext getApplicationContext() {
        return DeviceApplicationContextUtil.applicationContext;
    }

    public static Environment getEnvironment() {
        return DeviceApplicationContextUtil.environment;
    }

    public static String getProperty(String key) {
        return applicationContext.getBean(Environment.class).getProperty(key);
    }

    public static Object getBean(String name) {
        return applicationContext.getBean(name);
    }

    public static <T> T getBean(String name, Class<T> clazz) {
        return applicationContext.getBean(name, clazz);
    }

    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    public static <T> Map<String, T> getBeans(Class<T> clazz) {
        return applicationContext.getBeansOfType(clazz);
    }
}
