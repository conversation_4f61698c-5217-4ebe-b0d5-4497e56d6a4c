package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.io.Serializable;
import java.util.Date;

@Data
public class SaasIrrigateRecordStatisticsQueryDTO implements Serializable {
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 浇灌设备ID
     */
    @ApiModelProperty(name = "irrigateDeviceId", value = "浇灌设备ID")
    private Long irrigateDeviceId;
    /**
     * 浇灌区域ID
     */
    @ApiModelProperty(name = "irrigateAreaId", value = "浇灌区域ID")
    private String irrigateAreaId;
    /**
     * 浇灌开始时间
     */
    @ApiModelProperty(name = "irrigateStartTime", value = "浇灌开始时间")
    private Date irrigateStartTime;
    /**
     * 浇灌结束时间
     */
    @ApiModelProperty(name = "irrigateEndTime", value = "浇灌结束时间")
    private Date irrigateEndTime;

}
