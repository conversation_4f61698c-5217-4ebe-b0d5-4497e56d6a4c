package com.yuchen.saas.device.api.dto.deviceability;

import lombok.Data;

/**
 * 物模型-功能实体类
 * <AUTHOR>
 * @create 2024/6/27 17:51
 */
@Data
public class DeviceFunctionDataDTO {

    /**
     * 功能id
     */
    private String functionId;
    /**
     * 功能标识
     */
    private String functionKey;
    /**
     * 功能名称
     */
    private String functionName;
    /**
     * 对接方式
     */
    private String invokeModel;
    /**
     * 请求url
     */
    private String url;
    /**
     * 输入参数（json数据）
     */
    private String inputJsonParam;
    /**
     * 输出参数（json数据）
     */
    private String outputJsonParam;
}
