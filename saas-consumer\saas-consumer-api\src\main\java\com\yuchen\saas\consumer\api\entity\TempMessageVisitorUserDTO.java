package com.yuchen.saas.consumer.api.entity;

import lombok.Data;

/**
 * @description: 模版消息-预约成功通知(平台用户)-实体类
 * <AUTHOR>
 * @date 2023/6/30 16:14
 * @version 1.0
 */
@Data
public class TempMessageVisitorUserDTO extends TempMessageBaseDTO {

    /**
     * 模版ID
     */
    private final String TEMPLATE_ID = "8D7v2BW9EAiod31dZuEVXZRuE-kfYyOabsZy5NIEHCA";

    /**
     * appId
     */
    private String appId;

    /**
     * 访客姓名
     */
    private String keyword1;

    /**
     * 拜访对象
     */
    private String keyword2;

    /**
     * 到访时间
     */
    private String keyword3;

    /**
     * 详情url
     */
    private String url;
}
