package com.yuchen.saas.device.service.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.yuchen.saas.device.api.entity.NetDeviceEventData;
import com.yuchen.saas.device.api.enums.NetProductEnum;
import com.yuchen.saas.device.service.event.ReceiveEventDataEvent;
import com.yuchen.saas.device.service.service.iot.event.EventBizService;
import com.yuchen.saas.device.service.service.alarm.NetAlarmRunRuleService;
import com.yuchen.saas.device.service.service.iot.event.EventFactory;
import com.yuchen.saas.device.service.service.net.NetDeviceEventDataService;
import com.yuchen.saas.park.api.dto.park.IoTEvent;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.tool.utils.Func;
import org.springframework.beans.BeansException;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备事件数据上报监听
 */
@Slf4j
@Service
public class ReceiveEventDataEventListener {
    @Resource
    private NetDeviceEventDataService netDeviceEventDataService;
    @Resource
    private NetAlarmRunRuleService netAlarmRunRuleService;


    /**
     * 保存设备属性上报数据，TODO 后续优化 mongodb 或 hbase 存储
     *
     * @param event
     * @throws Exception
     */
    @TransactionalEventListener(fallbackExecution = true)
    public void saveEventData(ReceiveEventDataEvent event) {
        log.info("保存事件信息***********", Func.toJson(event));
        List<NetDeviceEventData> eventDataList = new ArrayList<>();
        IoTEvent ioTEvent = event.getIoTEvent();
        Date eventTime = DateUtil.date(ioTEvent.getEventTime() * 1000);
        Date serverTime = DateUtil.date(ioTEvent.getServerTime() * 1000);
        Map<String, Object> out = ioTEvent.getOut();
        out.forEach((key, value) -> {
            NetDeviceEventData data = BeanUtil.copyProperties(ioTEvent, NetDeviceEventData.class);
            data.setIdentify(key);
            data.setValue(value == null ? null : String.valueOf(value));
            data.setDeviceTime(eventTime);
            data.setServerTime(serverTime);
            data.setCreateTime(new Date());
            eventDataList.add(data);
        });
        netDeviceEventDataService.saveBatch(eventDataList);
    }


    /**
     * 执行报警规则
     *
     * @param event
     * @throws Exception
     */
    @EventListener
    public void alarmRunRule(ReceiveEventDataEvent event) {
        netAlarmRunRuleService.receiveEventData(event.getIoTEvent());
    }


    /**
     * 事件上报处理各产品业务
     *
     * @param event
     * @throws Exception
     */
    @TransactionalEventListener(fallbackExecution = true)
    public void doEventBiz(ReceiveEventDataEvent event) {
        IoTEvent ioTEvent = event.getIoTEvent();
        /*NetProductEnum productEnum = NetProductEnum.convert(ioTEvent.getProductKey());
        EventBizService eventBizService=null;
        try{
             eventBizService = SpringUtil.getBean(productEnum.getServiceNamePrefix() + "EventBizServiceImpl");
        }catch (BeansException e){
            log.info(String.format("获取%s产品的bean业务处理类失败",ioTEvent.getProductKey()));
        }
        if (eventBizService != null) {
            eventBizService.doBiz(ioTEvent);
        }*/
        try {
            EventFactory.doBiz(ioTEvent);
        }catch (Exception e){
            log.info("产品事件处理异常",e);
        }

    }


}
