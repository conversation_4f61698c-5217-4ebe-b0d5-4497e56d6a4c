package com.yuchen.saas.consumer.service.service.wechatTemp;

import com.yuchen.saas.consumer.api.dto.wechat.SendTemplateReqDTO;

/**
 * <AUTHOR>
 * @create 2024/1/5 14:42
 */
public interface WechatPushTempReportService {

    /**
     * 发送模版消息
     * @param reqDTO 模版消息实体类
     * @return
     */
    void sendWechatTempMessage(SendTemplateReqDTO reqDTO);


    String type();

    String getWxToken(String appId);

    SendTemplateReqDTO setOfficialTempConfigData(SendTemplateReqDTO reqDTO);
}
