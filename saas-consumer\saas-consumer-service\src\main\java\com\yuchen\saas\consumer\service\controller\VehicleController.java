package com.yuchen.saas.consumer.service.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yuchen.saas.manage.system.api.entity.NestCustomerManage;
import com.yuchen.saas.manage.system.api.feign.CustomerManageFeignServer;
import com.yuchen.saas.manage.user.api.entity.UserVehicleData;
import com.yuchen.saas.manage.user.api.entity.UserVehicleWalletPaySort;
import com.yuchen.saas.manage.user.api.feign.UserVehicleDataClient;
import com.yuchen.saas.manage.user.api.request.dto.*;
import com.yuchen.saas.manage.user.api.vo.UpdateUserVehicleResultVO;
import com.yuchen.saas.manage.user.api.vo.UserVehicleCertificationLogVO;
import com.yuchen.saas.parking.api.dto.AuditDto;
import com.yuchen.saas.parking.api.dto.ParkingSharePlaceAuditDto;
import com.yuchen.saas.parking.api.entity.ParkingSharePlaceAudit;
import com.yuchen.saas.parking.api.feign.ParkingSharePlaceClient;
import com.yuchen.saas.parking.api.feign.ReplacePlateNoRecordClient;
import com.yuchen.saas.parking.api.feign.StaggeredMonthlyCardClient;
import com.yuchen.saas.parking.api.feign.VehicleMonthlyCardDataClient;
import com.yuchen.saas.parking.api.param.*;
import com.yuchen.saas.parking.api.vo.*;
import com.yuchen.saas.payment.api.dto.wallet.NoFeelWalletPayDataReqDTO;
import com.yuchen.saas.payment.api.enums.SaasPaymentErrorEnums;
import com.yuchen.saas.payment.api.feign.WalletFeign;
import com.yuchen.saas.payment.api.vo.wallet.UserWalletInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.log.exception.ServiceException;
import org.nest.springwrap.core.mp.support.NestPage;
import org.nest.springwrap.core.secure.ContextUser;
import org.nest.springwrap.core.secure.utils.AuthUtil;
import org.nest.springwrap.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName VehicleController
 * @Description 我的车辆控制器
 * <AUTHOR>
 * @Date 2022/6/16 15:50
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/vehicle")
@Api(value = "我的车辆相关接口", tags = "我的车辆相关接口")
public class VehicleController {

    /**
     * 注入用户车辆
     */
    @Resource
    private UserVehicleDataClient userVehicleDataClient;

    /**
     * 注入钱包接口
     */
    @Resource
    private WalletFeign walletFeign;

    /**
     * 注入客户接口
     */
    private CustomerManageFeignServer customerManageFeignServer;

    /**
     * 注入月卡车辆
     */
    @Resource
    private VehicleMonthlyCardDataClient vehicleMonthlyCardDataClient;

    /**
     * 错时停车feign 调用接口
     */
    @Resource
    private StaggeredMonthlyCardClient staggeredMonthlyCardClient;
    @Resource
    private ParkingSharePlaceClient parkingSharePlaceClient;

    @Resource
    private ReplacePlateNoRecordClient replacePlateNoRecordClient;

    /**
     * 用户获取车辆数据分页
     * @param current 当前页码
     * @param size 分页数量
     * @return
     */
    @GetMapping("/getPage")
    @ApiOperation(value = "用户获取车辆数据分页", notes = "用户获取车辆数据分页")
    public R getPage(@RequestParam Integer current, @RequestParam Integer size) {
        //获取用户信息
        ContextUser contextUser = AuthUtil.getUser();
        if (Objects.isNull(contextUser)) {
            throw new ServiceException(SaasPaymentErrorEnums.USER_DATA_ERROR);
        }
        Long userDetailId = contextUser.getUserDetailId();
        log.info("userDetailId: {}", userDetailId);
        return userVehicleDataClient.getPage(userDetailId, current, size);
    }

    /**
     * 用户保存车辆数据
     * @param userVehicleData 用户车辆数据
     * @return
     */
    @PostMapping("/save")
    @ApiOperation(value = "用户保存车辆数据", notes = "用户保存车辆数据")
    public R save(@RequestBody @Validated UserVehicleData userVehicleData) {
        //获取用户信息
        ContextUser contextUser = AuthUtil.getUser();
        if (Objects.isNull(contextUser)) {
            throw new ServiceException(SaasPaymentErrorEnums.USER_DATA_ERROR);
        }
        Long userDetailId = contextUser.getUserDetailId();
        log.info("userDetailId: {}", userDetailId);
        userVehicleData.setUserDetailId(userDetailId);
        return userVehicleDataClient.save(userVehicleData);
    }

    /**
     * 用户保存认证车主允许其他用户添加车辆数据
     * @param userVehicleData 用户车辆数据
     * @return
     */
    @PostMapping("/saveUserVehicleAuthorize")
    @ApiOperation(value = "用户保存认证车主允许其他用户添加车辆数据", notes = "用户保存认证车主允许其他用户添加车辆数据")
    public R saveUserVehicleAuthorize(@RequestBody @Validated UserVehicleData userVehicleData) {
        //获取用户信息
        ContextUser contextUser = AuthUtil.getUser();
        if (Objects.isNull(contextUser)) {
            throw new ServiceException(SaasPaymentErrorEnums.USER_DATA_ERROR);
        }
        Long userDetailId = contextUser.getUserDetailId();
        log.info("userDetailId: {}", userDetailId);
        userVehicleData.setUserDetailId(userDetailId);
        return userVehicleDataClient.saveUserVehicleAuthorize(userVehicleData);
    }

    /**
     * 编辑车辆(设置默认车辆、开启即插即充)
     * @param userVehicleData 用户车辆数据
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "编辑车辆(设置默认车辆、开启即插即充)", notes = "编辑车辆(设置默认车辆、开启即插即充)")
    public R<UpdateUserVehicleResultVO> update(@RequestBody UserVehicleData userVehicleData) {

        return userVehicleDataClient.update(userVehicleData);
    }

    /**
     * 认证车辆-相关设置
     * @param reqDTO 设置数据
     * @return true/false
     */
    @PostMapping("/saveUserVehiclePrivacyConfig")
    public R<Boolean> saveUserVehiclePrivacyConfig(@RequestBody UserVehiclePrivacyReqDTO reqDTO) {
        return userVehicleDataClient.saveUserVehiclePrivacyConfig(reqDTO);
    }

    /**
     * 变更车牌号
     * @param changePlateNoDTO 变更车牌号传参
     * @return true/false
     */
    @PostMapping("/updatePlateNo")
    @ApiOperation(value = "变更车牌号", notes = "变更车牌号")
    public R<Boolean> updatePlateNo(@RequestBody @Validated ChangePlateNoDTO changePlateNoDTO) {
        return userVehicleDataClient.updatePlateNo(changePlateNoDTO);
    }

    /**
     * 判断该车牌号车辆是否已被认证并且被认证用户关闭了可代缴功能
     * @param plateNo 车牌号
     * @return true/false
     */
    @GetMapping("/isReplacePayJudge")
    @ApiOperation(value = "判断该车牌号车辆是否已被认证并且被认证用户关闭了可代缴功能", notes = "判断该车牌号车辆是否已被认证并且被认证用户关闭了可代缴功能")
    public R<Boolean> isReplacePayJudge(@RequestParam String plateNo) {
        return userVehicleDataClient.isReplacePayJudge(plateNo);
    }

    /**
     * 判断当前车牌是否被其他用户开启无感支付
     * @param dto 判断当前车牌是否被其他用户开启无感支付传参
     * @return true/false
     */
    @PostMapping("/senselessPayJudge")
    @ApiOperation(value = "判断当前车牌是否被其他用户开启无感支付", notes = "判断当前车牌是否被其他用户开启无感支付")
    public R<Boolean> senselessPayJudge(@RequestBody SenselessPayJudgeDTO dto) {
        return userVehicleDataClient.senselessPayJudge(dto);
    }

    /**
     * 开启/关闭可代缴、个人钱包账户无感支付、优惠券无感支付、微信支付分|先享后付无感支付
     * @param dto 可代缴、无感支付传参
     * @return true/false
     */
    @PostMapping("/enableFunction")
    @ApiOperation(value = "开启/关闭可代缴、个人钱包账户无感支付、优惠券无感支付、微信支付分|先享后付无感支付", notes = "开启/关闭可代缴、个人钱包账户无感支付、优惠券无感支付、微信支付分|先享后付无感支付")
    public R<Boolean> enableFunction(@RequestBody @Validated UserVehicleEnableFunctionDTO dto) {
        return userVehicleDataClient.enableFunction(dto);
    }

    /**
     * 获取钱包无感支付数据
     * @param vehicleId 车辆数据ID
     * @return list
     */
    @GetMapping("/getUserVehicleWalletPaySortInfo/{vehicleId}")
    public R<List<UserVehicleWalletPaySort>> getUserVehicleWalletPaySortInfo(@PathVariable Long vehicleId) {
        return userVehicleDataClient.getUserVehicleWalletPaySortInfo(vehicleId);
    }

    /**
     * 获取过滤无感支付剩下的钱包列表
     * @param reqDTO 车辆钱包数据
     * @return list
     */
    @PostMapping("/findNoVehicleWalletPaySortInfo")
    public R<List<UserWalletInfoVO>> findNoVehicleWalletPaySortInfo(@RequestBody NoFeelWalletPayDataReqDTO reqDTO) {
        return walletFeign.findNoVehicleWalletPaySortInfo(reqDTO);
    }

    /**
     * 获取所有客户列表
     */
    @GetMapping("/getAllCustomerManageList")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "获取所有客户列表", notes = "获取所有客户列表")
    public R<List<NestCustomerManage>> getAllCustomerManageList() {
        return customerManageFeignServer.getAllCustomerManageList();
    }

    /**
     * 用户删除车辆数据
     * @param payUserId 移动端用户唯一ID
     * @param id 车辆数据ID
     * @return
     */
    @GetMapping("/remove")
    @ApiOperation(value = "用户删除车辆数据", notes = "用户删除车辆数据")
    public R remove(@RequestParam String payUserId, @RequestParam String id) {

        return userVehicleDataClient.remove(id);
    }

    /**
     * 获取车辆申请月卡分页数据
     * @param pageParam 分页查询参数
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/getAuditPage")
    @ApiOperation(value = "获取车辆申请月卡分页数据", notes = "获取车辆申请月卡分页数据")
    public R getAuditPage(VehicleMonthlyCardDataPageParam pageParam) {
        return vehicleMonthlyCardDataClient.getAuditPage(pageParam.getPayUserId(),pageParam.getPersonPhone(),pageParam.getCurrent(),
                pageParam.getSize());
    }
    /**
     * 审核回显详情
     *
     * @param id id
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/getAuditDetail")
    @ApiOperation(value = "月卡审核回显详情", notes = "月卡审核回显详情")
    public R<VehicleMonthlyCardDetailVo>  getAuditDetail(@RequestParam(required = true) Long id) {
        return vehicleMonthlyCardDataClient.getAuditDetail(id);
    }

    /**
     * 审核月卡申请
     *
     * @param param 审核结果
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/auditData")
    @ApiOperation(value = "审核月卡申请", notes = "审核月卡申请")
    public R auditData(@Valid @RequestBody AuditMonthlyCardParam param) {
        return vehicleMonthlyCardDataClient.auditData(param);
    }
    /**
     * 获取车辆错时停车申请分页数据
     * @param pageParam 分页查询参数
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/getStaggeredPage")
    @ApiOperation(value = "获取车辆错时停车申请分页数据", notes = "错时停车")
    public R<IPage<StaggeredMonthlyCardDataVo>> getStaggeredPage(VehicleMonthlyCardDataPageParam pageParam) {
        return staggeredMonthlyCardClient.getAuditPage(pageParam.getPayUserId(),pageParam.getPersonPhone(),pageParam.getCurrent(),
                pageParam.getSize());
    }

    /**
     * 获取车辆错时停车规则
     * @param parkId 园区id
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/getStaggeredRule")
    @ApiOperation(value = "获取车辆错时停车规则", notes = "错时停车")
    public R getStaggeredRule(@RequestParam("parkId") Long parkId, @RequestParam("parkingId") Long parkingId) {
        return staggeredMonthlyCardClient.getStaggeredRule(parkId,parkingId);
    }

    /**
     * 移动端申请月卡
     * @param cardDataVo 申请月卡相关数据
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/applyMonthlyCard")
    @ApiOperation(value = "移动端申请月卡", notes = "移动端申请月卡")
    public R applyMonthlyCard(@RequestBody VehicleMonthlyCardDataVo cardDataVo) {
        return vehicleMonthlyCardDataClient.applyMonthlyCard(cardDataVo);
    }

    /**
     * 移动端修改月卡
     * @param cardDataVo 移动端修改月卡
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/updateMonthlyCard")
    @ApiOperation(value = "修改月卡记录", notes = "修改月卡记录")
    public R updateMonthlyCard(@RequestBody VehicleMonthlyCardDataVo cardDataVo) {
        return vehicleMonthlyCardDataClient.updateMonthlyCard(cardDataVo);
    }

    /**
     * 移动端删除月卡
     * @param cardDataVo 移动端删除月卡
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/delMonthlyCard")
    @ApiOperation(value = "移动端删除月卡", notes = "移动端删除月卡")
    public R delMonthlyCard(@RequestBody VehicleMonthlyCardDataVo cardDataVo) {
        return vehicleMonthlyCardDataClient.delMonthlyCard(cardDataVo.getId());
    }

    /**
     * 申请月卡更换车牌
     *
     * @param replacePlateNoRecord 月卡更换车牌数据
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/applyReplace")
    @ApiOperation(value = "申请月卡/错时更换车牌/车主", notes = "申请月卡/错时更换车牌/车主")
    public R applyReplace(@RequestBody ReplacePlateNoRecordAddParam replacePlateNoRecord) {
        replacePlateNoRecord.setApplyType("1");
        return vehicleMonthlyCardDataClient.applyReplace(replacePlateNoRecord);
    }
    /**
     * 获取月卡变更车牌详情
     *
     * @param id id
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/getReplaceByMonthlyId")
    @ApiOperation(value = "获取月卡变更车牌详情", notes = "获取月卡变更车牌详情")
    public R<ReplacePlateNoRecordAppVo>  getReplaceByMonthlyId(@RequestParam Long id) {
        return vehicleMonthlyCardDataClient.getByMonthlyId(id);
    }
    /**
     * 获取错时变更车牌详情
     *
     * @param id id
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/getReplaceByStaggeredId")
    @ApiOperation(value = "获取错时变更车牌详情", notes = "获取错时变更车牌详情")
    public R<ReplacePlateNoRecordAppVo>  getReplaceByStaggeredId(@RequestParam Long id) {
        return vehicleMonthlyCardDataClient.getByStaggeredId(id);
    }

    @PostMapping("/appCancelReplace")
    @ApiOperation(value = "变更车牌取消", notes = "变更车牌取消")
    public R appCancelReplace(@RequestBody ReplacePlateNoCancelParam param) {
        return vehicleMonthlyCardDataClient.appCancelReplace(param);
    }

    /**
     * 移动端申请错时停车
     * @param cardDataVo 申请相关数据
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/applyStaggeredCard")
    @ApiOperation(value = "移动端申请错时停车", notes = "错时停车")
    public R applyStaggeredCard(@RequestBody StaggeredMonthlyCardDataVo cardDataVo) {
        return staggeredMonthlyCardClient.applyMonthlyCard(cardDataVo);
    }

    /**
     * 移动端修改错时停车
     * @param cardDataVo 移动端修改错时停车
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/updateStaggeredCard")
    @ApiOperation(value = "移动端修改错时停车", notes = "错时停车")
    public R updateStaggeredCard(@RequestBody StaggeredMonthlyCardDataVo cardDataVo) {
        return staggeredMonthlyCardClient.updateMonthlyCard(cardDataVo);
    }

    /**
     * 移动端删除错时卡
     * @param cardDataVo 移动端删除错时卡
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/delStaggeredCard")
    @ApiOperation(value = "移动端删除错时卡", notes = "移动端删除错时卡")
    public R delStaggeredCard(@RequestBody VehicleMonthlyCardDataVo cardDataVo) {
        return staggeredMonthlyCardClient.delMonthlyCard(cardDataVo.getId());
    }

    /**
     * 获得剩余车位
     * @param parkId 申请相关数据
     * @return
     */
    @GetMapping("/getResidueCard")
    @ApiOperation(value = "查询剩余车位", notes = "错时停车")
    public R getResidueCard(@NotNull @RequestParam Long parkId,@NotNull @RequestParam Long ruleId) {
        return staggeredMonthlyCardClient.residueByRuleId(parkId,ruleId);
    }

    /**
     * @tag 1.4.0
     */
    @GetMapping("/certification/log/getUserUnreadList")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "获取用户车辆未读提醒log", notes = "获取用户车辆未读提醒log")
    public R<List<UserVehicleCertificationLogVO>> getUserUnreadList() {
        return userVehicleDataClient.getUserUnreadList();
    }

    /**
     * @tag 1.4.0
     */
    @PostMapping("/certification/log/updateReadStatus")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "修改为已读", notes = "修改为已读")
    public R<Boolean> updateReadStatus(@Validated @RequestBody UserVehicleCertificatioLogUpdateReadDTO dto) {
        return userVehicleDataClient.updateReadStatus(dto);
    }


    /**
     * 移动端申请/更新申请一卡多车
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/applySharePlace")
    @ApiOperation(value = "移动端申请/更新申请一卡多车", notes = "移动端申请/更新申请一卡多车")
    public R applySharePlace(ParkingSharePlaceAudit param) {
        return parkingSharePlaceClient.applySharePlace(param);
    }

    /**
     * 移动端删除一卡多车
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/delApplyAudit")
    @ApiOperation(value = "移动端删除一卡多车", notes = "移动端删除一卡多车")
    public R delSharePlaceApplyAudit(@RequestParam Long id) {
        return parkingSharePlaceClient.delSharePlaceApplyAudit(id);
    }

    /**
     * 一卡多车申请取消
     * @param
     * @return
     * @tag pauly-lyz
     */
    @PutMapping("/cancelCardData/{auditId}")
    @ApiOperation(value = "一卡多车申请取消", notes = "一卡多车申请取消")
    public R cancelCardData(@PathVariable Long auditId) {
        return parkingSharePlaceClient.cancelCardData(auditId);
    }

    /**
     * 一卡多车检测车辆是否拥有月卡/错时卡
     * @param
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/checkMonthlyCard")
    @ApiOperation(value = "一卡多车检测车辆是否拥有月卡/错时卡", notes = "一卡多车检测车辆是否拥有月卡/错时卡")
    public R<VehicleCardDataVo> checkMonthlyCard(Long parkingId, String plateNo){
        return parkingSharePlaceClient.checkMonthlyCard(parkingId, plateNo);
    }

    /**
     * 一卡多车申请审核
     * @param
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/getSharePlaceAuditDetail")
    @ApiOperation(value = "一卡多车申请详情", notes = "一卡多车申请详情")
    public R getSharePlaceAuditDetail(@RequestParam Long id){
        return parkingSharePlaceClient.getSharePlaceAuditDetail(id);
    }

    /**
     * 获取一卡多车审核分页数据
     * @param param 查询参数
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/getSharePlaceAuditPage")
    @ApiOperation(value = "获取一卡多车审核分页数据", notes = "获取一卡多车审核分页数据")
    public R getSharePlaceAuditPage(ParkingSharePlaceAuditDto param){
        return parkingSharePlaceClient.getSharePlaceAuditPage(param);
    }

    /**
     * AB车位申请审核
     * @param
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/auditSharePlace")
    @ApiOperation(value = "一卡多车申请审核", notes = "一卡多车申请审核")
    public R auditSharePlace(AuditDto auditDto){
        return parkingSharePlaceClient.auditSharePlace(auditDto);
    }

    /**
     * 移动端月卡申请免费
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/applyMonthCardFree")
    @ApiOperation(value = "移动端月卡申请免费", notes = "移动端月卡申请免费")
    public R applyMonthCardFree(@RequestBody VehicleMonthlyCardDataVo cardDataVo) {
        return vehicleMonthlyCardDataClient.applyFree(cardDataVo);
    }

    /**
     * 移动端错时申请免费
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/applyStaggeredFree")
    @ApiOperation(value = "移动端错时申请免费", notes = "移动端错时申请免费")
    public R applyStaggeredFree(@RequestBody StaggeredMonthlyCardDataVo cardDataVo) {
        return staggeredMonthlyCardClient.applyFree(cardDataVo);
    }

    /**
     * 管理端获取更换车牌审核列表
     * @return
     */
    @GetMapping("/admin/getPage")
    @ApiOperation(value = "管理端获取更换车牌/车主审核列表", notes = "管理端获取更换车牌/车主审核列表")
    public R<NestPage<ReplacePlateNoRecordPageVo>>getPage(@ApiParam(value = "园区id") @RequestParam Long parkId,
                                                          @ApiParam(value = "数据类型 0 月卡车牌变更 1错时车牌变更") @RequestParam Integer type,
                                                          @ApiParam(value = "停车场ID") @RequestParam(required = false) Long parkingId,
                                                          @ApiParam(value = "月卡类型id") @RequestParam(required = false) Long ruleId,
                                                          @ApiParam(value = "用户手机号") @RequestParam(required = false) String personPhone,
                                                          @ApiParam(value = "是否本人") @RequestParam(required = false) String isOwner,
                                                          @ApiParam(value = "车牌号")@RequestParam(required = false) String plateNo,
                                                          @ApiParam(value = "车主姓名")@RequestParam(required = false) String personName,
                                                          @ApiParam(value = "审核状态")@RequestParam(required = false) Integer  reviewState,
                                                          @ApiParam(value = "是否待处理")@RequestParam(required = false, defaultValue = "false") Boolean pending,
                                                          @RequestParam Integer current, @RequestParam Integer size) {
        return replacePlateNoRecordClient.getPage(parkId,type,parkingId,ruleId, personPhone, isOwner, plateNo, personName, reviewState, pending, current, size);
    }

    /**
     * 管理端获取更换车牌审核详情
     *
     * @return
     * @tag pauly-lyz
     */
    @GetMapping("/getReplacePlateNoDetail")
    @ApiOperation(value = "管理端获取更换车牌/车主审核详情", notes = "管理端获取更换车牌/车主审核详情")
    public R<ReplacePlateNoRecordVo> getReplacePlateNoDetail(@RequestParam Long id) {
        return replacePlateNoRecordClient.getReplacePlateNoDetail(id);
    }

    /**
     * 审核月卡更换车牌
     *
     * @param param 月卡更换车牌审核数据
     * @return
     * @tag pauly-lyz
     */
    @PostMapping("/auditReplace")
    @ApiOperation(value = "管理端审核月卡更换车牌/车主", notes = "管理端审核月卡更换车牌/车主")
    public R<Boolean> auditReplace(@RequestBody ReplacePlateNoRecordParam param) {
        return replacePlateNoRecordClient.auditReplace(param);
    }
}
