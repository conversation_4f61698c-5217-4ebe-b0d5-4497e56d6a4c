# 月卡与错时停车管理源码级分析

## 1. 主要涉及的实体类

- **PmgBatParkingMonthCard**：月卡信息表，记录月卡编号、费用、有效期、状态等。
- **PmgBatParkingMonthCardApply**：月卡申请表，记录用户申请、审核、绑定等信息。
- **PmgBatParkingMonthCardFeeRule**：月卡费用规则表。
- **PmgBatParkingMonthCardBind**：月卡与车辆/用户的绑定关系。
- **PmgBatParkingTimeShareRule**：错时停车规则表。
- **PmgBatParkingTimeShareApply**：错时停车申请表。
- **PmgBatParkingTimeShareBind**：错时停车绑定关系。

## 2. 主要Controller

- `PmgBatParkingMonthCardController`：月卡申请、审核、费用设置、续费、绑定、有效期管理接口
- `PmgBatParkingTimeShareController`：错时停车规则、申请、审核、费用、绑定、有效期管理接口

## 3. 主要Service接口与实现

- `PmgBatParkingMonthCardService`：月卡管理（申请、审核、续费、绑定、有效期等）
- `PmgBatParkingMonthCardFeeRuleService`：月卡费用规则管理
- `PmgBatParkingMonthCardBindService`：月卡绑定管理
- `PmgBatParkingTimeShareService`：错时停车规则、申请、审核、费用、绑定、有效期管理

## 4. 典型方法调用链与数据流转

### 4.1 月卡申请、审核、费用设置、续费、绑定、有效期管理

- 用户通过`PmgBatParkingMonthCardController.apply`提交月卡申请，写入`pmg_bat_parking_month_card_apply`
- 管理员审核通过后，生成月卡记录，写入`pmg_bat_parking_month_card`
- 费用规则通过`PmgBatParkingMonthCardFeeRuleController`配置，存储于`pmg_bat_parking_month_card_fee_rule`
- 续费通过`PmgBatParkingMonthCardController.renew`，更新有效期与费用
- 绑定关系通过`PmgBatParkingMonthCardBindController`管理，支持多车绑定、解绑
- 有效期到期自动提醒、失效处理

### 4.2 错时停车规则、申请、审核、费用、绑定、有效期管理

- 错时规则通过`PmgBatParkingTimeShareController.rule`配置，存储于`pmg_bat_parking_time_share_rule`
- 用户通过`PmgBatParkingTimeShareController.apply`提交错时申请，写入`pmg_bat_parking_time_share_apply`
- 审核通过后，生成绑定关系，写入`pmg_bat_parking_time_share_bind`
- 费用规则、有效期、绑定等通过相关Controller和Service管理
- 到期自动提醒、解绑

## 5. 关键业务流程图示

### 5.1 月卡申请与审核

```mermaid
graph TD
A[用户提交月卡申请] --> B[PmgBatParkingMonthCardController.apply]
B --> C[写入pmg_bat_parking_month_card_apply]
C --> D[管理员审核]
D -- 通过 --> E[生成月卡记录]
E --> F[写入pmg_bat_parking_month_card]
```

### 5.2 月卡续费与绑定

```mermaid
graph TD
A[用户发起续费] --> B[PmgBatParkingMonthCardController.renew]
B --> C[更新有效期/费用]
A2[用户绑定车辆] --> D[PmgBatParkingMonthCardBindController.bind]
D --> E[写入pmg_bat_parking_month_card_bind]
```

### 5.3 错时停车规则与申请

```mermaid
graph TD
A[管理员配置错时规则] --> B[PmgBatParkingTimeShareController.rule]
B --> C[写入pmg_bat_parking_time_share_rule]
A2[用户提交错时申请] --> D[PmgBatParkingTimeShareController.apply]
D --> E[写入pmg_bat_parking_time_share_apply]
E --> F[审核通过生成绑定]
F --> G[写入pmg_bat_parking_time_share_bind]
```

## 6. 其他说明

- 月卡、错时停车均支持多种费用规则、有效期、绑定关系
- 申请、审核、续费、解绑等流程均有详细状态流转与异常处理
- 支持自动提醒、批量操作、灵活扩展

---
如需对某一具体方法、类或流程进一步深入源码解读，请进一步指定需求！ 