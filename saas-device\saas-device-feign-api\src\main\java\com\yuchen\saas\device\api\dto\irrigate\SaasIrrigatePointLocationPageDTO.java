package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/30 15:53
 * @Description
 */
@Data
@ApiModel(value = "IrrigatePointLocationPageDTO")
public class SaasIrrigatePointLocationPageDTO extends Query {

    @ApiModelProperty(value = "项目id不能为空")
    @NotNull(message = "园区id不能为空")
    private Long parkId;

    @ApiModelProperty(value = "区域id")
    private Long irrigateAreaId;


    @ApiModelProperty(name = "irrigatePointLocationName", value = "点位名称")
    private String irrigatePointLocationName;

}
