package com.yuchen.saas.device.api.dto.operatingEnd.alarm;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @create 2024/6/25 10:17
 */
@Data
public class NetAlarmLevelSubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;
    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerId", value = "所属客户")
    private Long customerId;
    /**
     * 等级1
     */
    @ApiModelProperty(name = "levelOne", value = "等级1")
    @NotBlank(message = "等级1")
    private String levelOne;
    /**
     * 等级2
     */
    @ApiModelProperty(name = "levelTwo", value = "等级2")
    @NotBlank(message = "等级2")
    private String levelTwo;
    /**
     * 等级3
     */
    @ApiModelProperty(name = "levelThree", value = "等级3")
    @NotBlank(message = "等级3")
    private String levelThree;
    /**
     * 等级4
     */
    @ApiModelProperty(name = "levelFour", value = "等级4")
    @NotBlank(message = "等级4")
    private String levelFour;
    /**
     * 等级5
     */
    @ApiModelProperty(name = "levelFive", value = "等级5")
    @NotBlank(message = "等级5")
    private String levelFive;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
