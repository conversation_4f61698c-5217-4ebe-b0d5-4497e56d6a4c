package com.yuchen.saas.device.api.dto;

import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;

@Data
public class NetDeviceBasePageQryDto extends Query {

    /**
     * 所属客户id
     */
    private Long customerManageId;
    /**
     * 项目id
     */
    private Long parkId;
    /**
     * 设备编码
     */
    private String deviceName;
    /**
     * 设备名称
     */
    private String deviceAlias;
    /**
     * 设备类型
     */
    private Integer deviceType;
    /**
     * 地理位置
     */
    private String location;
    /**
     * 设备状态；0-断网，1-连网
     */
    private Integer isOnline;
    /**
     * 最后连接时间，开始
     */
    private Date startLastConnectTime;
    /**
     * 最后连接时间，结束
     */
    private Date endLastConnectTime;
    /**
     * 最后断开时间,开始
     */
    private Date startLastDisconnectTime;
    /**
     * 最后断开时间,结束
     */
    private Date endLastDisconnectTime;

}
