package com.yuchen.saas.consumer.api.dto.wechat;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 消息推送响应对象
 * <AUTHOR>
 * @create 2024/1/2 15:03
 */
@Data
@Accessors(chain = true)
public class SendTemplateRespDTO {
    /**
     * 错误码
     * 0 ok
     * 43116 该模板因滥用被滥用过多，已被限制下发
     */
    private long errcode;
    /**
     * 错误信息
     */
    private String errmsg;
    /**
     * 错误信息
     */
    private String msgid;

    public static SendTemplateRespDTO getInstance(){
        return new SendTemplateRespDTO()
                .setErrcode(404L)
                .setErrmsg("");
    }
    public boolean isSuccess() {
        return this.errcode == 0;
    }
}


