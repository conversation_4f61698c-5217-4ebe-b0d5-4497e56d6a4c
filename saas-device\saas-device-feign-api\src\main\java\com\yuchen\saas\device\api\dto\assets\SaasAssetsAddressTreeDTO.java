package com.yuchen.saas.device.api.dto.assets;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "SaasAssetsAddressTreeDTO", description = "SaasAssetsAddressTreeDTO")
public class SaasAssetsAddressTreeDTO {

    @ApiModelProperty(name = "customerId", value = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;

}
