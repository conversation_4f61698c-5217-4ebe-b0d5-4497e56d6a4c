package com.yuchen.saas.device.api.dto.scene;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

@Data
public class DeviceSceneLinkageRuleReqDTO{

    @Null(message="id必须为空",groups = {GroupAction.InsertAction.class})
    @NotNull(message="id不能为空",groups = {GroupAction.UpdateAction.class})
    private Long id;

    /**
     * 所属项目id
     */
    @NotNull(message="所属项目不能为空",groups = {GroupAction.InsertAction.class})
    private Long projectId;

    /**
     * 业务服务id
     */
    @ApiModelProperty(name = "businessId", value = "业务服务id")
    @NotNull(message="服务应用不能为空",groups = {GroupAction.InsertAction.class})
    private Long businessId;

    /**
     * 场景名称
     */
    @NotBlank(message="场景名称不能为空")
    private String sceneName;

    /**
     * 场景类型，1-设备触发，2-定时触发，3-手动触发
     */
    @NotNull(message="触发类型不能为空",groups = {GroupAction.InsertAction.class})
//    @Null(message="触发类型必须为空",groups = {GroupAction.UpdateAction.class})
    private Integer triggerMode;

    /**
     * 执行状态; 1-成功，0-失败
     */
    private Integer status;

    /**
     * 规则描述
     */
    @ApiModelProperty(name = "description", value = "规则描述")
    private String description;



}
