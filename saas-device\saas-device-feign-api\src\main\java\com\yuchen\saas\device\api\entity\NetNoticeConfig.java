package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 通知配置
 */
@Data
@TableName("net_notice_config")
public class NetNoticeConfig {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属客户
     */
    @NotNull(message = "所属客户不能为空")
    private Long customerManageId;

    /**
     * 配置名称
     */
    @NotNull(message = "配置名称不能为空")
    private String noticeName;

    /**
     * 通知分类；1-站外通知，2-弹窗告警，3-站内消息
     */
    @NotNull(message = "通知分类不能为空")
    private Integer noticeCategory;

    /**
     * 通知方式
     */
    @NotNull(message = "通知方式不能为空")
    private Long noticeMethod;

    /**
     * 通知类型
     */
    @NotNull(message = "通知类型不能为空")
    private Integer noticeType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态; 0-停用，1-启用
     */
    private Integer status;

    /**
     * 是否删除，0-否，1-是
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 通知方式名称
     */
    @TableField(exist = false)
    private String noticeMethodName;

    /**
     * 通知类型名称
     */
    @TableField(exist = false)
    private String noticeTypeName;

}
