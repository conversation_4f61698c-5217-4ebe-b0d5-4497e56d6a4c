package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.tenant.mp.TenantEntity;

/**
 * <p>
 * 告警派遣配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_alarm_staff_deploy")
@ApiModel(value="NetAlarmStaffDeploy对象", description="告警派遣配置表")
public class NetAlarmStaffDeploy extends TenantEntity {

    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 是否开启派工
     */
    @ApiModelProperty(name = "isStaffDeployment", value = "是否开启派工(0否1是)")
    private Integer isStaffDeployment;
    /**
     * 是否开启自动派工
     */
    @ApiModelProperty(name = "isAutoDeployment", value = "是否开启自动派工(0否1是)")
    private Integer isAutoDeployment;

}
