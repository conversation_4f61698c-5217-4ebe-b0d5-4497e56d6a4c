package com.yuchen.saas.consumer.service.controller.alarm;

import com.yuchen.saas.device.api.dto.*;
import com.yuchen.saas.device.api.entity.NetAlarmRecord;
import com.yuchen.saas.device.api.feign.NetAlarmFeign;
import com.yuchen.saas.device.api.vo.NetAlarmLevelStatisticsVO;
import com.yuchen.saas.device.api.vo.NetAlarmTrendVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.nest.springwrap.core.mp.support.NestPage;
import org.nest.springwrap.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/27 9:33
 * @Description TODO
 */
@RestController
@RequestMapping("/alarmCenter")
@Api(value = "小程序告警中心相关接口", tags = "小程序告警中心相关接口")
public class NetAlarmCenterController {
    @Resource
    private NetAlarmFeign netAlarmFeign;
    /**
     * 业务告警
     *
     */
    @PostMapping("/service/alarm")
    @ApiOperation(value = "业务告警",hidden = true)
    public void serviceAlarm(@RequestBody ServiceAlarmEvent serviceAlarmEvent) {
        netAlarmFeign.postServiceAlarmData(serviceAlarmEvent);
    }

    /**
     * 分页列表
     * @tag pauly-lyz
     */
    @PostMapping("/app/page")
    @ApiOperation(value = "小程序分页列表")
    public R<NestPage<NetAlarmRecord>> appPage(@RequestBody NetAlarmRecordPageDto qryDto) {
        return netAlarmFeign.alarmRecordPage(qryDto);
    }

    /**
     * 数量统计
     * @tag pauly-lyz
     */
    @PostMapping("/statistics")
    @ApiOperation(value = "数量统计")
    public R<NetAlarmLevelStatisticsVO> statistics(@RequestBody NetAlarmLevelStatisticsDto qryDto) {
        return netAlarmFeign.statistics(qryDto);
    }

    /**
     * 告警趋势
     * @tag pauly-lyz
     */
    @PostMapping("/trend")
    @ApiOperation(value = "告警趋势")
    public R<List<NetAlarmTrendVo>> trend(@RequestBody NetAlarmTrendDto qryDto) {
        return netAlarmFeign.trend(qryDto);
    }

    /**
     * 派工(支持批量)
     * @tag pauly-lyz
     */
    @PostMapping("/dispatched")
    @ApiOperation(value = "派工(支持批量)")
    public R dispatched(@RequestBody NetAlarmDeployDto dto) {
        return netAlarmFeign.dispatched(dto);
    }
    /**
     * 关闭(支持批量)
     * @tag pauly-lyz
     */
    @PostMapping("/close")
    @ApiOperation(value = "关闭(支持批量)")
    public R close(@RequestBody NetAlarmCloseDto dto) {
        return netAlarmFeign.close(dto);
    }
    /**
     * 详情
     * @tag pauly-lyz
     */
    @GetMapping("/detail/{id}")
    @ApiOperation(value = "详情")
    public R<NetAlarmRecord> detail(@PathVariable Long id) {
        return netAlarmFeign.detail(id);
    }
}
