package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/30 15:53
 * @Description
 */
@Data
@ApiModel(value = "IrrigatePointLocationSaveDTO")
public class SaasIrrigatePointLocationSaveDTO {

    /**
     * 点位名称
     */
    @NotNull(message = "点位名称不能为空")
    @ApiModelProperty(name = "irrigatePointLocationName", value = "点位名称")
    private String irrigatePointLocationName;
    /**
     * 区域id
     */
    @NotNull(message = "区域id不能为空")
    @ApiModelProperty(name = "irrigateAreaId", value = "区域id")
    private Long irrigateAreaId;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remarks", value = "备注信息")
    private String remarks;
    /**
     * 经度
     */
    @ApiModelProperty(name = "longitude", value = "经度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(name = "latitude", value = "纬度")
    private String latitude;
    /**
     * 地理位置
     */
    @ApiModelProperty(name = "address", value = "地理位置")
    private String address;
    /**
     * 园区点位id
     */
    @ApiModelProperty(name = "parkPointLocationId", value = "园区点位id")
    private Long parkPointLocationId;
    /**
     * 园区ID（项目id）
     */
    @NotNull(message = "项目id不能为空")
    @ApiModelProperty(name = "parkId", value = "园区ID（项目id）")
    private Long parkId;

    
   
    @ApiModelProperty(name = "planePosition", value = "点位")
    private String planePosition;
    
    /**
     * 视频ID
     */
    @ApiModelProperty(name = "videoId", value = "视频ID")
    private Long videoId;
}
