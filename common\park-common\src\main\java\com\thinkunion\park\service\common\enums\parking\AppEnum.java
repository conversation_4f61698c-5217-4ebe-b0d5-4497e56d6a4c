package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 小程序APPID枚举
 * <AUTHOR>
 */
@AllArgsConstructor
public enum AppEnum {

    HJGWH_TEST_APP("wx908e9848a2667637", "钰辰云小程序(测试)-测试小程序"),
    HJGWH_APP("wx306ce81471effaf0", "厦门火炬智慧云平台-生产小程序"),
    HJJT_APP("wxc1e73c5498c82608", "聚合场站-生产小程序"),
    BBC_APP_TEST("wx047a2873b3948afb", "泊泊车-公众号"),
    HJZHPT_APP_OFFICIAL("wx7fb754b20e275d1f", "火炬智慧平台-公众号"),
    HJGWH_APP_TEST("wx306ce81471effaf0", "厦门火炬智慧云平台-生产小程序"),
    HJJT_APP_TEST("wx306ce81471effaf0", "厦门火炬智慧云平台-生产小程序"),
    HJJTFK_APP("wxc93911f775dcf247", "火炬集团访客-生产"),
    XMHJZHPT_APP("wxedcc666fd0b2f801", "厦门火炬智慧平台-生产小程序"),
   ;

    private String code;

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(String code) {
        AppEnum[] enumArray = AppEnum.values();
        for (AppEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
