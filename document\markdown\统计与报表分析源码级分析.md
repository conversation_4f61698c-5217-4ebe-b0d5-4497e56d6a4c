# 统计与报表分析源码级分析

## 1. 主要涉及的实体类

- **PmgStatTrafficFlow**：车流量统计表，记录日/周/月/年车流量、趋势、峰值等。
- **PmgStatIncome**：收入统计表，记录各类收入来源、趋势、结构、对比等。
- **PmgStatParkingPlaceUsage**：车位利用率、周转率、满位率等统计表。
- **PmgStatParkingDuration**：车辆停留时长统计表，记录平均、分布、长时间停车等。
- **PmgLogParkingCar**、**PmgBatParkBill**：过车记录、账单表，为统计分析提供原始数据。

## 2. 主要Controller

- `PmgStatTrafficFlowController`：车流量分析接口
- `PmgStatIncomeController`：收入分析接口
- `PmgStatParkingPlaceUsageController`：车位利用率、周转率、满位率分析接口
- `PmgStatParkingDurationController`：车辆停留时长分析接口

## 3. 主要Service接口与实现

- `PmgStatTrafficFlowService`：车流量统计与分析
- `PmgStatIncomeService`：收入统计与分析
- `PmgStatParkingPlaceUsageService`：车位利用率、周转率、满位率统计与分析
- `PmgStatParkingDurationService`：车辆停留时长统计与分析

## 4. 典型方法调用链与数据流转

### 4.1 车流量分析

- 通过`PmgStatTrafficFlowController`按日/周/月/年查询车流量、趋势、峰值
- 数据来源于`PmgLogParkingCar`过车记录，定时统计写入`pmg_stat_traffic_flow`
- 支持趋势图、峰值分析、同比环比等

### 4.2 收入分析

- 通过`PmgStatIncomeController`查询各类收入（停车费、占用费、月卡、补缴等）
- 数据来源于`PmgBatParkBill`等账单表，定时统计写入`pmg_stat_income`
- 支持收入结构、趋势、对比、来源分析

### 4.3 车位利用率、周转率、满位率分析

- 通过`PmgStatParkingPlaceUsageController`查询车位利用率、周转率、满位率
- 数据来源于`PmgLogParkingCar`、`PmgBatParkingPlace`等，定时统计写入`pmg_stat_parking_place_usage`
- 支持效率优化建议输出

### 4.4 车辆停留时长分析

- 通过`PmgStatParkingDurationController`查询车辆平均停留时长、分布、长时间停车等
- 数据来源于`PmgLogParkingCar`、`PmgBatParkBill`，定时统计写入`pmg_stat_parking_duration`
- 支持分布图、异常停车预警等

## 5. 关键业务流程图示

### 5.1 车流量统计分析

```mermaid
graph TD
A[过车记录采集] --> B[定时统计]
B --> C[写入pmg_stat_traffic_flow]
C --> D[PmgStatTrafficFlowController.query]
D --> E[趋势/峰值/同比环比分析]
```

### 5.2 收入统计分析

```mermaid
graph TD
A[账单数据采集] --> B[定时统计]
B --> C[写入pmg_stat_income]
C --> D[PmgStatIncomeController.query]
D --> E[结构/趋势/对比分析]
```

### 5.3 车位利用率与效率分析

```mermaid
graph TD
A[过车/车位数据采集] --> B[定时统计]
B --> C[写入pmg_stat_parking_place_usage]
C --> D[PmgStatParkingPlaceUsageController.query]
D --> E[利用率/周转率/满位率/优化建议]
```

### 5.4 车辆停留时长分析

```mermaid
graph TD
A[过车/账单数据采集] --> B[定时统计]
B --> C[写入pmg_stat_parking_duration]
C --> D[PmgStatParkingDurationController.query]
D --> E[平均/分布/长时间停车分析]
```

## 6. 其他说明

- 统计分析支持多维度、多周期、可视化输出
- 支持定时任务、批量统计、异常预警、效率建议等扩展
- 可与大屏、报表、BI等系统集成

---
如需对某一具体方法、类或流程进一步深入源码解读，请进一步指定需求！ 