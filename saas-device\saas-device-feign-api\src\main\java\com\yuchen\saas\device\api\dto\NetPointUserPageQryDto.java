package com.yuchen.saas.device.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;

@Data
public class NetPointUserPageQryDto extends Query {

    /**
     * 所属项目id
     */
    private Long parkId;
    /**
     * 人员名称
     */
    private String userName;
    /**
     * 状态；0-关闭，1-开启
     */
    private Integer status;
    /**
     * 开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startCreateTime;
    /**
     * 结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCreateTime;

}
