package com.thinkunion.park.service.common.iot.capability.devicemodel.rules;

import com.google.gson.annotations.SerializedName;

public class MetaRule {

    private String min;
    private String max;
    private String step;
    private String precise;
    private String length;
    @SerializedName(value = "0")
    private String zero;
    @SerializedName(value = "1")
    private String one;
    private String unit;
    private String unitName;

    public String getMin() {
        return this.min;
    }

    public void setMin(String min) {
        this.min = min;
    }

    public String getMax() {
        return this.max;
    }

    public void setMax(String max) {
        this.max = max;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getLength() {
        return this.length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getZero() {
        return this.zero;
    }

    public void setZero(String zero) {
        this.zero = zero;
    }

    public String getOne() {
        return this.one;
    }

    public void setOne(String one) {
        this.one = one;
    }

    public String getStep() {
        return this.step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getPrecise() {
        return precise;
    }

    public void setPrecise(String precise) {
        this.precise = precise;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}

