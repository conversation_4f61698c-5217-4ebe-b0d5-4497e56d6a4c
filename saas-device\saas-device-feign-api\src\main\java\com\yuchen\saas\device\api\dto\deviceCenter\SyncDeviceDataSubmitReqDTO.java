package com.yuchen.saas.device.api.dto.deviceCenter;

import com.yuchen.saas.device.api.entity.deviceCenter.SaasDeviceSensor;
import com.yuchen.saas.device.api.entity.deviceCenter.SaasDeviceSmartHardware;
import com.yuchen.saas.device.api.entity.deviceCenter.SaasDeviceVideoMonitor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2024/8/28 9:38
 */
@Data
public class SyncDeviceDataSubmitReqDTO {

    /**
     * 大类（1：视频监控，2：智能硬件，3：传感设备）
     */
    @ApiModelProperty(name = "broadCategory", value = "大类（1：视频监控，2：智能硬件，3：传感设备）")
    @NotBlank(message = "硬件大类不能为空")
    private String broadCategory;

    /**
     * 视频监控
     */
    private SaasDeviceVideoMonitor saasDeviceVideoMonitor;

    /**
     * 智能硬件
     */
    private SaasDeviceSmartHardware saasDeviceSmartHardware;

    /**
     * 传感设备
     */
    private SaasDeviceSensor saasDeviceSensor;

}
