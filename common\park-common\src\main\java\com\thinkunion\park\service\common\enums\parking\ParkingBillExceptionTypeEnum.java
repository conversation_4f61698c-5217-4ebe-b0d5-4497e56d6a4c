package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 针对场中场地库异常出场处理枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum ParkingBillExceptionTypeEnum {

    //地库异常出场,地面正常结算时地库账单记录这个编码
    PRESENCE_OF_ABNORMAL_OUTPUT(1, "存在异常出场"),
    //地库异常出场,地面正常结算时地面账单记录这个编码
    VEHICLE_READY_FOR_DEPARTURE(2, "检测到车辆准备出场，关联账单存在异常，请及时处理。处理完成之后，当前账单费用将自动更新。"),
    UNCLEARED_ENTER_AGAIN(3, "存在未结清账单，再次入场"),

    ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        ParkingBillExceptionTypeEnum[] enumArray = ParkingBillExceptionTypeEnum.values();
        for (ParkingBillExceptionTypeEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
