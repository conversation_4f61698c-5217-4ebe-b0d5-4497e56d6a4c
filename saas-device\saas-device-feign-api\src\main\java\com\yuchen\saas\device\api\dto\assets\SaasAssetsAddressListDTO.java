package com.yuchen.saas.device.api.dto.assets;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nest.springwrap.core.mp.support.Query;

import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SaasAssetsAddressListDTO", description = "SaasAssetsAddressListDTO")
public class SaasAssetsAddressListDTO extends Query {
    @ApiModelProperty(name = "customerId", value = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    @ApiModelProperty(name = "addressCode", value = "资产位置编码")
    private String addressCode;
    /**
     * 资产位置名称
     */
    @ApiModelProperty(name = "addressName", value = "资产位置名称")
    private String addressName;
    /**
     * 父级id
     */
    @ApiModelProperty(name = "parentId", value = "父级id")
    private Long parentId;
}
