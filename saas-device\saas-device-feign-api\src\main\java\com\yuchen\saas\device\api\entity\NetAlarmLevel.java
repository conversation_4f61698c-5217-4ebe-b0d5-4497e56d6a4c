package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

import javax.validation.constraints.NotBlank;

/**
 * 告警级别设置
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_alarm_level")
@ApiModel(value="NetAlarmLevel对象", description="告警级别设置")
public class NetAlarmLevel extends BaseEntity {

    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerId", value = "所属客户")
    private Long customerId;
//    /**
//     * 告警名称
//     */
//    @ApiModelProperty(name = "levelName", value = "告警名称")
//    private String levelName;
    /**
     * 等级1
     */
    @ApiModelProperty(name = "levelOne", value = "等级1")
    @NotBlank(message = "等级1")
    private String levelOne;
    /**
     * 等级2
     */
    @ApiModelProperty(name = "levelTwo", value = "等级2")
    @NotBlank(message = "等级2")
    private String levelTwo;
    /**
     * 等级3
     */
    @ApiModelProperty(name = "levelThree", value = "等级3")
    @NotBlank(message = "等级3")
    private String levelThree;
    /**
     * 等级4
     */
    @ApiModelProperty(name = "levelFour", value = "等级4")
    @NotBlank(message = "等级4")
    private String levelFour;
    /**
     * 等级5
     */
    @ApiModelProperty(name = "levelFive", value = "等级5")
    @NotBlank(message = "等级5")
    private String levelFive;
    /**
     * 告警编码
     */
    @ApiModelProperty(name = "levelCode", value = "告警编码")
    private String levelCode;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 告警级别名称
     */
    @TableField(exist = false)
    @ApiModelProperty(name = "alarmLevelName", value = "告警级别名称")
    private String alarmLevelName;

    public String getAlarmLevelName(int level) {
        switch (level){
            case 1:
                return levelOne;
            case 2:
                return levelTwo;
            case 3:
                return levelThree;
            case 4:
                return levelFour;
            case 5:
                return levelFive;
            default:
                return "";
        }
    }



}
