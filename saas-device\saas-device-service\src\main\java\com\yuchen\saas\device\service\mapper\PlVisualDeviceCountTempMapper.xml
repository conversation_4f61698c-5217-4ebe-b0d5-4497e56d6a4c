<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.PlVisualDeviceCountTempMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.PlVisualDeviceCountTemp">
        <id column="id" property="id" />
        <id column="park_id" property="parkId" />
        <result column="device_total_number" property="deviceTotalNumber" />
        <result column="device_online_number" property="deviceOnlineNumber" />
        <result column="device_offline_number" property="deviceOfflineNumber" />
        <result column="device_faulty_number" property="deviceFaultyNumber" />
        <result column="device_camera_number" property="deviceCameraNumber" />
        <result column="device_sensing_number" property="deviceSensingNumber" />
        <result column="device_door_number" property="deviceDoorNumber" />
        <result column="deviceelevator_number" property="deviceelevatorNumber" />
        <result column="device_parking_number" property="deviceParkingNumber" />
        <result column="device_gateway_number" property="deviceGatewayNumber" />
        <result column="device_pass_total_number" property="devicePassTotalNumber" />
        <result column="device_pass_online_number" property="devicePassOnlineNumber" />
        <result column="device_pass_unline_number" property="devicePassUnlineNumber" />
        <result column="device_pass_malfunction_number" property="devicePassMalfunctionNumber" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user" property="createUser" />
        <result column="create_org_user" property="createOrgUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_org_user" property="updateOrgUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, park_id, device_total_number, device_online_number, device_offline_number, device_faulty_number, device_camera_number, device_sensing_number, device_door_number, deviceelevator_number, device_parking_number, device_gateway_number, device_pass_total_number, device_pass_online_number, device_pass_unline_number, device_pass_malfunction_number, tenant_id, create_user, create_org_user, create_dept, create_time, update_user, update_org_user, update_time, status, is_deleted
    </sql>

</mapper>
