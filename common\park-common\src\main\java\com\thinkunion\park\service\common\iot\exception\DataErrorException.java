package com.thinkunion.park.service.common.iot.exception;

import org.nest.springwrap.core.tool.api.DeviceCode;


public class DataErrorException extends RuntimeException {

    private static final long serialVersionUID = 534996425110290578L;
    protected String code;
    protected String message;

    public DataErrorException(DeviceCode code) {
        this.code = code.returnTextCode();
        this.message = code.getMessage();
    }

    public DataErrorException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public DataErrorException(String code, String message, Throwable cause) {
        super(cause);
        this.code = code;
        this.message = message;
    }

    public DataErrorException(String message) {
        super(message);
        this.message = message;
    }

    public DataErrorException(Throwable cause) {
        super(cause);
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
