package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核状态（0 待审核 1 审核通过 2 拒绝,3进行中）
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReviewStateEnum {


    TO_BE_REVIEWED(0, "待审核","0",0),
    PASS_THE_AUDIT(2, "审核通过","2",2),
    REFUSED(4, "拒绝","4",4),
    RUNNING(1, "进行中","1",1),
    WITHDRAWN(3, "已撤回","3",3),
   ;

    private final Integer code;


    private final String description;

    /**
     * 审批流的结果(审批流对应业务的结果)
     */
    private final String actStateCode;

    private final Integer flowStateCode;


    public static String getDescByCode(Integer code) {
        ReviewStateEnum[] enumArray = ReviewStateEnum.values();
        for (ReviewStateEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }

    /**
     * 根据审批流本身的结果转换成业务的结果
     */
    public static Integer getCodeByActStateCode(String actState) {
        ReviewStateEnum[] enumArray = ReviewStateEnum.values();
        for (ReviewStateEnum e : enumArray) {
            if (e.getActStateCode().equals(actState)) {
                return e.getCode();
            }
        }
        return null;
    }

    public static Integer getCodeByFlowStateCode(Integer nodeStatus) {
        ReviewStateEnum[] enumArray = ReviewStateEnum.values();
        for (ReviewStateEnum e : enumArray) {
            if (e.getFlowStateCode().equals(nodeStatus)) {
                return e.getCode();
            }
        }
        return null;
    }
}
