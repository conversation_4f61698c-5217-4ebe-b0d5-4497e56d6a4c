package com.yuchen.saas.device.api.dto.operatingEnd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

@Data
public class NetThirdPlatformQryReqDTO extends Query {

    /**
     * 第三方平台名称
     */
    @ApiModelProperty(name = "thirdPlatformName", value = "第三方平台名称")
    private String thirdPlatformName;
    /**
     * 对接方式
     */
    @ApiModelProperty(name = "dockingMethod", value = "对接方式")
    private String dockingMethod;
    /**
     * 消息协议
     */
    @ApiModelProperty(name = "messageProtocol", value = "消息协议")
    private String messageProtocol;

}
