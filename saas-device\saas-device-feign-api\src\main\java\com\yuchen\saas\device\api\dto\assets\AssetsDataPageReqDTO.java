package com.yuchen.saas.device.api.dto.assets;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nest.springwrap.core.mp.support.Query;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/2/27 16:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AssetsDataPageReqDTO extends Query {

    /**
     * 已选择资产数据id
     */
    private List<Long> selectedIdList;
    /**
     * 资产状态编码列表
     */
    private List<String> assetsStateCodeList;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 资产名称
     */
    @ApiModelProperty(name = "assetsName", value = "资产名称")
    private String assetsName;
    /**
     * 资产状态编码
     */
    @ApiModelProperty(name = "assetsStateCode", value = "资产状态编码")
    private String assetsStateCode;
    /**
     * 资产分类id
     */
    @ApiModelProperty(name = "assetsSortId", value = "资产分类id")
    private Long assetsSortId;
    /**
     * 资产编码
     */
    @ApiModelProperty(name = "assetsCode", value = "资产编码")
    private String assetsCode;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "equipmentCode", value = "设备编码")
    private String equipmentCode;
    /**
     * 资产品牌
     */
    @ApiModelProperty(name = "assetsBrand", value = "资产品牌")
    private String assetsBrand;
    /**
     * 资产型号
     */
    @ApiModelProperty(name = "assetsModel", value = "资产型号")
    private String assetsModel;
    /**
     * 设备序列号
     */
    @ApiModelProperty(name = "assetsEquipmentSerialNumber", value = "设备序列号")
    private String assetsEquipmentSerialNumber;
    /**
     * 资产管理员id
     */
    @ApiModelProperty(name = "assetsAdminUserId", value = "资产管理员id")
    private Long assetsAdminUserId;
    /**
     * 订单号
     */
    @ApiModelProperty(name = "assetsOrderNo", value = "订单号")
    private String assetsOrderNo;
    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "assetsSupplierName", value = "供应商名称")
    private String assetsSupplierName;
    /**
     * 使用状态编码
     */
    @ApiModelProperty(name = "assetsUseStatusCode", value = "使用状态编码")
    private String assetsUseStatusCode;
    /**
     * 购置方式编码
     */
    @ApiModelProperty(name = "assetsPurchaseModelCode", value = "购置方式编码")
    private String assetsPurchaseModelCode;
    /**
     * 购置/起租开始日期
     */
    @ApiModelProperty(name = "assetsPurchaseStartDate", value = "购置/起租日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsPurchaseStartDate;
    /**
     * 购置/起租结束日期
     */
    @ApiModelProperty(name = "assetsPurchaseEndDate", value = "购置/起租结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsPurchaseEndDate;
    /**
     * 维保到期开始日期
     */
    @ApiModelProperty(name = "assetsMaintenanceExpireStartDate", value = "维保到期开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsMaintenanceExpireStartDate;
    /**
     * 维保到期结束日期
     */
    @ApiModelProperty(name = "assetsMaintenanceExpireEndDate", value = "维保到期结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsMaintenanceExpireEndDate;
    /**
     * 所属位置id
     */
    @ApiModelProperty(name = "assetsLocationId", value = "所属位置id")
    private Long assetsLocationId;
    /**
     * 使用部门id
     */
    @ApiModelProperty(name = "assetsUseDeptId", value = "使用部门id")
    private Long assetsUseDeptId;
    /**
     * 硬盘
     */
    @ApiModelProperty(name = "assetsDisk", value = "硬盘")
    private String assetsDisk;
    /**
     * 内存
     */
    @ApiModelProperty(name = "assetsInternal", value = "内存")
    private String assetsInternal;
    /**
     * CPU型号
     */
    @ApiModelProperty(name = "assetsCpuModel", value = "CPU型号")
    private String assetsCpuModel;
    /**
     * 绑定智能硬件（0：否，1：是）
     */
    @ApiModelProperty(name = "bindIntelligentHardware", value = "绑定智能硬件（0：否，1：是）")
    private String bindIntelligentHardware;
    /**
     * 生产厂商
     */
    @ApiModelProperty(name = "manufacturer", value = "生产厂商")
    private String manufacturer;
    /**
     * 使用人id
     */
    @ApiModelProperty(name = "assetsUserId", value = "使用人id")
    private Long assetsUserId;
    /**
     * 领用开始日期
     */
    @ApiModelProperty(name = "assetsClaimingStartDate", value = "领用开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsClaimingStartDate;
    /**
     * 领用结束日期
     */
    @ApiModelProperty(name = "assetsClaimingEndDate", value = "领用结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsClaimingEndDate;

}
