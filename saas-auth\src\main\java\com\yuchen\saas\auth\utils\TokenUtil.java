package com.yuchen.saas.auth.utils;

import com.thinkunion.park.service.common.constant.TenantConstant;
import com.yuchen.saas.manage.system.api.entity.Tenant;
import lombok.SneakyThrows;
import org.nest.springwrap.core.launch.constant.TokenConstant;
import org.nest.springwrap.core.tenant.NestTenantProperties;
import org.nest.springwrap.core.tool.constant.NestConstant;
import org.nest.springwrap.core.tool.jackson.JsonUtil;
import org.nest.springwrap.core.tool.utils.*;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.oauth2.common.exceptions.UnapprovedClientAuthenticationException;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;

import java.util.Base64;
import java.util.Calendar;
import java.util.Date;

public class TokenUtil {

    public final static String AVATAR = TokenConstant.AVATAR;
    public final static String ACCOUNT = TokenConstant.ACCOUNT;
    public final static String USER_NAME = TokenConstant.USER_NAME;
    public final static String NICK_NAME = TokenConstant.NICK_NAME;
    public final static String REAL_NAME = TokenConstant.REAL_NAME;
    public final static String USER_ID = TokenConstant.USER_ID;
    public final static String DEPT_ID = TokenConstant.DEPT_ID;
    public final static String POST_ID = TokenConstant.POST_ID;
    public final static String ROLE_ID = TokenConstant.ROLE_ID;
    public final static String TENANT_ID = TokenConstant.TENANT_ID;
    public final static String ROLE_NAME = TokenConstant.ROLE_NAME;
    public final static String PARK_ID = TokenConstant.PARK_ID;
    public final static String OAUTH_ID = TokenConstant.OAUTH_ID;
    public final static String CLIENT_ID = TokenConstant.CLIENT_ID;
    public final static String DETAIL = TokenConstant.DETAIL;
    public final static String LICENSE = TokenConstant.LICENSE;
    public final static String LICENSE_NAME = TokenConstant.LICENSE_NAME;
    public final static String USER_INFO_ID = TokenConstant.USER_INFO_ID;
    public final static String IS_ADMIN = TokenConstant.IS_ADMIN;
    public final static String MENU_IDS = TokenConstant.MENU_IDS;
    public final static String DEPT_ROLE_IDS = TokenConstant.DEPT_ROLE_IDS;
    public final static String USER_DETAIL_ID = TokenConstant.USER_DETAIL_ID;
    public final static String ORG_USER_ID = TokenConstant.ORG_USER_ID;
    public final static String CUSTOMER_MANAGE_ID = TokenConstant.CUSTOMER_MANAGE_ID;
    public final static String IS_SUPER_ADMIN = TokenConstant.IS_SUPER_ADMIN;
    public final static String ID_ENTITY = TokenConstant.ID_ENTITY;
    public final static Integer IS_ADMIN_VALUE =1;
    public final static String CAPTCHA_HEADER_KEY = "Captcha-Key";
    public final static String CAPTCHA_HEADER_CODE = "Captcha-Code";
    public final static String CAPTCHA_NOT_CORRECT = "验证码不正确";
    public final static String TENANT_HEADER_KEY = "Tenant-Id";
    public final static String TENANT_PARAM_KEY = "tenant_id";
    public final static String DEFAULT_TENANT_ID = "108994";
    public final static String TENANT_NOT_FOUND = "租户ID未找到";
    public final static String USER_TYPE_HEADER_KEY = "User-Type";
    public final static String DEFAULT_USER_TYPE = "web";
    public final static String USER_NOT_FOUND = "用户名或密码错误";
    public final static String USER_HAS_NO_ROLE = "未获得用户的角色信息";
    public final static String USER_HAS_NO_TENANT = "未获得用户的租户信息";
    public final static String USER_HAS_NO_TENANT_PERMISSION = "租户授权已过期,请联系管理员";
    public final static String HEADER_KEY = "Authorization";
    public final static String HEADER_PREFIX = "Basic ";
    public final static String DEFAULT_AVATAR = "";
    @Deprecated
    public final static String ADMIN_DEPT="1123598813738675201";
    private static NestTenantProperties tenantProperties;


    private static NestTenantProperties getTenantProperties() {
        if (tenantProperties == null) {
            tenantProperties = SpringUtil.getBean(NestTenantProperties.class);
        }
        return tenantProperties;
    }


    @SneakyThrows
    public static String[] extractAndDecodeHeader() {
        String header = WebUtil.getRequest().getHeader(TokenUtil.HEADER_KEY);
        if (header == null || !header.startsWith(TokenUtil.HEADER_PREFIX)) {
            throw new UnapprovedClientAuthenticationException("请求头中无client信息");
        }

        byte[] base64Token = header.substring(6).getBytes(Charsets.UTF_8_NAME);

        byte[] decoded;
        try {
            decoded = Base64.getDecoder().decode(base64Token);
        } catch (IllegalArgumentException var7) {
            throw new BadCredentialsException("Failed to decode basic authentication token");
        }

        String token = new String(decoded, Charsets.UTF_8_NAME);
        int index = token.indexOf(StringPool.COLON);
        if (index == -1) {
            throw new BadCredentialsException("Invalid basic authentication token");
        } else {
            return new String[]{token.substring(0, index), token.substring(index + 1)};
        }
    }


    public static String getClientIdFromHeader() {
        String[] tokens = extractAndDecodeHeader();
        return tokens[0];
    }


    public static int getTokenValiditySecond() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 3);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (int) (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    public static int getRefreshTokenValiditySeconds() {
        return 60 * 60 * 24 * 15;
    }

    public static boolean judgeTenant(Tenant tenant) {
        if (tenant == null || tenant.getId() == null) {
            throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_TENANT);
        }
        if (StringUtil.equalsIgnoreCase(tenant.getTenantId(), NestConstant.ADMIN_TENANT_ID)) {
            return false;
        }
        Date expireTime = tenant.getExpireTime();
        if (getTenantProperties().getLicense()) {
            String licenseKey = tenant.getLicenseKey();
            String decrypt = DesUtil.decryptFormHex(licenseKey, TenantConstant.DES_KEY);
            expireTime = JsonUtil.parse(decrypt, Tenant.class).getExpireTime();
        }
        if (expireTime != null && expireTime.before(DateUtil.now())) {
            throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
        }
        return false;
    }
}
