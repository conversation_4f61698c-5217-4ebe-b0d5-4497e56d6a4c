<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.net.NetAlarmRecordMapper">
    <select id="levelStatistics" resultType="com.yuchen.saas.device.api.vo.NetAlarmLevelStatisticsVO">
        SELECT
            COALESCE(SUM(CASE WHEN alarm_level = 1 THEN 1 ELSE 0 END), 0) AS level_one_count,
            COALESCE(SUM(CASE WHEN alarm_level = 2 THEN 1 ELSE 0 END), 0) AS level_two_count,
            COALESCE(SUM(CASE WHEN alarm_level = 3 THEN 1 ELSE 0 END), 0) AS level_three_count,
            COALESCE(SUM(CASE WHEN alarm_level = 4 THEN 1 ELSE 0 END), 0) AS level_four_count,
            COALESCE(SUM(CASE WHEN alarm_level = 5 THEN 1 ELSE 0 END), 0) AS level_five_count,
            COALESCE(SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END), 0) AS pending_count,
            COALESCE(SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END), 0) AS processed_count,
            COUNT(1) AS total
        FROM net_alarm_record
        <where>
            <if test="beginTime != null">
                AND alarm_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                AND alarm_time &lt;= #{endTime}
            </if>
            <if test="customerManageId != null">
                AND customer_manage_id = #{customerManageId}
            </if>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
    </select>
    <select id="todayCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            net_alarm_record
        Where DATE(alarm_time) = CURDATE()
        <if test="customerManageId != null">
            AND customer_manage_id = #{customerManageId}
        </if>
        <if test="projectId != null">
            AND project_id = #{projectId}
        </if>
    </select>
    <select id="trendYear" resultType="com.yuchen.saas.device.api.vo.NetAlarmTrendVo">
        SELECT
        COALESCE(t1.alarm_count, 0) AS alarm_count,
        YEAR(t2.date) AS year,
        MONTH(t2.date) AS month
        FROM (
        SELECT
        DATE_FORMAT(#{beginTime}, '%Y-%m-01') + INTERVAL (v * 10 + u) MONTH AS date
        FROM
        (SELECT 0 v UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) v,
        (SELECT 0 u UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) u
        WHERE
        DATE_FORMAT(#{beginTime}, '%Y-%m-01') + INTERVAL (v * 10 + u) MONTH &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-01')
        ) t2
        LEFT JOIN (
        SELECT
        COUNT(1) AS alarm_count,
        YEAR(alarm_time) AS year,
        MONTH(alarm_time) AS month
        FROM
        net_alarm_record
        WHERE
        alarm_time BETWEEN #{beginTime} AND #{endTime}
        <if test="customerManageId != null">
            AND customer_manage_id = #{customerManageId}
        </if>
        <if test="serviceId != null">
            AND service_id = #{serviceId}
        </if>
        <if test="alarmSource != null">
            AND alarm_source = #{alarmSource}
        </if>
        <if test="projectId != null">
            AND project_id = #{projectId}
        </if>
        GROUP BY
        YEAR(alarm_time),
        MONTH(alarm_time)
        ) t1 ON t1.year = YEAR(t2.date) AND t1.month = MONTH(t2.date)
        ORDER BY
        t2.date
    </select>
    <select id="trendMonth" resultType="com.yuchen.saas.device.api.vo.NetAlarmTrendVo">
        SELECT
        IFNULL(t1.alarm_count, 0) AS alarm_count,
        t2.year,
        t2.month,
        t2.day
        FROM
        (
        SELECT
        COUNT(1) AS alarm_count,
        YEAR(alarm_time) AS year,
        MONTH(alarm_time) AS month,
        DAY(alarm_time) AS day
        FROM
        net_alarm_record
        WHERE
        alarm_time BETWEEN #{beginTime} AND #{endTime}
        <if test="customerManageId != null">
            AND customer_manage_id = #{customerManageId}
        </if>
        <if test="serviceId != null">
            AND service_id = #{serviceId}
        </if>
        <if test="alarmSource != null">
            AND alarm_source = #{alarmSource}
        </if>
        <if test="projectId != null">
            AND project_id = #{projectId}
        </if>
        GROUP BY
        YEAR(alarm_time),
        MONTH(alarm_time),
        DAY(alarm_time)
        ) t1
        RIGHT JOIN
        (
        SELECT
        YEAR(date) AS year,
        MONTH(date) AS month,
        DAY(date) AS day
        FROM
        (
        SELECT
        DATE_ADD(#{beginTime}, INTERVAL (units.i + tens.i * 10 + hundreds.i * 100) DAY) AS date
        FROM
        (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) units,
        (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) tens,
        (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) hundreds
        WHERE
        DATE_ADD(#{beginTime}, INTERVAL (units.i + tens.i * 10 + hundreds.i * 100) DAY) &lt;= #{endTime}
        ) dates
        ) t2
        ON t1.year = t2.year AND t1.month = t2.month AND t1.day = t2.day
        ORDER BY
        t2.year, t2.month, t2.day
    </select>
    <select id="trendDay" resultType="com.yuchen.saas.device.api.vo.NetAlarmTrendVo">
        SELECT
        COALESCE(alarm_count, 0) AS alarm_count,
        YEAR(ts) AS year,
        MONTH(ts) AS month,
        DAY(ts) AS day,
        HOUR(ts) AS hour
        FROM (
        -- 生成时间序列
        SELECT
        DATE(#{beginTime}) + INTERVAL (h * 24 + i) HOUR AS ts
        FROM
        (SELECT 0 h UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) h,
        (SELECT 0 i UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20 UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23) i
        WHERE
        DATE(#{beginTime}) + INTERVAL (h * 24 + i) HOUR &lt;= DATE(#{endTime}) + INTERVAL 23 HOUR
        ) ts
        LEFT JOIN (
        SELECT
        COUNT(1) AS alarm_count,
        YEAR(alarm_time) AS year,
        MONTH(alarm_time) AS month,
        DAY(alarm_time) AS day,
        HOUR(alarm_time) AS hour
        FROM
        net_alarm_record
        WHERE
        alarm_time BETWEEN #{beginTime} AND #{endTime}
        <if test="customerManageId != null">
            AND customer_manage_id = #{customerManageId}
        </if>
        <if test="serviceId != null">
            AND service_id = #{qryDto.serviceId}
        </if>
        <if test="alarmSource != null">
            AND alarm_source = #{alarmSource}
        </if>
        <if test="projectId != null">
            AND project_id = #{projectId}
        </if>
        GROUP BY
        YEAR(alarm_time),
        MONTH(alarm_time),
        DAY(alarm_time),
        HOUR(alarm_time)
        ) alarms ON
        YEAR(ts) = alarms.year AND
        MONTH(ts) = alarms.month AND
        DAY(ts) = alarms.day AND
        HOUR(ts) = alarms.hour
        ORDER BY
        ts;
    </select>
    <select id="barGraph" resultType="com.yuchen.saas.device.api.vo.NetAlarmBarGraphVO">
        SELECT
            COALESCE(SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END), 0) AS pending_count,
            COALESCE(SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END), 0) AS processed_count,
            SUM(1) AS total_count,
            service_id AS service_id
        FROM
            net_alarm_record
        Where alarm_time BETWEEN #{beginTime} AND #{endTime}
        <if test="customerManageId != null">
            AND customer_manage_id = #{customerManageId}
        </if>
        <if test="projectId != null">
            AND project_id = #{projectId}
        </if>
        group by service_id
    </select>
    <select id="pieChart" resultType="com.yuchen.saas.device.api.vo.NetAlarmServiceCountVO">
        SELECT
            SUM(1) AS total_count,
            service_id AS service_id
        FROM
            net_alarm_record
        Where alarm_time BETWEEN #{beginTime} AND #{endTime}
        <if test="customerManageId != null">
            AND customer_manage_id = #{customerManageId}
        </if>
        <if test="projectId != null">
            AND project_id = #{projectId}
        </if>
        group by service_id
    </select>
    <select id="pageByDay" resultType="com.yuchen.saas.device.api.vo.NetAlarmStatisticsPageVO">
        SELECT
            service_id AS service_id,
            DATE(alarm_time) AS time,
            COALESCE(SUM(CASE WHEN alarm_level = 1 THEN 1 ELSE 0 END), 0) AS level_one_count,
            COALESCE(SUM(CASE WHEN alarm_level = 2 THEN 1 ELSE 0 END), 0) AS level_two_count,
            COALESCE(SUM(CASE WHEN alarm_level = 3 THEN 1 ELSE 0 END), 0) AS level_three_count,
            COALESCE(SUM(CASE WHEN alarm_level = 4 THEN 1 ELSE 0 END), 0) AS level_four_count,
            COALESCE(SUM(CASE WHEN alarm_level = 5 THEN 1 ELSE 0 END), 0) AS level_five_count,
            COALESCE(SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END), 0) AS pending_count,
            COALESCE(SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END), 0) AS closed_count,
            COALESCE(SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END), 0) AS processed_count
        FROM
            net_alarm_record
        Where alarm_time BETWEEN #{qryDto.beginTime} AND #{qryDto.endTime}
        <if test="qryDto.customerManageId != null">
            AND customer_manage_id = #{qryDto.customerManageId}
        </if>
        <if test="qryDto.projectId != null">
            AND project_id = #{qryDto.projectId}
        </if>
        group by service_id,DATE(alarm_time)
    </select>
    <select id="countAllService" resultType="com.yuchen.saas.device.api.vo.NetAlarmCountAllServiceVO">
        SELECT
            service_id AS serviceId,
            COUNT(1) AS count
        FROM
            net_alarm_record
        group by service_id
    </select>
</mapper>