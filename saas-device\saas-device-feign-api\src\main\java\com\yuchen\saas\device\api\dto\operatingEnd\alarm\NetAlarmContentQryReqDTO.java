package com.yuchen.saas.device.api.dto.operatingEnd.alarm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

/**
 * <AUTHOR>
 * @create 2024/6/25 10:42
 */
@Data
public class NetAlarmContentQryReqDTO extends Query {

    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerId", value = "所属客户")
    private Long customerId;
    /**
     * 告警内容
     */
    @ApiModelProperty(name = "alarmContent", value = "告警内容")
    private String alarmContent;
    /**
     * 告警内容编码
     */
    @ApiModelProperty(name = "alarmContentCode", value = "告警内容编码")
    private String alarmContentCode;
    /**
     * 告警内容分类（1：物联告警、2：设备告警、3：业务告警）
     */
    @ApiModelProperty(name = "alarmContentCategory", value = "告警内容分类（1：物联告警、2：设备告警、3：业务告警）")
    private String alarmContentCategory;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "deviceCategoryId", value = "设备类型")
    private Long deviceCategoryId;
    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
}
