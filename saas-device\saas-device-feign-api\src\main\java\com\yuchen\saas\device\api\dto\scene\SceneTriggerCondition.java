package com.yuchen.saas.device.api.dto.scene;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 触发器对象
 */
@Data
public class SceneTriggerCondition {

    /**
     * 产品key
     */
    @NotNull(message = "产品不能为空", groups = {Default.class})
    private String productKey;

    /**
     * 设备名称 (全部设备传 _all)
     */
    @NotNull(message = "设备不能为空", groups = {Default.class})
    private String deviceName;

    /**
     * 触发方式 property-属性触发，event-事件触发，deviceStatusChange-上下线触发
     */
    @NotNull(message = "触发方式不能为空", groups = {Default.class})
    @Pattern(regexp = "property|event|deviceStatusChange", message = "触发方式：property，event，deviceStatusChange", groups = {Default.class})
    private String uri;

    /**
     * 属性名称（全部传 _all）
     */
    @NotNull(message = "属性名不能为空", groups = {Property.class, Event.class})
    private String propertyName;

    /**
     * 事件（全部传 _all）
     */
    @NotNull(message = "事件名不能为空", groups = {Event.class})
    private String eventCode;

    /**
     * NetDataTypeEnum
     * 参数类型
     * 1：int(整数型)
     * 2：long(长整数型)
     * 3：float(单精度浮点型)
     * 4：double(双精度浮点型)
     * 5：text(字符串)
     * 6：bool(布尔型)
     * 7：time(时间类型)
     */
    @NotNull(message = "参数类型不能为空", groups = {Property.class, Event.class})
    private Integer propMode;

    /**
     * NetCompareTypeEnum
     * 比较符
     * 1：等于
     * 2：不等于
     * 3：大于
     * 4：大于等于
     * 5：小于
     * 6：小于等于
     */
    @NotNull(message = "比较符不能为空", groups = {Property.class, Event.class})
    private Integer compareType;

    /**
     * 比较值
     */
    @NotNull(message = "比较符不能为空", groups = {Property.class, Event.class})
    private String compareValue;

    /**
     * 上下线状态 0 上下线，1 上线，2 下线
     */
    @NotNull(message = "上下线状态不能为空", groups = {Status.class})
    private Integer onlineStatus;

    public interface Default {
    }
    public interface Property {
    }

    public interface Event {
    }

    public interface Status {
    }


}
