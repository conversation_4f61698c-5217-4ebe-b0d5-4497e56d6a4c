package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


@Data
public class SaasIrrigateTaskCloseDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID")
    @NotNull(message = "主键ID不能为空")
    private Long id;
    /**
     * 关闭原因(1:天气原因 2:养护调整 3:其他)
     */
    @ApiModelProperty(name = "closeReason", value = "关闭原因(1:天气原因 2:养护调整 3:其他)")
    @NotNull(message = "关闭原因不能为空")
    private Integer closeReason;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

}
