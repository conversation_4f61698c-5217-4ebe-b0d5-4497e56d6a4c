package com.yuchen.saas.device.api.entity.scene;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_scene_record")
@ApiModel(value="NetSceneRecord对象", description="场景联动记录表")
public class NetSceneRecord extends BaseEntity {

    /**
     * 所属客户
     */
    private Long customerId;

    /**
     * 场景联动id
     */
    private Long sceneId;

    /**
     * 场景类型，1-设备触发，2-定时触发，3-手动触发
     */
    private String triggerMode;

    /**
     * 执行状态，1-成功，0-失败
     */
    private String executeStatus;

    /**
     * 触发条件json
     */
    private String triggerConditionJson;

    /**
     * 执行动作json
     */
    private String executeActionJson;

    /**
     * 执行条件json
     */
    private String executeConditionJson;


}
