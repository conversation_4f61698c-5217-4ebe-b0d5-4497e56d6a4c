package com.yuchen.saas.device.api.dto.assets.client;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2024/4/29 13:54
 */
@Data
public class AddInspectRepairDataDTO {

    /**
     * 报修类型(1:设备故障，2：外观损害，3：联网异常，4：其他故障)
     */
    @ApiModelProperty(name = "breakdownType", value = "报修类型(1:设备故障，2：外观损害，3：联网异常，4：其他故障)")
    @NotBlank(message = "请选择报修类型")
    private String breakdownType;
    /**
     * 报修人名称
     */
    @ApiModelProperty(name = "repairUserName", value = "报修人名称")
    @NotBlank(message = "报修人不能为空")
    private String repairUserName;
    /**
     * 联系电话
     */
    @ApiModelProperty(name = "phone", value = "联系电话")
    @NotBlank(message = "联系电话不能为空")
    private String phone;

    /**
     * 设备编码
     */
    @ApiModelProperty(name = "equipmentCode", value = "设备编码")
    @NotBlank(message = "设备编码不能为空")
    private String equipmentCode;

    /**
     * 设备资产明细,跟后台存一样的数组数据，字段也一样，方便统一查询
     */
    @NotBlank(message = "设备数据不能为空")
    private String assetsData;

    /**
     * 备注信息(报修内容)
     */
    @ApiModelProperty(name = "repairRemark", value = "备注信息(报修内容)")
    private String repairRemark;
    /**
     * 附件图片地址 逗号隔开
     */
    @ApiModelProperty(name = "repairFile", value = "附件图片地址 逗号隔开")
    private String repairFile;
}
