package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/30 15:53
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "IrrigateAreaUpdateDTO")
public class SaasIrrigateAreaUpdateDTO extends SaasIrrigateAreaSaveDTO {

    /**
     * id
     */
    @ApiModelProperty(name = "id", value = "id")
    @NotNull(message = "id不能为空")
    private Long id;

}
