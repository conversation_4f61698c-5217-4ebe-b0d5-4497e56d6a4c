package com.yuchen.saas.device.api.dto.assets;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "SaasAssetsCategoryUpdateSortDTO", description = "SaasAssetsCategoryUpdateSortDTO")
public class SaasAssetsCategoryUpdateSortDTO {
    @ApiModelProperty(name = "id", value = "id")
    @NotNull(message="id不能为空")
    private Long id;

    /**
     * 排序
     */
    @ApiModelProperty(name = "sort", value = "排序")
    @NotNull(message="排序不能为空")
    private Integer sort;


}
