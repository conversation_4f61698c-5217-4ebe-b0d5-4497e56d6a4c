package com.yuchen.saas.device.service.iot;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class IotMqttConfig {
    @Value("${iot.mqtt.uri}")
    private String uri;

    @Value("${iot.mqtt.access-key}")
    private String accessKey;

    @Value("${iot.mqtt.access-secret}")
    private String accessSecret;

    @Value("${iot.openapi.uri}")
    private String openapiUri;

    @Value("${iot.openapi.access-key}")
    private String openapiAccessKey;

    @Value("${iot.openapi.access-secret}")
    private String openapiAccessSecret;

    @Value("${iot.mqtt.shareGroupName}")
    private String shareGroupName;

    @Override
    public String toString() {
        return "IotMqttConfig{" +
                "uri='" + uri + '\'' +
                ", accessKey='" + accessKey + '\'' +
                ", accessSecret='" + accessSecret + '\'' +
                ", openapiUri='" + openapiUri + '\'' +
                ", openapiAccessKey='" + openapiAccessKey + '\'' +
                ", openapiAccessSecret='" + openapiAccessSecret + '\'' +
                ", shareGroupName='" + shareGroupName + '\'' +
                '}';
    }
}