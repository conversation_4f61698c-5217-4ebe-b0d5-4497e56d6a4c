# 车辆出入场管理源码级分析

## 1. 主要涉及的实体类

- **PmgLogParkingCar**：过车记录表，记录每次车辆进出场、压线、放行等事件。
- **PmgBatParkBill**：停车账单表，记录车辆在场期间的费用、结算状态等。
- **PmgBatParkParkingConnectDevice**：设备绑定表，关联车道与闸机、LED、语音等设备。
- **ParkingContext / IoTEvent**：事件上下文与物联网事件数据，贯穿整个出入场流程。

## 2. 主要Controller

- `ParkingEnterEventHandler`（入场事件处理）
- `ParkingExitEventHandler`（出场事件处理）
- 相关控制器如`ParkingController`、`PmgLogParkingCarController`等用于查询过车记录、账单等

## 3. 主要Service接口与实现

- `ParkingGateClient`/`ParkingGateClientImpl`：闸机、LED、语音等设备的联动控制
- `ParkingChargeSettleService`：账单结算、费用校验、特殊放行等
- `ParkingCacheManager`：缓存车牌、放行状态、过车事件等
- `ParkingVehiclePassLogService`：过车记录的保存与查询
- `ParkingBillService`：账单生成、查询、更新
- `ParkingPlaceService`：车位分配与释放

## 4. 典型方法调用链与数据流转

### 4.1 车牌识别与过车记录

1. 入口/出口设备识别到车牌，生成IoTEvent
2. 通过`ParkingEnterEventHandler.handle`或`ParkingExitEventHandler.handle`处理事件
3. 解析车牌、事件类型，构建`ParkingContext`
4. 生成或更新`PmgLogParkingCar`过车记录，保存到数据库
5. 对无牌车、异常情况有特殊处理逻辑（如临时编号、人工确认等）

### 4.2 入场权限验证

- 在`ParkingEnterEventHandler.handle`中：
  - 检查黑名单/白名单
  - 检查是否有未结算账单（如有则禁止入场）
  - 检查月卡、错时卡、特殊车辆等权限
  - 生成`EnterPassTicket`，决定是否允许入场

### 4.3 入场开闸控制

- 若允许入场，调用`ParkingGateClient.sendGateOpen`下发开闸指令
- 同步调用`sendLedByTicketNew`、`sendVoiceByTicketNew`推送LED和语音提示
- 记录开闸事件到缓存和过车日志

### 4.4 出场权限与费用校验

- 在`ParkingExitEventHandler.handle`中：
  - 检查账单是否已结算、是否在免费时段
  - 检查特殊放行权限（如测试模式、人工放行等）
  - 生成`ExitPassTicket`，决定是否允许出场
  - 若允许，调用`ParkingGateClient.sendGateOpen`开闸

### 4.5 出场开闸与关闸

- 开闸：同入场，调用`sendGateOpen`，并推送LED/语音
- 关闸：通常由设备自动完成，系统不主动下发关闸指令
- 记录出场事件、释放车位、终结账单

### 4.6 事件推送

- 通过`pushParkingLogNotification`、`publisher.publishEvent`等方式：
  - 推送过车、开闸、放行等事件到前端、消息中心、第三方系统
  - 支持WebSocket、消息队列等多种推送方式

## 5. 关键业务流程图示

### 5.1 入场流程

```mermaid
graph TD
A[入口设备识别车牌] --> B[ParkingEnterEventHandler.handle]
B --> C[权限校验/生成通行票]
C --> D{允许入场?}
D -- 否 --> E[拒绝入场, 记录日志]
D -- 是 --> F[开闸, LED/语音提示]
F --> G[保存过车记录]
G --> H[推送入场事件]
```

### 5.2 出场流程

```mermaid
graph TD
A[出口设备识别车牌] --> B[ParkingExitEventHandler.handle]
B --> C[账单/权限校验]
C --> D{允许出场?}
D -- 否 --> E[拒绝出场, 记录日志]
D -- 是 --> F[开闸, LED/语音提示]
F --> G[保存过车记录, 释放车位, 终结账单]
G --> H[推送出场事件]
```

### 5.3 过车记录与事件推送

```mermaid
graph TD
A[过车事件发生] --> B[生成/更新PmgLogParkingCar]
B --> C[保存数据库]
C --> D[推送消息到前端/消息中心/第三方]
```

## 6. 其他说明

- 代码中对无牌车、异常事件有专门分支处理，保证业务闭环
- 设备联动通过`ParkingGateClient`统一封装，便于扩展和维护
- 事件推送机制支持多通道，便于与外部系统集成

---
如需对某一具体方法、类或流程进一步深入源码解读，请进一步指定需求！ 