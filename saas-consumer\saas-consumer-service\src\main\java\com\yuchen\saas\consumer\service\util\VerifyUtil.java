package com.yuchen.saas.consumer.service.util;

import java.util.regex.Pattern;

/**
 * @Author: 张逸飞
 * @Date: 2021/4/21 10:04
 * @Description: 验证工具类
 */
public class VerifyUtil {

    /**
     * 根据输入的车牌号判断是否正确
     * @param plateNo 车牌号
     * @return boolean true:是  false:否
     */
    public static boolean checkPlateNumberFormat(String plateNo) {
        String pattern = "([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])" +
                "[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1" +
                "})";
        return Pattern.matches(pattern, plateNo);
    }

    /**
     * 判断是否是手机号
     * @param tel 手机号
     * @return boolean true:是  false:否
     */
    public static boolean isMobile(String tel) {
        String pattern = "((\\+86|0086)?\\s*)((134[0-8]\\d{7})|(((13([0-3]|[5-9]))|(14[5-9])|15([0-3]|[5-9])|(16" +
                "(2|[5-7]))|17([0-3]|[5-8])|18[0-9]|19(1|[8-9]))\\d{8})|(14(0|1|4)0\\d{7})|(1740([0-5]|[6-9]|[10-12])" +
                "\\d{7}))";
        return Pattern.matches(pattern, tel);
    }
}
