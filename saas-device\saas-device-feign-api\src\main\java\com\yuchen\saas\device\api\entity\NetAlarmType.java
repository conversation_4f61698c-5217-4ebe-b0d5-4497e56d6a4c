package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 告警类型
 */
@Data
@Accessors(chain = true)
@TableName("net_alarm_type")
@ApiModel(value="NetAlarmType对象", description="告警类型表")
public class NetAlarmType {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(name = "id", value = "id主键")
    private Long id;

    /**
     * 告警分类; 3-业务告警 4-第三方告警
     */
    @NotNull(message = "告警分类不能为空")
    @ApiModelProperty(name = "category", value = "告警分类; 3-业务告警 4-第三方告警")
    private Integer category;

    /**
     * 告警类型名称
     */
    @NotNull(message = "告警类型名称不能为空")
    @ApiModelProperty(name = "alarmTypeName", value = "告警类型名称")
    private String alarmTypeName;

    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空")
    @ApiModelProperty(name = "alarmCode", value = "唯一标识不能为空")
    private String alarmCode;
    /**
     * 项目id,多选,隔开
     */
    @ApiModelProperty(name = "projectIds", value = "项目id,多选,隔开")
    private String projectIds;
    /**
     * 服务应用id
     */
    @ApiModelProperty(name = "serviceId", value = "服务应用id")
    private Long serviceId;
    /**
     * 第三方平台id
     */
    @ApiModelProperty(name = "thirdPartyId", value = "第三方平台id")
    private Long thirdPartyId;
    /**
     * 接口
     */
    @ApiModelProperty(name = "interface_value", value = "接口")
    private String interface_value;

//    /**
//     * 关联其它业务
//     */
//    private String businessIds;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 状态; 0-停用，1-启用
     */
    @ApiModelProperty(name = "status", value = "状态; 0-停用，1-启用")
    private Integer status;

    /**
     * 是否删除，0-否，1-是
     */
    @TableLogic
    @ApiModelProperty(name = "isDeleted", value = "是否删除，0-否，1-是")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateTime", value = "更新时间")
    private Date updateTime;

}
