package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.thinkunion.park.service.common.entity.BasicEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: 宋少挺
 * @CreateTime: 2022/10/13 15:24
 * @Description: 设备开关组规则
 * @Version: 1.0
 */
@Data
@ApiModel(value = "设备开关组规则 数据表", description = "设备开关组规则数据表")
@TableName("device_switch_group_rule")
public class DeviceSwitchGroupRule extends BasicEntity {

    /**
     * 规则名称
     */
    @ApiModelProperty(name = "name", value = "规则名称")
    private String name;
    /**
     * 开始日期
     */
    @ApiModelProperty(name = "startDate", value = "开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    /**
     * 结束日期
     */
    @ApiModelProperty(name = "endDate", value = "结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    /**
     * 开点
     */
    @ApiModelProperty(name = "openTime", value = "开点")
    private String openTime;
    /**
     * 关点
     */
    @ApiModelProperty(name = "closeTime", value = "关点")
    private String closeTime;

}