Java 项目开发规范
1. 代码风格
1.1 命名规范
包名：全小写，使用反域名风格，如 com.yuchen.saas.manage.user
类名：大驼峰（PascalCase），如 UserDeptServiceImpl
方法名：小驼峰（camelCase），如 listByDeptId
变量名：小驼峰（camelCase），如 userDeptDto
常量名：全大写，单词间下划线分隔，如 MAX_SIZE
接口名：以 I 开头，如 IUserDeptService
布尔变量：以 is、has、can 等开头，如 isAdmin
1.2 缩进与排版
使用4个空格缩进，不使用Tab。
每行不超过120个字符，必要时换行。
大括号 {} 单独成行，if/else/for/while/方法等均如此。
方法之间空一行。
1.3 空格与换行
关键字与括号之间加空格，如 if (condition) {
运算符两边加空格，如 a = b + c;
方法参数较多时，建议多行排列，每个参数单独一行。

2.4 版权与作者信息
重要类文件头部可加版权、作者、日期等信息。
3. 代码规范
3.1 结构规范
每个类只负责单一职责（SRP）。
Service 层不直接操作数据库，统一通过 Mapper 层。
Controller 层只做参数校验、调用 Service、返回结果。
3.2 事务管理
涉及多表或多步操作时，务必加上 @Transactional 注解。
只在 Service 层加事务，不在 Controller 层加事务。
3.3 日志规范
统一使用 lombok.extern.slf4j.Slf4j 提供的 log 对象。
记录关键操作、异常、重要参数，避免记录敏感信息。
3.4 异常处理
业务异常统一抛出自定义异常（如 ServiceException）。
不要直接抛出或捕获 Exception，应捕获具体异常类型。
3.5 代码复用
公共方法抽取到工具类或父类，避免重复代码。
复杂查询封装到 Mapper 层，避免 Service 层拼接 SQL。
3.6 集合判空
统一使用工具类（如 Func.isEmpty(list)）判空，避免 NPE。
3.7 代码提交
禁止提交未使用的代码、调试代码、无用注释。
合并前自测通过，确保无编译错误和明显警告。
4. 其他建议
统一使用 Lombok 简化 Getter/Setter/构造器等。
VO/DTO/Entity/Request/Response 分层清晰，禁止混用。
重要业务流程建议配合流程图、时序图等文档说明。