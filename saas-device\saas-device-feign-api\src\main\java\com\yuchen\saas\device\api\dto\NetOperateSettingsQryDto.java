package com.yuchen.saas.device.api.dto;

import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

@Data
public class NetOperateSettingsQryDto extends Query {

    /**
     * 所属客户
     */
    private Long customerManageId;

    /**
     * 运维内容
     */
    private String alarmName;

    /**
     * 通知方式id
     */
    private Long noticeConfigId;

    /**
     * 通知频率; 1-推送一次，2-自定义
     */
    private Integer frequency;

    /**
     * 状态 1-启用，0-停用
     */
    private Integer status;

}
