package com.yuchen.saas.device.api.dto.operatingEnd;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/6/3 17:13
 */
@Data
public class NetOperateDeviceSubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;

    /**
     * 所属客户(组织)
     */
    @ApiModelProperty(name = "customerManageId", value = "所属客户(组织)")
    @NotNull(message = "请选择所属客户")
    private Long customerManageId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    @NotNull(message = "请选择所属产品")
    private Long productId;
    /**
     * 产品来源（1：IOT产品，2：第三方产品）
     */
    @ApiModelProperty(name = "productSource", value = "产品来源（1：IOT产品，2：第三方产品）")
    private String productSource;
    /**
     * iot产品key
     */
    @ApiModelProperty(name = "productKey", value = "iot产品key")
    @NotBlank(message = "iot产品key不能为空")
    private String productKey;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "deviceCategoryId", value = "设备类型")
    @NotNull(message = "请选择设备类型")
    private Long deviceCategoryId;
    /**
     * 大类（1：视频监控，2：智能硬件，3：传感设备）
     */
    @ApiModelProperty(name = "broadCategory", value = "大类（1：视频监控，2：智能硬件，3：传感设备）")
    @NotBlank(message = "请选择设备大类")
    private String broadCategory;
    /**
     * iot设备标识
     */
  /*  @ApiModelProperty(name = "deviceName", value = "iot设备标识")
    @NotBlank(message = "iot设备标识不能为空")
    private String deviceName;*/
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 设备品牌id
     */
    @ApiModelProperty(name = "deviceBrandId", value = "设备品牌id")
    @NotNull(message = "请选择设备品牌")
    private Long deviceBrandId;
    /**
     * 设备型号id
     */
    @ApiModelProperty(name = "deviceModelId", value = "设备型号id")
    @NotNull(message = "请选择设备型号")
    private Long deviceModelId;
    /**
     * 地理位置
     */
    @ApiModelProperty(name = "location", value = "地理位置")
    private String location;
    /**
     * 经度
     */
    @ApiModelProperty(name = "longitude", value = "经度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(name = "latitude", value = "纬度")
    private String latitude;
    /**
     * 设备图片
     */
    @ApiModelProperty(name = "filePath", value = "设备图片")
    private String filePath;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remark", value = "备注信息")
    private String remark;

    @ApiModelProperty(name = "deviceDetailList", value = "第三方")
    private List<NetOperateDeviceSubmitReqDetailDTO> deviceDetailList;

    @Data
    public static class NetOperateDeviceSubmitReqDetailDTO{
        @NotBlank
        @ApiModelProperty(name = "thirdPlatformCode", value = "物联第三方平台Code")
        private String thirdPlatformCode;

        @NotBlank
        @ApiModelProperty(name = "deviceName", value = "第三方设备Id")
        private String deviceName;
    }
}
