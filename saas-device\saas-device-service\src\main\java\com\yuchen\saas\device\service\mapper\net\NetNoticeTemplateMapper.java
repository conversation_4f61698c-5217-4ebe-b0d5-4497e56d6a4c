package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.entity.NetNoticeTemplate;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface NetNoticeTemplateMapper extends BaseMapper<NetNoticeTemplate> {


    @Select("SELECT nt.*,d1.node_name as notice_method_name, d2.node_name as notice_type_name,nc.notice_name as notice_config_name from net_notice_template nt" +
            " LEFT JOIN net_notice_config nc on nt.notice_config_id = nc.id" +
            " LEFT JOIN net_notice_method d1 on nc.notice_method = d1.node_key" +
            " LEFT JOIN net_notice_method d2 on nc.notice_type = d2.node_key" +
            " ${ew.customSqlSegment}")
    Page<NetNoticeTemplate> selectPageNoticeTemplate(Page<NetNoticeTemplate> page, @Param(Constants.WRAPPER) QueryWrapper<String> wrapper);


}
