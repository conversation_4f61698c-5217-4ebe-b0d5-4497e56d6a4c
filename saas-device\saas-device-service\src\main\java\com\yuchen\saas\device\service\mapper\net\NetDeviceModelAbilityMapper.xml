<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.net.NetDeviceModelAbilityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.NetDeviceModelAbility">
        <id column="id" property="id" />
        <result column="device_model_id" property="deviceModelId" />
        <result column="ability_type" property="abilityType" />
        <result column="ability_name" property="abilityName" />
        <result column="ability_code" property="abilityCode" />
        <result column="data_type" property="dataType" />
        <result column="ability_data" property="abilityData" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, device_model_id, ability_type, ability_name, ability_code, data_type, ability_data, remark, status, create_user, create_dept, create_time, update_user, update_time, is_deleted
    </sql>
    <select id="getDeviceAbilitySumList"
            resultType="com.yuchen.saas.device.api.dto.operatingEnd.DeviceAbilitySumDTO">
        SELECT
            device_model_id AS deviceModelId,
            COUNT( device_model_id ) AS deviceAbilitySum
        FROM
            net_device_model_ability
        WHERE
            is_deleted = 0
        <if test="null != modelIds and modelIds.size > 0 ">
            and device_model_id in
            <foreach collection="modelIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
            device_model_id
    </select>

</mapper>
