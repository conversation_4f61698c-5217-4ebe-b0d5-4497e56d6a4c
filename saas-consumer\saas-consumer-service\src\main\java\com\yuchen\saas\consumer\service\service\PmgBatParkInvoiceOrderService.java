package com.yuchen.saas.consumer.service.service;

import com.yuchen.saas.consumer.service.dto.Result;
import com.yuchen.saas.consumer.service.entity.PmgBatParkInvoiceOrder;
import com.yuchen.saas.consumer.service.vo.ParkInvoiceVo;
import com.yuchen.saas.payment.api.entity.PmgBatParkPay;
import org.nest.springwrap.core.mp.base.BaseService;

import java.util.List;

/**
 * @Author: 张逸飞
 * @Date: 2021/3/13 19:10
 * @Description: 停车场发票主订单操作
 */
public interface PmgBatParkInvoiceOrderService extends BaseService<PmgBatParkInvoiceOrder> {

    /**
     * (异步)开具蓝字发票异步方法
     * @param email 邮箱信息
     * @param parkInvoiceVo 发票抬头信息
     * @param pmgBatParkPayList 支付单数据
     * @return
     * @throws Exception
     */
    Result kpAsync(String email, ParkInvoiceVo parkInvoiceVo,
                   List<PmgBatParkPay> pmgBatParkPayList) throws Exception;

    /**
     * 定时查询发票结果
     */
    void queryInvoiceResult();

    /**
     * 根据关键字获取客户抬头模糊匹配结果列表(至少三个字)
     * @param parkId 园区ID
     * @param requestBody 关键字
     * @return
     * @throws Exception
     */
    Result cxCustomers(Long parkId, String requestBody) throws Exception;

    /**
     * 根据订单号获取发票订单详细，并将支付单号用逗号分隔为字符串返回
     * @param orderNo 发票订单号
     * @return
     */
    String getPayOrderNumber(String orderNo);
}
