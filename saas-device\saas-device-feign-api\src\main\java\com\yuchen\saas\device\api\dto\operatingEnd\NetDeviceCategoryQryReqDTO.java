package com.yuchen.saas.device.api.dto.operatingEnd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

/**
 * <AUTHOR>
 * @create 2024/5/23 15:00
 */
@Data
public class NetDeviceCategoryQryReqDTO extends Query {

    /**
     * code
     */
    @ApiModelProperty(name = "categoryCode", value = "code")
    private String categoryCode;
    /**
     * 设备类型名称
     */
    @ApiModelProperty(name = "categoryName", value = "设备类型名称")
    private String categoryName;
    /**
     * 大类（1：视频监控，2：智能硬件，3：传感设备）
     */
    @ApiModelProperty(name = "broadCategory", value = "大类（1：视频监控，2：智能硬件，3：传感设备）")
    private String broadCategory;
}
