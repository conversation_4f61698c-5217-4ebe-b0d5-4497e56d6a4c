package com.yuchen.saas.consumer.service.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuchen.saas.consumer.service.dto.Result;
import com.yuchen.saas.consumer.service.entity.PmgBatParkInvoiceOrder;
import com.yuchen.saas.consumer.service.entity.PmgBatParkInvoiceOrderItem;
import com.yuchen.saas.consumer.service.entity.PmgBatParkInvoiceTitle;
import com.yuchen.saas.consumer.service.vo.ParkInvoiceVo;
import com.yuchen.saas.payment.api.entity.PmgBatParkPay;
import org.nest.springwrap.core.mp.support.Query;
import org.nest.springwrap.core.tool.api.R;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName InvoiceService
 * @Description 发票相关接口
 * <AUTHOR>
 * @Date 2022/4/6 14:42
 */
public interface InvoiceService {

    /**
     * 获取开票详细信息
     *
     * @param payUserId 用户ID
     * @param ids   支付单ids
     * @return
     */
    R getKPInfo(String payUserId, String ids);

    /**
     * 保存发票抬头信息
     * @param pmgBatParkInvoiceTitle 发票抬头信息
     * @return
     */
    Boolean saveInvoiceTitle(PmgBatParkInvoiceTitle pmgBatParkInvoiceTitle);

    /**
     * 获取用户的发票抬头列表
     * @param pmgBatParkInvoiceTitle 发票抬头信息
     * @return
     */
    List<PmgBatParkInvoiceTitle> getInvoiceTitleList(PmgBatParkInvoiceTitle pmgBatParkInvoiceTitle);

    /**
     * 创建发票订单
     * @param parkInvoiceVo 发票抬头信息
     * @return
     * @throws Exception
     */
    R createInvoice(ParkInvoiceVo parkInvoiceVo) throws Exception;

    /**
     * 根据条件获取发票订单分页数据
     * @param pmgBatParkInvoiceOrder 发票订单数据
     * @param query 分页数据
     * @return
     */
    IPage<PmgBatParkInvoiceOrder> getInvoiceOrderPage(PmgBatParkInvoiceOrder pmgBatParkInvoiceOrder, Query query);

    /**
     * 根据条件获取发票订单详情内容
     * @param payUserId 用户ID
     * @param orderNo 发票订单号
     * @return
     */
    PmgBatParkInvoiceOrder getInvoiceOrderInfo(String payUserId, String orderNo);

    /**
     * 根据条件获取临停支付单列表
     * @param payUserId 用户ID
     * @param orderNo 发票订单号
     * @return
     */
    List<PmgBatParkPay> getParkBillList(String payUserId, String orderNo);

    /**
     * 根据关键字获取客户抬头模糊匹配结果列表(至少三个字)
     * @param parkId 园区ID
     * @param keyword 关键字
     * @return
     * @throws Exception
     */
    Result cxCustomers(Long parkId, String keyword) throws Exception;

    /**
     * 查询发票订单分页数据
     * @param current 当前页码
     * @param size 每页大小
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param customerName 发票抬头
     * @return
     */
    IPage<PmgBatParkInvoiceOrder> getInvoiceOrderPage(Integer current, Integer size, LocalDateTime startTime, LocalDateTime endTime, String customerName, Long parkId);

    /**
     * 查询开票子订单列表
     * @param orderNo 发票订单号
     * @return
     */
    List<PmgBatParkInvoiceOrderItem> getInvoiceOrderItemList(String orderNo);
}
