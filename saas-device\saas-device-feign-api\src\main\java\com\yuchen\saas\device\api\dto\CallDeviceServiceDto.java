package com.yuchen.saas.device.api.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 调用 iot 设备服务
 */
@Data
public class CallDeviceServiceDto {

    @NotNull(message = "productKey不能为空")
    private String productKey;
    @NotNull(message = "deviceName不能为空")
    private String deviceName;
    /**
     * 服务id
     */
    private String identify;
    /**
     * 调用设备服务入参参数
     */
    private Map<String, Object> in;
}
