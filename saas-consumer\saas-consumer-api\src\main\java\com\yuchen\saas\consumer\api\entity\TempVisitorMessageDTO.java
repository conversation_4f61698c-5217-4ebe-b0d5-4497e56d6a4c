package com.yuchen.saas.consumer.api.entity;

import lombok.Data;

/**
 * @description: 类目模版消息-火炬访客预约消息通知模板
 * <AUTHOR>
 * @date 2023/7/13
 * @version 1.0
 */
@Data
public class TempVisitorMessageDTO {

    /**
     * 模版ID
     */
    private String TEMPLATE_ID;

    /**
     * 支付平台用户ID
     */
    private String payUserId;

    /**
     * appId
     */
    private String appId;

    /**
     * 来访人员
     */
    private String thing2;

    /**
     * 开始时间
     */
    private String time3;

    /**
     * 拜访人
     */
    private String thing6;

    /**
     * 拜访公司
     */
    private String thing7;

    /**
     * 联系电话
     */
    private String phone_number10;


    /**
     * 详情url
     */
    private String url;
}
