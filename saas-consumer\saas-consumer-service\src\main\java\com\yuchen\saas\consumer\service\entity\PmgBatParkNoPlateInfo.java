package com.yuchen.saas.consumer.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * @Author: 张逸飞
 * @Date: 2021年4月2日 11:01:21
 * @Description: 停车场无牌车登记信息表
 */
@Data
@TableName("pmg_bat_park_no_plate_info")
public class PmgBatParkNoPlateInfo extends TenantEntity {

    /**
     * 支付平台用户ID
     */
    @ApiModelProperty(name = "payUserId", value = "支付平台用户ID")
    private String payUserId;

    /**
     * 车主姓名
     */
    @ApiModelProperty(name = "name", value = "车主姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "phone", value = "手机号")
    private String phone;

    /**
     * 停车场ID
     */
    @ApiModelProperty(name = "parkingId", value = "停车场ID")
    private Long parkingId;

    /**
     * 停车场名称
     */
    @ApiModelProperty(name = "parkingName", value = "停车场名称")
    private String parkingName;

    /**
     * 车辆入园压线车辆照片
     */
    @ApiModelProperty(name = "inLineImageUrl", value = "车辆入园压线车辆照片")
    private String inLineImageUrl;

    /**
     * 车辆出园压线车辆照片
     */
    @ApiModelProperty(name = "outLineImageUrl", value = "车辆出园压线车辆照片")
    private String outLineImageUrl;

    /**
     * 车牌号码
     */
    @ApiModelProperty(name = "plateNo", value = "车牌号码")
    private String plateNo;

    /**
     * 车辆类型（0：其他车、1：小型车、2：大型车、3：摩托车）
     */
    @ApiModelProperty(name = "vehicleType", value = "车辆类型（0：其他车、1：小型车、2：大型车、3：摩托车）")
    private Integer vehicleType;

    /**
     * 能源类型（0：其他类型、1：汽油车、2：新能源）
     */
    @ApiModelProperty(name = "energyType", value = "能源类型（0：其他类型、1：汽油车、2：新能源）")
    private Integer energyType;

    /**
     * 进入理由
     */
    @ApiModelProperty(name = "reason", value = "进入理由")
    private String reason;

    /**
     * 进入时间
     */
    @ApiModelProperty(name = "enterTime", value = "进入时间")
    private Date enterTime;

    /**
     * 车辆状态  0：未知、1：已入场、2：已出场
     */
    @ApiModelProperty(name = "carStatus", value = "车辆状态  0：未知、1：已入场、2：已出场")
    private Integer carStatus;

    /**
     * 车辆支付状态  0：未支付、1：已支付、2：已超时、9：免费时段
     */
    @ApiModelProperty(name = "payStatus", value = "车辆支付状态  0：未支付、1：已支付、2：已超时、9：免费时段")
    private Integer payStatus;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payTime", value = "支付时间")
    private Date payTime;
    /**
     * 所在入口id
     */
    @ApiModelProperty(name = "entranceId", value = "所在入口id")
    private Long entranceId;
    /**
     * 所在入口名称
     */
    @ApiModelProperty(name = "entranceName", value = "所在入口名称")
    private String entranceName;

    // --------------- 以下为临时需要的数据，不需要存入库 ---------------

    /**
     * 缴费金额
     */
    @TableField(exist = false)
    private String fee;

    /**
     * 入场免费时间 单位：分钟
     */
    @TableField(exist = false)
    private Long freeTime;

    /**
     * 缴费后允许延时出场时间 单位：分钟
     */
    @TableField(exist = false)
    private Long delayTime;

    /**
     * 倒计时剩余时间
     */
    @TableField(exist = false)
    private Long countdown;
}