package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 车辆类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
public enum CarTypeEnum {


    TEMPORARY_CAR("0", "临时车"),
    MONTH_CAR("1", "月卡车"),
    STAGGERED_CAR("2", "错时车"),
    CHANGE_CAR("3", "动态车"),
    AB_CAR("4", "AB车"),
/*    PROPERTY_CAR("4", "产权车"),
    FIXED_CAR("5", "固定车"),*/
   ;

    private String code;

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(String code) {
        CarTypeEnum[] enumArray = CarTypeEnum.values();
        for (CarTypeEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
