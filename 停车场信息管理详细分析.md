# 停车场信息管理详细分析

## 1. 概述

停车场信息管理是智慧停车系统的核心模块，负责停车场的全生命周期管理，包括停车场注册、维护、区域划分、运营状态管理和车位数量统计等功能。

## 2. 核心功能模块

### 2.1 停车场注册与维护

#### 2.1.1 功能描述
- **停车场注册**：新建停车场基础信息，包括名称、地址、联系方式等
- **停车场维护**：修改停车场信息，包括基础信息更新、配置调整等
- **停车场删除**：逻辑删除停车场，支持移除操作

#### 2.1.2 核心实体类
```java
@TableName("pmg_bat_park_parking")
public class PmgBatParkParking extends TenantEntity {
    // 停车场基础信息
    private String name;                    // 停车场名称
    private String address;                 // 停车场地址
    private String parkId;                  // 园区ID
    private String parentId;                // 父级停车场ID
    private Integer menuType;               // 菜单类型（1：主停车场，2：子停车场）

    // 车位配置
    private Integer totalPlace;             // 总车位数
    private Integer totalPermPlace;         // 固定车位数
    private Integer totalTempPlace;         // 临时车位数
    private Integer openTotalTemporaryPlace; // 开放临时车位数

    // 运营状态
    private Integer operateStatus;          // 运营状态（1：未运营，2：运营中）
    private String verifyState;             // 确认状态（0：未确认，1：已确认）
    private Integer isRemove;               // 是否移除（0：未移除，1：移除）

    // 收费配置
    private Boolean enableCharging;         // 是否开通收费
    private String enableChargingRules;     // 已开通收费规则
    private String collectFeeModel;         // 收费模式
    private String invoiceModel;            // 开票模式
}
```

#### 2.1.3 主要接口
- `POST /parking/PmgBatParkParking/saveParkParking` - 新增停车场
- `PUT /parking/PmgBatParkParking/updateParkParking` - 修改停车场
- `DELETE /parking/PmgBatParkParking/delete` - 删除停车场
- `DELETE /parking/PmgBatParkParking/remove` - 移除停车场

### 2.2 停车场区域划分

#### 2.2.1 功能描述
- **层级结构管理**：支持主停车场-子停车场的层级结构
- **区域划分**：按照建筑、楼层、区域进行停车场划分
- **空间信息管理**：关联园区空间信息，实现精细化管理

#### 2.2.2 核心实体类
```java
@TableName("park_space_info")
public class ParkSpaceInfo extends BasicEntity {
    private String name;        // 空间名称
    private Long parentId;      // 父主键
    private Integer type;       // 类型（1：层、2：区、3：建筑）
    private Integer sort;       // 排序
    private Integer levelType;  // 层类型（0：地下层、1：地面层）
}
```

#### 2.2.3 区域划分逻辑
1. **主停车场创建**：设置parentId为顶级父ID
2. **子停车场创建**：关联父停车场ID，继承园区信息
3. **空间关联**：通过spaceId关联具体的空间位置

### 2.3 运营状态管理

#### 2.3.1 状态定义
- **未运营（1）**：停车场已创建但未开始运营
- **运营中（2）**：停车场正常运营状态
- **暂停运营（3）**：临时暂停运营
- **停止运营（4）**：永久停止运营

#### 2.3.2 状态变更规则
```java
public Boolean updateStatus(ParkingInfoStatusUpdateDTO dto) {
    PmgBatParkParking parking = parkingService.getById(dto.getId());

    // 状态变更验证
    if (dto.getOperateStatus() == 4 && hasActiveBills(dto.getId())) {
        throw new ServiceException("存在未结算账单，无法停止运营");
    }

    parking.setOperateStatus(dto.getOperateStatus());
    return parkingService.updateById(parking);
}
```

#### 2.3.3 状态管理接口
- `PUT /parking/PmgBatParkParking/updateStatus` - 更新运营状态
- `PUT /parking/PmgBatParkParking/verifyParking` - 确认停车场

### 2.4 车位数量统计

#### 2.4.1 统计维度
- **总车位数**：停车场配置的总车位数量
- **固定车位数**：分配给固定用户的车位数量
- **临时车位数**：对外开放的临时车位数量
- **剩余车位数**：当前可用的车位数量
- **占用车位数**：当前被占用的车位数量

#### 2.4.2 统计实现
```java
public class ParkingStatisticsInfo {
    private Integer totalPlace;      // 总车位数
    private Integer leftPlace;       // 剩余车位数
    private Integer usedPlace;       // 已用车位数
    private Integer fixedPlace;      // 固定车位数
    private Integer tempPlace;       // 临时车位数
}
```

#### 2.4.3 实时统计更新
```java
public void updateLeftSpaceCount(String carType, ParkingStatisticsInfo parkingExtend, Integer passagesType) {
    Integer leftPlace = parkingExtend.getLeftPlace();
    // 进场减少剩余车位，出场增加剩余车位
    parkingExtend.setLeftPlace(passagesType == 0 ? leftPlace - 1 : leftPlace + 1);

    // 更新父停车场统计
    PmgBatParkParking parentParking = parkingService.getParent(parkingExtend.getId());
    if (parentParking != null) {
        updateParentParkingStats(parentParking, passagesType);
    }
}
```

## 3. 数据库表结构

### 3.1 主要数据表

#### 3.1.1 停车场信息表 (pmg_bat_park_parking)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| name | varchar(100) | 停车场名称 |
| address | varchar(500) | 停车场地址 |
| park_id | varchar(64) | 园区ID |
| parent_id | varchar(64) | 父级停车场ID |
| total_place | int | 总车位数 |
| total_perm_place | int | 固定车位数 |
| total_temp_place | int | 临时车位数 |
| operate_status | int | 运营状态 |
| verify_state | varchar(2) | 确认状态 |
| is_remove | int | 是否移除 |

#### 3.1.2 车位信息表 (parking_place)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| parking_id | bigint | 停车场ID |
| place_code | varchar(50) | 车位编码 |
| place_no | varchar(50) | 车位号 |
| place_type | bigint | 车位类型 |
| allocated_state | int | 分配状态 |
| place_used_type | varchar(10) | 车位使用类型 |

#### 3.1.3 空间信息表 (park_space_info)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| name | varchar(100) | 空间名称 |
| parent_id | bigint | 父主键 |
| type | int | 类型（1：层、2：区、3：建筑） |
| level_type | int | 层类型（0：地下层、1：地面层） |

## 4. 业务流程

### 4.1 停车场注册流程
1. **基础信息录入**：填写停车场名称、地址、联系方式等
2. **区域配置**：选择所属园区和空间位置
3. **车位配置**：设置总车位数、固定车位数、临时车位数
4. **收费配置**：配置收费模式、开票模式等
5. **状态设置**：设置初始运营状态
6. **信息保存**：保存停车场信息到数据库

### 4.2 停车场维护流程
1. **信息查询**：根据ID查询停车场详细信息
2. **信息修改**：更新停车场基础信息或配置
3. **状态变更**：更新运营状态或确认状态
4. **数据验证**：验证修改数据的合法性
5. **信息更新**：更新数据库中的停车场信息

### 4.3 车位统计更新流程
1. **事件触发**：车辆进出场事件触发统计更新
2. **统计计算**：根据进出类型更新剩余车位数
3. **父级更新**：如果是子停车场，同步更新父停车场统计
4. **缓存更新**：更新Redis缓存中的统计信息
5. **实时推送**：推送最新统计数据到前端

## 5. 技术实现

### 5.1 核心服务类
- `ParkingService`：停车场基础信息管理服务
- `ParkingInfoService`：停车场信息查询服务
- `ParkingPlaceService`：车位管理服务
- `ParkingCacheManager`：停车场缓存管理

### 5.2 主要控制器
- `ParkingController`：停车场管理接口
- `ParkingPlaceController`：车位管理接口
- `ParkingEventController`：停车场事件处理接口

### 5.3 数据传输对象
- `ParkingPageDTO`：停车场分页查询参数
- `ParkingInfoStatusUpdateDTO`：状态更新参数
- `VerifyParkingReqDTO`：停车场确认参数
- `ParkingPageVO`：停车场分页返回数据

## 6. 业务功能流程图

### 6.1 停车场注册流程图

```mermaid
flowchart TD
    A[开始停车场注册] --> B[填写基础信息]
    B --> C{信息验证}
    C -->|验证失败| D[返回错误信息]
    C -->|验证成功| E[选择所属园区]
    E --> F[配置空间位置]
    F --> G[设置车位配置]
    G --> H[配置收费规则]
    H --> I[设置运营状态]
    I --> J[保存停车场信息]
    J --> K{保存成功?}
    K -->|失败| L[返回保存失败]
    K -->|成功| M[生成停车场编号]
    M --> N[初始化统计信息]
    N --> O[创建缓存数据]
    O --> P[注册完成]
    D --> Q[结束]
    L --> Q
    P --> Q
```

### 6.2 停车场维护流程图

```mermaid
flowchart TD
    A[开始维护操作] --> B[查询停车场信息]
    B --> C{停车场存在?}
    C -->|不存在| D[返回不存在错误]
    C -->|存在| E[检查操作权限]
    E --> F{权限验证}
    F -->|无权限| G[返回权限错误]
    F -->|有权限| H[选择维护类型]
    H --> I{维护类型}
    I -->|基础信息| J[更新基础信息]
    I -->|状态变更| K[更新运营状态]
    I -->|收费配置| L[更新收费配置]
    I -->|车位配置| M[更新车位配置]
    J --> N[数据验证]
    K --> O[状态变更验证]
    L --> P[收费规则验证]
    M --> Q[车位数量验证]
    N --> R[保存更新]
    O --> S{状态变更合法?}
    P --> T{收费配置合法?}
    Q --> U{车位配置合法?}
    S -->|不合法| V[返回状态错误]
    S -->|合法| R
    T -->|不合法| W[返回配置错误]
    T -->|合法| R
    U -->|不合法| X[返回车位错误]
    U -->|合法| R
    R --> Y{保存成功?}
    Y -->|失败| Z[返回保存失败]
    Y -->|成功| AA[更新缓存]
    AA --> BB[推送变更通知]
    BB --> CC[维护完成]
    D --> DD[结束]
    G --> DD
    V --> DD
    W --> DD
    X --> DD
    Z --> DD
    CC --> DD
```

### 6.3 车位统计更新流程图

```mermaid
flowchart TD
    A[车辆进出事件] --> B[获取停车场信息]
    B --> C[获取当前统计数据]
    C --> D{事件类型}
    D -->|进场| E[剩余车位-1]
    D -->|出场| F[剩余车位+1]
    E --> G[更新占用统计]
    F --> H[更新占用统计]
    G --> I[检查父停车场]
    H --> I
    I --> J{存在父停车场?}
    J -->|不存在| K[保存统计数据]
    J -->|存在| L[更新父停车场统计]
    L --> M[递归处理父级]
    M --> K
    K --> N[更新Redis缓存]
    N --> O[推送实时数据]
    O --> P[记录统计日志]
    P --> Q[统计更新完成]
```

### 6.4 停车场区域划分流程图

```mermaid
flowchart TD
    A[开始区域划分] --> B[选择园区]
    B --> C[查询空间结构]
    C --> D[展示空间树形结构]
    D --> E[选择空间位置]
    E --> F{空间类型}
    F -->|建筑| G[创建建筑级停车场]
    F -->|楼层| H[创建楼层级停车场]
    F -->|区域| I[创建区域级停车场]
    G --> J[设置建筑属性]
    H --> K[设置楼层属性]
    I --> L[设置区域属性]
    J --> M[关联空间ID]
    K --> M
    L --> M
    M --> N[设置层级关系]
    N --> O{是否子停车场?}
    O -->|是| P[设置父停车场ID]
    O -->|否| Q[设置为主停车场]
    P --> R[继承园区信息]
    Q --> S[设置园区信息]
    R --> T[保存区域配置]
    S --> T
    T --> U[生成区域编码]
    U --> V[区域划分完成]
```

## 7. 关键业务规则

### 7.1 停车场创建规则
1. **唯一性验证**：同一园区内停车场名称不能重复
2. **层级限制**：最多支持3级停车场层级结构
3. **车位配置**：总车位数 = 固定车位数 + 临时车位数
4. **状态初始化**：新建停车场默认为"未运营"状态

### 7.2 状态变更规则
1. **运营状态变更**：
   - 未运营 → 运营中：需要完成收费配置
   - 运营中 → 暂停运营：需要处理在场车辆
   - 暂停运营 → 停止运营：需要清空所有车辆
2. **删除限制**：运营中的停车场不能删除
3. **移除条件**：只有未运营状态的停车场可以移除

### 7.3 车位统计规则
1. **实时更新**：车辆进出场实时更新统计数据
2. **父子联动**：子停车场统计变更自动更新父停车场
3. **数据一致性**：统计数据与实际车位状态保持一致
4. **异常处理**：统计异常时自动触发数据修复

## 8. 接口文档

### 8.1 停车场管理接口

#### 8.1.1 新增停车场
- **接口地址**：`POST /parking/PmgBatParkParking/saveParkParking`
- **请求参数**：
```json
{
  "name": "停车场名称",
  "address": "停车场地址",
  "parkId": "园区ID",
  "parentId": "父停车场ID",
  "totalPlace": 100,
  "totalPermPlace": 50,
  "totalTempPlace": 50,
  "operateStatus": 1
}
```
- **返回结果**：
```json
{
  "code": 200,
  "success": true,
  "data": {
    "id": 123,
    "parkingCode": "P001"
  },
  "msg": "操作成功"
}
```

#### 8.1.2 查询停车场列表
- **接口地址**：`GET /parking/PmgBatParkParking/page`
- **请求参数**：
```json
{
  "current": 1,
  "size": 10,
  "parkId": "园区ID",
  "parkingName": "停车场名称",
  "operateStatus": "运营状态"
}
```

#### 8.1.3 更新运营状态
- **接口地址**：`PUT /parking/PmgBatParkParking/updateStatus`
- **请求参数**：
```json
{
  "id": 123,
  "operateStatus": 2
}
```

### 8.2 车位统计接口

#### 8.2.1 获取停车场统计信息
- **接口地址**：`GET /parking/PmgBatParkParking/statistics`
- **返回结果**：
```json
{
  "code": 200,
  "data": {
    "totalPlace": 100,
    "leftPlace": 45,
    "usedPlace": 55,
    "fixedPlace": 50,
    "tempPlace": 50
  }
}
```

## 9. 缓存策略

### 9.1 Redis缓存设计
- **停车场基础信息**：`parking:info:{parkingId}`，TTL: 24小时
- **车位统计数据**：`parking:stats:{parkingId}`，TTL: 1小时
- **运营状态**：`parking:status:{parkingId}`，TTL: 12小时

### 9.2 缓存更新策略
1. **写入时更新**：数据变更时同步更新缓存
2. **定时刷新**：每小时定时刷新统计数据
3. **失效重建**：缓存失效时从数据库重建

## 10. 监控与告警

### 10.1 关键指标监控
- **停车场数量**：总停车场数、运营中停车场数
- **车位利用率**：各停车场车位使用率
- **系统性能**：接口响应时间、数据库查询性能

### 10.2 告警规则
- **车位利用率异常**：利用率超过95%或低于10%
- **统计数据异常**：统计数据与实际不符
- **接口异常**：接口错误率超过5%
