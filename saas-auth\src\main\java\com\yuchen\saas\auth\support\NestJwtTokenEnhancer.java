package com.yuchen.saas.auth.support;

import com.yuchen.saas.auth.service.NestUserDetails;
import com.yuchen.saas.auth.utils.TokenUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.jwt.JwtUtil;
import org.nest.springwrap.core.jwt.props.JwtProperties;
import org.nest.springwrap.core.tool.utils.Func;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;

import java.util.HashMap;
import java.util.Map;


@AllArgsConstructor
@Slf4j
public class NestJwtTokenEnhancer implements TokenEnhancer {

	private final JwtAccessTokenConverter jwtAccessTokenConverter;
	private final JwtProperties jwtProperties;

	@Override
	public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
		NestUserDetails principal = (NestUserDetails) authentication.getUserAuthentication().getPrincipal();

		//token参数增强
		Map<String, Object> info = new HashMap<>(16);
		info.put(TokenUtil.CLIENT_ID, TokenUtil.getClientIdFromHeader());
		info.put(TokenUtil.USER_ID, Func.toStr(principal.getUserId()));
		info.put(TokenUtil.DEPT_ID, Func.toStr(principal.getDeptId()));
		info.put(TokenUtil.POST_ID, Func.toStr(principal.getPostId()));
		info.put(TokenUtil.ROLE_ID, Func.toStr(principal.getRoleId()));
		info.put(TokenUtil.TENANT_ID, principal.getTenantId());
		info.put(TokenUtil.PARK_ID,principal.getParkId());
		info.put(TokenUtil.OAUTH_ID, principal.getOauthId());
		info.put(TokenUtil.ACCOUNT, principal.getAccount());
		info.put(TokenUtil.USER_NAME, principal.getUsername());
		info.put(TokenUtil.NICK_NAME, principal.getName());
		info.put(TokenUtil.REAL_NAME, principal.getRealName());
		info.put(TokenUtil.ROLE_NAME, principal.getRoleName());
		info.put(TokenUtil.AVATAR, principal.getAvatar());
		info.put(TokenUtil.DETAIL, principal.getDetail());
		info.put(TokenUtil.LICENSE, TokenUtil.LICENSE_NAME);
		info.put(TokenUtil.IS_ADMIN, principal.getIsAdmin());
		info.put(TokenUtil.MENU_IDS, principal.getMenuIds());
		info.put(TokenUtil.DEPT_ROLE_IDS, principal.getDeptRoleIds());
		info.put(TokenUtil.USER_INFO_ID,Func.toStr(principal.getUserInfoId()));
		info.put(TokenUtil.USER_DETAIL_ID, principal.getUserDetailId());
		info.put(TokenUtil.ORG_USER_ID, principal.getOrgUserId());
		info.put(TokenUtil.CUSTOMER_MANAGE_ID, principal.getCustomerManageId());
		info.put(TokenUtil.IS_SUPER_ADMIN, principal.getIsSuperAdmin());
		info.put(TokenUtil.ID_ENTITY, principal.getIdEntity());
		((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(info);

		//token状态设置
		if (jwtProperties.getState()) {
			OAuth2AccessToken oAuth2AccessToken = jwtAccessTokenConverter.enhance(accessToken, authentication);
			String tokenValue = oAuth2AccessToken.getValue();
			String tenantId = principal.getTenantId();
			String userId = Func.toStr(principal.getUserId());
			JwtUtil.addAccessToken(tenantId, userId, tokenValue, accessToken.getExpiresIn());
			//加权限缓存缓存时间跟token一样,,暂时拿key来用做key,(后续拿value值来做为key,不然没办法重置权限key后端不知道是谁的)
			JwtUtil.addMenuRoot(tenantId, Func.toJson(principal.getMenuRoot()),tokenValue,accessToken.getExpiresIn());
			log.info(Func.toJson(principal.getMenuRoot()));
		}

		return accessToken;
	}
}
