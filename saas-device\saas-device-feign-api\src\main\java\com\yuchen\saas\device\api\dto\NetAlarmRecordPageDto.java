package com.yuchen.saas.device.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/9/26 19:20
 * @Description TODO
 */
@Data
public class NetAlarmRecordPageDto implements Serializable {
    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerManageId", value = "客户id")
    private Long customerManageId;
    @ApiModelProperty("当前页")
    private Integer current;
    @ApiModelProperty("每页的数量")
    private Integer size;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 告警名称
     */
    @ApiModelProperty(name = "alarmName", value = "告警名称")
    private String alarmName;
    /**
     * 设备标识
     */
    @ApiModelProperty(name = "deviceName", value = "设备标识")
    private String deviceName;
    /**
     * 状态; 1-待处理，2-已关闭，3-已派工
     */
    @ApiModelProperty(name = "status", value = "状态; 1-待处理，2-已关闭，3-已派工")
    private Integer status;
    /**
     * 告警等级
     */
    @ApiModelProperty(name = "alarmLevel", value = "告警等级")
    private Integer alarmLevel;
    /**
     * 告警开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "startAlarmTime", value = "告警开始时间")
    private Date startAlarmTime;
    /**
     * 告警结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "endAlarmTime", value = "告警结束时间")
    private Date endAlarmTime;

}
