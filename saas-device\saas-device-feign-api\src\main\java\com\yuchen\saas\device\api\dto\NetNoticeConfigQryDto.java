package com.yuchen.saas.device.api.dto;

import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

@Data
public class NetNoticeConfigQryDto extends Query {

    /**
     * 所属客户id
     */
    private Long customerManageId;
    /**
     * 配置名称
     */
    private String noticeName;
    /**
     * 通知方式
     */
    private Integer nodeKey;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 通知分类；1-站外通知，2-弹窗告警，3-站内消息
     */
    private Integer noticeCategory;

}
