package com.yuchen.saas.device.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;

@Data
public class NetMonitorRecordPageQryDto extends Query {

    /**
     * 所属项目id
     */
    private Long parkId;
    /**
     * 人员名称
     */
    private String pointUser;
    /**
     * 布控点位
     */
    private String pointLocation;
    /**
     *监控名称
     */
    private String monitorName;
    /**
     * 开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startMonitorTime;
    /**
     * 结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endMonitorTime;

}
