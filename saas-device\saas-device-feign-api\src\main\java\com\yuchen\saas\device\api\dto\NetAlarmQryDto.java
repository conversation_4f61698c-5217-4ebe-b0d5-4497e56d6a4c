package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/30 15:02
 * @Description TODO
 */
@Data
public class NetAlarmQryDto implements Serializable {
    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerManageId", value = "所属客户")
    private Long customerManageId;

    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 告警名称
     */
    @ApiModelProperty(name = "alarmName", value = "告警名称")
    private String alarmName;
    /**
     * 产品key
     */
    @ApiModelProperty(name = "productKey", value = "产品key")
    private String productKey;
    /**
     * 设备标识
     */
    @ApiModelProperty(name = "deviceName", value = "设备标识")
    private String deviceName;
    /**
     * 场景id
     */
    @ApiModelProperty(name = "sceneId", value = "场景id")
    private Long sceneId;
    /**
     * 服务应用id
     */
    @ApiModelProperty(name = "serviceId", value = "服务应用id")
    private Long serviceId;
    /**
     * NetTriggerModeEnum
     * 触发方式; 1-设备上线，2-设备离线，3-属性上报，4-事件上报
     */
    @ApiModelProperty(name = "triggerMode", value = "触发方式; 1-设备上线，2-设备离线，3-属性上报，4-事件上报")
    private Integer triggerMode;
    /**
     * 告警来源; 1-设备触发，2-场景联动，3-业务规则
     */
    @ApiModelProperty(name = "alarmSource", value = "告警来源; 1-设备触发，2-场景联动，3-业务规则，4-第三方平台")
    private Integer alarmSource;
    /**
     * 第三方平台id
     */
    @ApiModelProperty(name = "thirdPartyId", value = "第三方平台id")
    private Long thirdPartyId;
    /**
     * 告警类型标识
     */
    @ApiModelProperty(name = "alarmTypeCode", value = "告警类型标识")
    private String alarmTypeCode;
    /**
     * 事件标识
     */
    @ApiModelProperty(name = "eventCode", value = "事件标识")
    private String eventCode;
}
