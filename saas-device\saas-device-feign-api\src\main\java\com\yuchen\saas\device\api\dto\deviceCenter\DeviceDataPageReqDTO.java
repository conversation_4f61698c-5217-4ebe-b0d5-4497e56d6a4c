package com.yuchen.saas.device.api.dto.deviceCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2024/8/30 9:21
 */
@Data
public class DeviceDataPageReqDTO extends Query {

    /**
     * 大类（1：视频监控，2：智能硬件，3：传感设备）
     */
    @ApiModelProperty(name = "broadCategory", value = "大类（1：视频监控，2：智能硬件，3：传感设备）")
    @NotBlank(message = "硬件大类不能为空")
    private String broadCategory;

    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;

    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;

    /**
     * 产品key
     */
    @ApiModelProperty(name = "productKey", value = "产品key")
    private String productKey;

    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;

    /**
     * 设备类型
     */
    @ApiModelProperty(name = "deviceType", value = "设备类型")
    private Long deviceType;

    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceAddress", value = "设备位置")
    private String deviceAddress;

    /**
     * 空间id
     */
    @ApiModelProperty(name = "spaceId", value = "空间id")
    private Long spaceId;

    /**
     * 最后连接开始时间
     */
    @ApiModelProperty(name = "lastConnectionStartTime", value = "最后连接开始时间")
    private Date lastConnectionStartTime;

    /**
     * 最后连接结束时间
     */
    @ApiModelProperty(name = "lastConnectionEndTime", value = "最后连接结束时间")
    private Date lastConnectionEndTime;

    /**
     * 最后断开开始时间
     */
    @ApiModelProperty(name = "lastBreakStartTime", value = "最后断开开始时间")
    private Date lastBreakStartTime;

    /**
     * 最后断开结束时间
     */
    @ApiModelProperty(name = "lastBreakEndTime", value = "最后断开结束时间")
    private Date lastBreakEndTime;


}
