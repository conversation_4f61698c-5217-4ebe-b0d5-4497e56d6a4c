package com.thinkunion.park.service.common.enums.parking;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/16 16:27
 * 0 其他车; 1 小型车; 2 大型车; 3 摩托车; 4 全部
 */
public enum ChargeRuleEnum {
    /**
     * 其他车
     */
    OTHER("0"),

    /**
     * 小型车
     */
    SMALL("1"),

    /**
     * 大型车
     */
    BIG("2"),

    /**
     * 摩托车motorcycle
     */
    MOTORCYCLE("3"),
    /**
     * 全部
     */
    All("-1");


    private String value;

    ChargeRuleEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
