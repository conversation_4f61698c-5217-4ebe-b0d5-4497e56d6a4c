# 设备与集成管理源码级分析

## 1. 主要涉及的实体类

- **PmgBatDevice**：设备信息表，记录闸机、LED、语音、摄像头等设备的基础信息、类型、状态。
- **PmgBatDeviceStatusLog**：设备状态日志表，记录设备在线、离线、故障等状态变更。
- **PmgBatDeviceBind**：设备与车道、车位、停车场的绑定关系表。
- **PmgBatDeviceLinkRule**：设备联动规则表。
- **PmgBatDeviceIotConfig**：IoT协议、一体机适配配置表。

## 2. 主要Controller

- `PmgBatDeviceController`：设备注册、信息维护、状态监控、远程控制、故障处理接口
- `PmgBatDeviceBindController`：设备与车道/车位/停车场的绑定与解绑接口
- `PmgBatDeviceLinkRuleController`：设备联动规则配置接口
- `PmgBatDeviceIotController`：一体机设备管理、IoT协议适配接口

## 3. 主要Service接口与实现

- `PmgBatDeviceService`：设备信息管理、注册、状态监控、远程控制、故障处理
- `PmgBatDeviceStatusLogService`：设备状态日志管理
- `PmgBatDeviceBindService`：设备与业务对象的绑定与解绑
- `PmgBatDeviceLinkRuleService`：设备联动规则管理
- `PmgBatDeviceIotService`：一体机设备管理、IoT协议适配

## 4. 典型方法调用链与数据流转

### 4.1 设备注册、状态监控、远程控制、故障处理

- 通过`PmgBatDeviceController.register`注册设备，写入`pmg_bat_device`
- 设备状态变更（上线、离线、故障）通过`PmgBatDeviceStatusLogService`记录日志
- 远程控制（如开闸、LED显示、语音播报）通过`PmgBatDeviceService.remoteControl`下发指令
- 故障处理支持自动告警、人工处理、状态恢复

### 4.2 设备与车道/车位/停车场的绑定与解绑

- 通过`PmgBatDeviceBindController.bind/unbind`实现设备与车道、车位、停车场的绑定与解绑
- 绑定关系存储于`pmg_bat_device_bind`
- 设备联动、权限校验等流程动态读取绑定关系

### 4.3 设备联动规则配置

- 通过`PmgBatDeviceLinkRuleController`配置设备联动规则（如过车时开闸+LED+语音）
- 联动规则存储于`pmg_bat_device_link_rule`
- 事件触发时，`PmgBatDeviceLinkRuleService`解析规则并下发多设备指令

### 4.4 一体机设备管理、IoT协议适配

- 通过`PmgBatDeviceIotController`管理一体机设备、配置IoT协议参数
- IoT协议适配配置存储于`pmg_bat_device_iot_config`
- 设备事件通过IoT协议解析，适配不同厂商/协议/数据格式

## 5. 关键业务流程图示

### 5.1 设备注册与状态监控

```mermaid
graph TD
A[设备注册] --> B[PmgBatDeviceController.register]
B --> C[写入pmg_bat_device]
A2[设备状态变更] --> D[PmgBatDeviceStatusLogService.log]
D --> E[写入pmg_bat_device_status_log]
```

### 5.2 设备绑定与解绑

```mermaid
graph TD
A[选择设备与车道/车位/停车场] --> B[PmgBatDeviceBindController.bind]
B --> C[写入pmg_bat_device_bind]
A2[解绑] --> D[PmgBatDeviceBindController.unbind]
D --> E[删除绑定关系]
```

### 5.3 设备联动规则配置与执行

```mermaid
graph TD
A[配置联动规则] --> B[PmgBatDeviceLinkRuleController.save]
B --> C[写入pmg_bat_device_link_rule]
A2[事件触发] --> D[PmgBatDeviceLinkRuleService.execute]
D --> E[下发多设备指令]
```

### 5.4 一体机与IoT协议适配

```mermaid
graph TD
A[注册一体机设备] --> B[PmgBatDeviceIotController.register]
B --> C[配置IoT协议参数]
C --> D[写入pmg_bat_device_iot_config]
A2[设备事件上报] --> E[PmgBatDeviceIotService.parseEvent]
E --> F[适配不同协议/厂商]
```

## 6. 其他说明

- 支持多类型设备的统一管理与扩展，适配多协议、多厂商
- 设备状态监控、远程控制、联动等流程均有详细状态流转与异常处理
- 支持批量操作、自动告警、灵活扩展

---
如需对某一具体方法、类或流程进一步深入源码解读，请进一步指定需求！ 