package com.thinkunion.park.service.common.iot.capability.devicemodel.loader;

import com.thinkunion.park.service.common.iot.capability.devicemodel.DeviceModel;

public class SingleExtendSerializer
        extends DeviceModelSerializer {

    @Override
    public String serialize(DeviceModel deviceModel) {
        return this.serializeInner(deviceModel);
    }

    @Override
    public DeviceModel deserialize(String json) {
        return this.deserializeInner(json);
    }
}

