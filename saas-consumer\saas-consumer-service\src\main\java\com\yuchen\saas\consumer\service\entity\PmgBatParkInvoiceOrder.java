package com.yuchen.saas.consumer.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.thinkunion.park.service.common.entity.BasicEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年3月17日 16:28:59
 * 发票订单主表
 */
@Data
@TableName("pmg_bat_park_invoice_order")
@ApiModel(value = "发票订单主表", description = "发票订单主表")
public class PmgBatParkInvoiceOrder extends BasicEntity {

    /**
     * 微信或者支付宝用户ID
     */
    @ApiModelProperty(name = "payUserId", value = "微信或者支付宝用户ID")
    private String payUserId;
    /**
     * 开票订单编号
     */
    @ApiModelProperty(name = "orderNo", value = "开票订单编号")
    private String orderNo;
    /**
     * 	销货方纳税人识别号
     */
    @ApiModelProperty(name = "taxpayerCode", value = "销货方纳税人识别号")
    private String taxpayerCode;
    /**
     * 购货方名称，即发票抬头
     */
    @ApiModelProperty(name = "customerName", value = "购货方名称，即发票抬头")
    private String customerName;
    /**
     * 购货方纳税人识别号
     */
    @ApiModelProperty(name = "customerCode", value = "购货方纳税人识别号")
    private String customerCode;
    /**
     * 购货方地址
     */
    @ApiModelProperty(name = "customerAddress", value = "购货方地址")
    private String customerAddress;
    /**
     * 购货方电话
     */
    @ApiModelProperty(name = "customerTel", value = "购货方电话")
    private String customerTel;
    /**
     * 购货方开户银行
     */
    @ApiModelProperty(name = "customerBankName", value = "购货方开户银行")
    private String customerBankName;
    /**
     * 购货方银行账号
     */
    @ApiModelProperty(name = "customerBankAccount", value = "购货方银行账号")
    private String customerBankAccount;
    /**
     * 开票人
     */
    @ApiModelProperty(name = "drawer", value = "开票人")
    private String drawer;
    /**
     * 税价合计金额
     */
    @ApiModelProperty(name = "totalAmount", value = "税价合计金额")
    private BigDecimal totalAmount;
    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱")
    private String email;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
    /**
     * 开票状态(0:开票中 1:开票成功 2:开票失败 3:红冲中(待退票) 4:已红冲(已退票) 5:被驳回 6:重开发票申请中 7:待作废 8:已作废 9:未开票 10:被驳回且重新申请 11:待红冲 12:红冲失败)
     */
    @ApiModelProperty(name = "invoiceStatus", value = "开票状态(0:开票中 1:开票成功 2:开票失败 3:红冲中(待退票) 4:已红冲(已退票) 5:被驳回 6:重开发票申请中 7:待作废 8:已作废 9:未开票 10:被驳回且重新申请 11:待红冲 12:红冲失败)")
    private Integer invoiceStatus;
    /**
     * PDF下载URL
     */
    @ApiModelProperty(name = "pdfUnsignedUrl", value = "PDF下载URL")
    private String pdfUnsignedUrl;
    /**
     * PDF 查看URL
     */
    @ApiModelProperty(name = "pdfViewUrl", value = "PDF 查看URL")
    private String pdfViewUrl;
    /**
     * 账单类型 1:临停账单。2:月卡账单
     */
    @ApiModelProperty(name = "billType", value = "1:临停账单。2:月卡账单")
    private Integer billType;

    @ApiModelProperty(name = "personPhone", value = "开票人电话")
    private String personPhone;

    @ApiModelProperty(name = "message", value = "三方接口开票返回消息")
    private String message;



    /**
     * 订单明细
     */
    @TableField(exist = false)
    private List<PmgBatParkInvoiceOrderItem> orderItems;

    /**
     * 开票时间
     */
    @TableField(exist = false)
    private String orderTime;

    /**
     * 是否直接开票。true:直接开具发票
     */
    @TableField(exist = false)
    private Boolean autoBilling;

    /**
     * 扩展参数
     */
    @TableField(exist = false)
    private Map<String, String> extendedParams;

}
