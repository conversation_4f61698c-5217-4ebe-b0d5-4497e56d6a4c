package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.yuchen.saas.device.api.entity.NetOperateUsers;
import com.yuchen.saas.device.api.vo.NetNoticeUserCountVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface NetOperateUsersMapper extends BaseMapper<NetOperateUsers> {


    @Select("select operate_settings_id, count(*) as count from net_operate_users ${ew.customSqlSegment} group by operate_settings_id")
    List<NetNoticeUserCountVo> getUserCountList(@Param(Constants.WRAPPER) QueryWrapper<String> wrapper);

}
