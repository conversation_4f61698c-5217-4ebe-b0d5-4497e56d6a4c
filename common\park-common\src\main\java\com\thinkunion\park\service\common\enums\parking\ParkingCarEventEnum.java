package com.thinkunion.park.service.common.enums.parking;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/27 10:40
 * @Description: 关于车辆事件的一些标识
 */


public enum ParkingCarEventEnum {

    /**
     * 进场压线事件：car_in_on_line_event
     */
    CAR_IN_ON_LINE_EVENT("car_in_on_line_event"),
    /**
     * 进场放行事件：car_in_event
     */
    CAR_IN_EVENT("car_in_event"),
    /**
     * 出厂压线事件：car_out_on_line_event
     */
    CAR_OUT_ON_LINE_EVENT("car_out_on_line_event"),
    /**
     * 出厂放行时间：car_out_event
     */
    CAR_OUT_EVENT("car_out_event"),

    /**
     * 压线事件：1
     */
    DETECT_WITHOUT_PICTURE("1"),

    /**
     * 图片事件：2
     */
    DETECT_WITH_PICTURE("2"),

    /**
     * 入场事件：3
     */
    ENTER_PASS_EVENT("3"),

    /**
     * 出场事件：4
     */
    EXIT_PASS_EVENT("4"),

    /**
     * 车牌矫正：5
     */
    PLATE_CORRECT("5"),

    /**
     * 图片重传：6
     */
    IMAGE_CORRECT("6");



    private String value;

    ParkingCarEventEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
