package com.thinkunion.park.service.common.utils;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 编码规则
 * <AUTHOR>
 * @create 2024/4/8 18:18
 */
public class ReceiptCodeUtil {

    private static final String CHAR_SET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();

    public static String setReceiptCodeValue(String typeName) {
        int random = (int)((new Random()).nextDouble() * 90000.0D) + 10000;
        String formatDateStr = (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date());
        return typeName + formatDateStr + random;
    }

    public static String setLocationCodeValue(String typeName) {
        int random = (int)((new Random()).nextDouble() * 90000.0D) + 10000;
        String formatDateStr = (new SimpleDateFormat("yyyyMMdd")).format(new Date());
        return typeName + formatDateStr + random;
    }

    public static String setAssetsCodeValue(String typeName) {
        int random = (int)((new Random()).nextDouble() * 90000.0D) + 10000;
        String formatDateStr = (new SimpleDateFormat("yyyyMMdd")).format(new Date());
        return typeName + formatDateStr + random;
    }

    public static String setCategoryCodeValue(String typeName) {
        int random = (int)((new Random()).nextDouble() * 90000.0D) + 10000;
        return typeName + random;
    }

    public static String setThirdPlatformCodeValue(String thirdPlatformName) {
        int random = (int)((new Random()).nextDouble() * 90000.0D) + 10000;
        String formatDateStr = (new SimpleDateFormat("yyyyMMdd")).format(new Date());
        return thirdPlatformName + formatDateStr + random;
    }

    public static String setAlarmLevelCodeValue(String alarmLevelName) {
        int random = (int)((new Random()).nextDouble() * 90000.0D) + 10000;
        String formatDateStr = (new SimpleDateFormat("yyyyMMdd")).format(new Date());
        return alarmLevelName + formatDateStr + random;
    }

    public static String setAlarmContentCodeValue(String alarmContentName) {
        int random = (int)((new Random()).nextDouble() * 90000.0D) + 10000;
        String formatDateStr = (new SimpleDateFormat("yyyyMMdd")).format(new Date());
        return alarmContentName + formatDateStr + random;
    }

    public static String setResourceCodeValue(String typeName) {
        int random = (int)((new Random()).nextDouble() * 90000.0D) + 10000;
        String formatDateStr = (new SimpleDateFormat("yyyyMMdd")).format(new Date());
        return typeName + formatDateStr + random;
    }

    public static String generateRandomString(int length) {
        if (length <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = RANDOM.nextInt(CHAR_SET.length());
            char randomChar = CHAR_SET.charAt(randomIndex);
            sb.append(randomChar);
        }
        return sb.toString();
    }
}
