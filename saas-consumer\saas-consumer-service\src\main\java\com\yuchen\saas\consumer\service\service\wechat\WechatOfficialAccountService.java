package com.yuchen.saas.consumer.service.service.wechat;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yuchen.saas.consumer.api.dto.wechat.ReplyMessageDTO;
import com.yuchen.saas.consumer.api.dto.wechat.WeChatEventInfoDTO;
import com.yuchen.saas.consumer.service.config.WeChatConfig;
import com.yuchen.saas.consumer.service.service.WeChatService;
import com.yuchen.saas.consumer.service.util.wechat.WeChatCustomMessageUtil;
import com.yuchen.saas.consumer.service.util.wechat.XMLUtil;
import com.yuchen.saas.manage.user.api.entity.SaasWechatSubscribeData;
import com.yuchen.saas.manage.user.api.feign.ISaasWechatSubscribeDataClient;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.log.exception.ServiceException;
import org.nest.springwrap.core.tool.api.R;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2024/1/2 15:09
 */
@Service
@Slf4j
public class WechatOfficialAccountService {

    @Resource
    private ISaasWechatSubscribeDataClient subscribeDataClient;
    @Resource
    private RestTemplate restTemplate;

    @Resource
    private WeChatService weChatService;

    @Resource
    private WeChatConfig weChatConfig;

    @Resource
    private WechatOfficialAccountMessageServer accountMessageServer;

    private static final String USER_INFO_URL = "%s/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN";

    private static final String TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";



    public void subscribeInfo(Map<String, String> map){
        WeChatEventInfoDTO eventInfo = new WeChatEventInfoDTO()
                .setToUserName(map.get("ToUserName"))
                .setFromUserName(map.get("FromUserName"))
                .setCreateTime(map.get("CreateTime"))
                .setMsgType(map.get("MsgType"))
                .setEvent(map.get("Event"));
        String accessToken = getToken(map.get("appId"));
        log.info("accessToken:{}",accessToken);
        String unionId = getUserInfo(map.get("appId"), accessToken,eventInfo.getFromUserName());
        SaasWechatSubscribeData saasWechatSubscribeData = new SaasWechatSubscribeData();
        saasWechatSubscribeData.setAppType("5");
        saasWechatSubscribeData.setUnionId(unionId);
        saasWechatSubscribeData.setAppId(map.get("appId"));
        saasWechatSubscribeData.setOpenId(eventInfo.getFromUserName());
        subscribeDataClient.saveSubscribeData(saasWechatSubscribeData);
        // 发送关注回复
//        accountMessageServer.pushWeChatCustomMessage(map.get("appId"), eventInfo.getFromUserName(), accessToken, "测试关注回复");
    }

    /**
     * 回复求购留言进行关注
     */
    public String replySubscribeInfo(Map<String, String> map) {
        WeChatEventInfoDTO eventInfo = new WeChatEventInfoDTO()
                .setToUserName(map.get("ToUserName"))
                .setFromUserName(map.get("FromUserName"))
                .setCreateTime(map.get("CreateTime"))
                .setMsgType(map.get("MsgType"))
                .setContent(map.get("content"));

        String accessToken = getToken(getToken(map.get("appId")));
        log.info("accessToken:{}",accessToken);
        String unionId = getUserInfo(map.get("appId"), accessToken,eventInfo.getFromUserName());
        ReplyMessageDTO result = new ReplyMessageDTO()
                .setToUserName(eventInfo.getFromUserName())
                .setFromUserName(eventInfo.getToUserName())
                .setCreateTime(System.currentTimeMillis())
                .setMsgType("text");
        if(StrUtil.isNotBlank(unionId)){
            SaasWechatSubscribeData saasWechatSubscribeData = new SaasWechatSubscribeData();
            saasWechatSubscribeData.setUnionId(unionId);
            saasWechatSubscribeData.setAppId(map.get("appId"));
            saasWechatSubscribeData.setOpenId(eventInfo.getFromUserName());
            saasWechatSubscribeData.setAppType("5");
            subscribeDataClient.saveSubscribeData(saasWechatSubscribeData);
            result.setContent("留言通知");
        }else {
            result.setContent("开通失败,暂未获取到您的用户信息");
        }
        return XMLUtil.textMessageToXml(result);
    }

    private String getUserInfo(String appId, String accessToken,String openId) {
        String url = String.format(USER_INFO_URL, weChatConfig.getDomainByAppId(appId), accessToken, openId);
        String json = restTemplate.getForObject(url, String.class);
        JSONObject jsonObject = JSONObject.parseObject(json);
        log.info("json:{}",json);
        return jsonObject.getString("unionid");
    }

    private String getToken(String appId) {
//        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(TOKEN_URL,new JSONObject().fluentPut("grant_type","client_credential").fluentPut("appid",appId).fluentPut("secret",appSecret).toJSONString(), JSONObject.class);
//        JSONObject json = Optional.ofNullable(responseEntity.getBody()).orElse(new JSONObject());
//        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
//        log.info("access_token_json:{}",json.toJSONString());
//        return json.getString("access_token");
        return weChatService.getWXAccessToken(appId, weChatConfig.getAppSecretByAppId(appId));
    }

    private String getNewToken(String appId, String appSecret) {
        String url = String.format(TOKEN_URL, appId, appSecret);
        String json = restTemplate.getForObject(url, String.class);
        JSONObject jsonObject = JSONObject.parseObject(json);
        log.info("json:{}",jsonObject);
        return jsonObject.getString("access_token");
    }

    public void unSubscribeInfo(Map<String, String> map){
        WeChatEventInfoDTO eventInfo = new WeChatEventInfoDTO()
                .setToUserName(map.get("ToUserName"))
                .setFromUserName(map.get("FromUserName"))
                .setCreateTime(map.get("CreateTime"))
                .setMsgType(map.get("MsgType"))
                .setEvent(map.get("Event"));
        SaasWechatSubscribeData saasWechatSubscribeData = new SaasWechatSubscribeData();
        saasWechatSubscribeData.setAppId(map.get("appId"));
        saasWechatSubscribeData.setOpenId(eventInfo.getFromUserName());
        saasWechatSubscribeData.setAppType("5");
        subscribeDataClient.updateUnSubscribeData(saasWechatSubscribeData);
    }

    public JSONObject configWechatMenu(String appId, JSONObject jsonObject) {
        String accessToken = getToken(appId);
        log.info("accessToken:{}",accessToken);
        String menus = jsonObject.toJSONString();
        String url = weChatConfig.getDomainByAppId(appId) + "/cgi-bin/menu/create?access_token=" + accessToken;
        JSONObject data = JSONObject.parseObject(menus, JSONObject.class);
        return getResult(data, url);
    }

    private JSONObject getResult(JSONObject data, String url) {
        HttpHeaders headers = new HttpHeaders();
        HttpMethod method = HttpMethod.POST;
        // 以表单的方式提交
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        //将请求头部和参数合成一个请求
        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(data, headers);
        //执行HTTP请求，将返回的结构使用ResultVO类格式化
        ResponseEntity<JSONObject> response = restTemplate.exchange(url, method, requestEntity, JSONObject.class);
        return response.getBody();
    }
}

