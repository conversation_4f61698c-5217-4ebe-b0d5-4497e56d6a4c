package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 月卡状态（0 未缴费 1 正常使用 2 已过期）
 * <AUTHOR>
 */
@AllArgsConstructor
public enum ActiveStateEnum {


    UNPAID_FEE(0, "未交费(免费未生效)"),
    NORMAL_USE(1, "正常使用"),
    OUT_OF_DATE(2, "已过期"),
    DEFUNCT(3, "已失效"),
   ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        ActiveStateEnum[] enumArray = ActiveStateEnum.values();
        for (ActiveStateEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
