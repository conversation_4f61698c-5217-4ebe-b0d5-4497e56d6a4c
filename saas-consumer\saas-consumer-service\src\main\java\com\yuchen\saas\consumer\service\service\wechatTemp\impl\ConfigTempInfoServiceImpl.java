package com.yuchen.saas.consumer.service.service.wechatTemp.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuchen.saas.consumer.api.entity.ConfigTempInfo;
import com.yuchen.saas.consumer.service.mapper.wechat.ConfigTempInfoMapper;
import com.yuchen.saas.consumer.service.service.wechatTemp.IConfigTempInfoService;
import org.nest.springwrap.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  模板消息配置类服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Service
public class ConfigTempInfoServiceImpl extends BaseServiceImpl<ConfigTempInfoMapper, ConfigTempInfo> implements IConfigTempInfoService {

    @Override
    public ConfigTempInfo getConfigTempInfoByTempCodeAndAppId(String tempCode, String appId) {
        return getOne(new LambdaQueryWrapper<ConfigTempInfo>()
                .eq(ConfigTempInfo::getTempCode, tempCode)
                .eq(ConfigTempInfo::getAppId, appId)
                .last("LIMIT 1"));
    }
}
