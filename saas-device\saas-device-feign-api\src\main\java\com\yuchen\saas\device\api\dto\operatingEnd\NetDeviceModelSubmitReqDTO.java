package com.yuchen.saas.device.api.dto.operatingEnd;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @create 2024/6/3 10:45
 */
@Data
public class NetDeviceModelSubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;
    /**
     * 设备品牌id
     */
    @ApiModelProperty(name = "deviceBrandId", value = "设备品牌id")
    private Long deviceBrandId;
    /**
     * 型号编码
     */
    @ApiModelProperty(name = "modelCode", value = "型号编码")
    private String modelCode;
    /**
     * 型号名称
     */
    @ApiModelProperty(name = "modelName", value = "型号名称")
    private String modelName;
    /**
     * 协议版本
     */
    @ApiModelProperty(name = "agreementVersion", value = "协议版本")
    private String agreementVersion;
    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "deviceCategoryId", value = "设备类型")
    private Long deviceCategoryId;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
    /**
     * 业务状态 1开启，0关闭
     */
    @ApiModelProperty("业务状态 业务状态 1开启，0关闭")
    private Integer status;
}
