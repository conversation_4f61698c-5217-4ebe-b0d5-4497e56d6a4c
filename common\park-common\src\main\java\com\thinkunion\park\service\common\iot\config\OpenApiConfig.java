package com.thinkunion.park.service.common.iot.config;

import com.thinkunion.sdk.iot.IoTClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class OpenApiConfig {

    @Value("${iot.openapi.uri}")
    private String uri;

    @Value("${iot.openapi.access-key}")
    private String accessKey;

    @Value("${iot.openapi.access-secret}")
    private String accessSecret;


    @Bean
    public IoTClient iotClient() {
        return new IoTClient(uri, accessKey, accessSecret);
//        return new IoTClient("http://************:8889", "OnpAWDZE6kEzIAuQojRWb0q9", "460uORLy6i3iE8OX1V7sZGipBzJFDe");// llc 本地配置
    }

}
