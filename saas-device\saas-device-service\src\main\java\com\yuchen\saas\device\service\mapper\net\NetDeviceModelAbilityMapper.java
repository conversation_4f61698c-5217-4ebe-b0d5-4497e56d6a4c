package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuchen.saas.device.api.dto.operatingEnd.DeviceAbilitySumDTO;
import com.yuchen.saas.device.api.entity.NetDeviceModelAbility;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 设备型号能力信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
public interface NetDeviceModelAbilityMapper extends BaseMapper<NetDeviceModelAbility> {

    List<DeviceAbilitySumDTO> getDeviceAbilitySumList(@Param("modelIds") List<Long> modelIds);
}
