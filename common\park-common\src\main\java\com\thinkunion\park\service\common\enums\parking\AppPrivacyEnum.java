package com.thinkunion.park.service.common.enums.parking;

import cn.hutool.core.util.StrUtil;
import com.thinkunion.park.service.common.enums.wechat.OfficialTempEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/1/19 11:59
 */
@Getter
@AllArgsConstructor
public enum AppPrivacyEnum {

    XMHJZHYPT_APPLET("wx306ce81471effaf0", "厦门火炬智慧云平台", "智慧平台"),
    XMHJZHPT_APPLET("wxedcc666fd0b2f801", "厦门火炬智慧平台", "智慧平台"),
    JHCZ__APPLET("wxc1e73c5498c82608", "聚合场站", "聚合场站"),
    YCY__APPLET("wx908e9848a2667637", "钰辰云", "智慧平台"),

        ;
    private final String appId;

    private final String appName;

    private final String desc;

    public static String getEnumDesc(String appId) {
        if (StrUtil.isNotBlank(appId)) {
            for (AppPrivacyEnum e : AppPrivacyEnum.values()) {
                if (e.getAppId().equals(appId)) {
                    return e.getDesc();
                }
            }
        }
        return "智慧平台";
    }
}
