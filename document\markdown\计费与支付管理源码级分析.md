# 计费与支付管理源码级分析

## 1. 主要涉及的实体类

- **PmgBatParkBill**：停车账单表，记录车辆在场期间的费用、结算状态、支付信息等。
- **PmgBatParkPay**：支付单表，记录每笔支付的金额、渠道、状态等。
- **PmgBatParkPayRefund**：退款单表，记录退款申请、审核、执行、结果等。
- **PmgBatInvoice**：发票表，记录发票申请、开具、状态等。
- **PmgBatCarPaymentRule**、**PmgBatChargeRule**：计费规则表，支持多种计费模式（临时车、月卡、错时、节假日、特殊车辆等）。

## 2. 主要Controller

- `PmgBatParkBillController`：账单相关接口（生成、查询、结算、导出等）
- `PmgBatParkPayController`/`PayController`：支付相关接口（多渠道支付、回调、补缴、催缴等）
- `PmgBatParkPayRefundController`：退款相关接口
- `PmgBatInvoiceController`：发票相关接口
- `PmgBatCarPaymentRuleController`、`PmgBatChargeRuleController`：计费规则配置接口

## 3. 主要Service接口与实现

- `ParkingBillService`/`ParkingChargeSettleService`：账单生成、结算、费用计算、导出等
- `PmgBatParkPayService`/`XCPayService`/`AllInPayService`：支付单生成、支付处理、回调、补缴、催缴等
- `PmgBatParkPayRefundService`：退款处理
- `PmgBatInvoiceService`：发票处理
- `PmgBatCarPaymentRuleService`/`PmgBatChargeRuleService`：计费规则管理

## 4. 典型方法调用链与数据流转

### 4.1 计费规则配置

- 通过`PmgBatCarPaymentRuleController`、`PmgBatChargeRuleController`配置不同类型车辆、时段、节假日等计费规则
- 规则存储于`pmg_bat_car_payment_rule`、`pmg_bat_charge_rule`等表
- 账单生成/结算时动态读取并应用对应规则

### 4.2 账单生成、查询、结算、导出

- 车辆入场/出场事件触发账单生成（`ParkingBillService.saveParkingBill`等）
- 账单查询通过`PmgBatParkBillController`分页、条件检索
- 结算通过`ParkingChargeSettleService.endToTheBill`等方法，更新账单状态
- 导出通过`PmgBatParkBillController`导出接口，生成Excel等格式

### 4.3 欠费管理、补缴、催缴

- 欠费账单通过账单状态、逾期字段筛选
- 补缴通过`PayController`发起补缴支付，更新账单与支付单状态
- 催缴可通过消息推送、短信等方式集成

### 4.4 多渠道支付

- 支付接口支持微信、支付宝、钱包、优惠券等（`PayController.createPay`、`orderGoToPay`等）
- 支付单生成后，调用第三方支付SDK/网关，返回支付二维码/链接
- 支付完成后，第三方平台回调`PayController.XCPayNotify`等接口

### 4.5 支付回调处理、支付后自动开闸

- 回调接口校验支付结果，更新支付单、账单状态
- 若支付成功且车辆在出口，自动调用`ParkingGateClient.sendGateOpen`开闸
- 记录支付与开闸事件

### 4.6 退款管理

- 退款申请通过`PmgBatParkPayRefundController.initiateRefund`等接口发起
- 审核、执行、结果记录在`PmgBatParkPayRefund`表
- 支持原路退回、人工审核等多种模式

### 4.7 发票管理

- 发票申请、开具、查询通过`PmgBatInvoiceController`相关接口
- 支持电子发票、纸质发票等多种类型
- 发票状态与支付、账单、退款等联动

## 5. 关键业务流程图示

### 5.1 账单生成与支付流程

```mermaid
graph TD
A[车辆入场/出场事件] --> B[ParkingBillService.saveParkingBill]
B --> C[生成账单]
C --> D[PayController.createPay]
D --> E[生成支付单]
E --> F[第三方支付]
F --> G[支付回调]
G --> H[更新账单/支付单状态]
H --> I{是否在出口?}
I -- 是 --> J[自动开闸]
I -- 否 --> K[等待出场]
```

### 5.2 退款流程

```mermaid
graph TD
A[用户发起退款申请] --> B[PmgBatParkPayRefundController.initiateRefund]
B --> C[退款审核]
C --> D[退款执行]
D --> E[更新退款/账单/支付单状态]
```

### 5.3 发票流程

```mermaid
graph TD
A[用户发起发票申请] --> B[PmgBatInvoiceController]
B --> C[发票审核/开具]
C --> D[发票状态更新]
```

## 6. 其他说明

- 计费规则高度可配置，支持多种业务场景
- 支付、退款、发票等流程均有详细的状态流转与异常处理
- 支持与第三方支付、发票平台的深度集成

---
如需对某一具体方法、类或流程进一步深入源码解读，请进一步指定需求！ 