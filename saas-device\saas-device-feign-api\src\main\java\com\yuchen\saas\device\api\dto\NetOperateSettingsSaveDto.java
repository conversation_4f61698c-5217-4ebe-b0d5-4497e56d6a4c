package com.yuchen.saas.device.api.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Data
public class NetOperateSettingsSaveDto {

    /**
     * 所属客户
     */
    @NotNull(message = "所属客户不能为空")
    private Long customerManageId;

    /**
     * 管理区id
     */
    @NotNull(message = "管理区id不能为空")
    private Long manageAreaId;

    /**
     * 运维事项
     */
    @NotNull(message = "运维事项不能为空")
    private Long alarmConfigId;

    /**
     * 通知方式ids,逗号拼接
     */
    @NotNull(message = "通知方式不能为空")
    private String noticeConfigIds;

    /**
     * 通知频率; 1-推送一次，2-自定义
     */
    @NotNull(message = "通知频率不能为空")
    private Integer frequency;

    /**
     * 超过 x 分钟/时/天
     */
    private Integer frequencyValue;

    /**
     * 频率单位; 1-分钟，2-小时，3-天
     */
    private Integer frequencyUnit;

    /**
     * 重复次数
     */
    private Integer repeats;

    /**
     * 备注
     */
    private String remark;

    /**
     * 运维人员列表
     */
    @Size(min = 1,message = "运维人员列表不能为空")
    private List<UsersInfo> userList = new ArrayList<>();

    @Data
    public static class UsersInfo {
        /**
         * 人员id
         */
        private Long userId;
        /**
         * 手机号
         */
        private String phone;
        /**
         * 姓名
         */
        private String userName;
    }

}
