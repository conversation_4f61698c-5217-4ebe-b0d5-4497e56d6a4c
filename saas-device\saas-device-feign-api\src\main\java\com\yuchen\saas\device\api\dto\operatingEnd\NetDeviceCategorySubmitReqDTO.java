package com.yuchen.saas.device.api.dto.operatingEnd;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @create 2024/5/23 15:00
 */
@Data
public class NetDeviceCategorySubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;
    /**
     * 设备类型名称
     */
    @ApiModelProperty(name = "categoryName", value = "设备类型名称")
    @NotBlank(message = "设备类型名称不能为空")
    private String categoryName;
    /**
     * 大类（1：视频监控，2：智能硬件，3：传感设备）
     */
    @ApiModelProperty(name = "broadCategory", value = "大类（1：视频监控，2：智能硬件，3：传感设备）")
    @NotBlank(message = "请选择大类")
    private String broadCategory;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
