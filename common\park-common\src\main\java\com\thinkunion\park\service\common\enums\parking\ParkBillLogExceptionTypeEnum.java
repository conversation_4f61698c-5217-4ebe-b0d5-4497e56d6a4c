package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;


@AllArgsConstructor
public enum ParkBillLogExceptionTypeEnum {


    GATE_AND_PARKING_SPACE(0, "闸门与车位事件时间冲突异常"),
    EVENT_TIME_ERROR(1, "事时间重叠或者闭环缺失(进出成对存在)"),
    NUMBER_OF_CHARGING_VEHICLE(2, "充电车牌号填错"),
    PARKING_SPACE_CHARGING_ERROR(3, "车牌在本车位的事件时间里有其他车位的充电记录"),
    CHARGING_ERROR(4, "没停车事件但是有充电记录"),
   ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        ParkBillLogExceptionTypeEnum[] enumArray = ParkBillLogExceptionTypeEnum.values();
        for (ParkBillLogExceptionTypeEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
