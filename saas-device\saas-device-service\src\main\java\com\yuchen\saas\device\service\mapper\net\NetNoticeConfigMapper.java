package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.entity.NetNoticeConfig;
import com.yuchen.saas.device.api.vo.NetNoticeMethodVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface NetNoticeConfigMapper extends BaseMapper<NetNoticeConfig> {

    @Select("SELECT nc.*,d1.`node_name` as notice_method_name, d2.`node_name` as notice_type_name from net_notice_config nc" +
            " LEFT JOIN net_notice_method d1 on nc.notice_method = d1.node_key" +
            " LEFT JOIN net_notice_method d2 on nc.notice_type = d2.node_key" +
            " ${ew.customSqlSegment}")
    Page<NetNoticeConfig> selectPageNoticeConfig(Page<NetNoticeConfig> page, @Param(Constants.WRAPPER) QueryWrapper<String> wrapper);

    @Select("SELECT * FROM net_notice_method WHERE pid=0 and is_deleted=0 ORDER BY sort")
    List<NetNoticeMethodVo> getNoticeMethods();

    @Select("SELECT * FROM net_notice_method WHERE pid != 0 and is_deleted=0 ORDER BY sort")
    List<NetNoticeMethodVo> getNoticeTypes(@Param("pid") int pid);

}
