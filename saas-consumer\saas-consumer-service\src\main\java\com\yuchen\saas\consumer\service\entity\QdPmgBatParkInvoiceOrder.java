package com.yuchen.saas.consumer.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yuchen.saas.consumer.service.dto.BdcjyzlfwVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @description: 全电发票主表新增参数
 * <AUTHOR>
 * @date 2023/5/22 9:44
 * @version 1.0
 */
@Data
@ApiModel(value = "全电发票主表新增参数", description = "全电发票主表新增参数")
public class QdPmgBatParkInvoiceOrder extends PmgBatParkInvoiceOrder {

    /**
     * (全电发票)特定业务必填，传值特定要素编码值（比如货物运输服务，传值为04）,03：建筑服务发票;04：货物运输服务发票、06：不动产经营租赁服务、09：旅客运输服务发票
     */
    @TableField(exist = false)
    private String tdyslxdm;

    /**
     * (全电发票)开票类型。p:电子增值税普通发票（默认），ps:电子收购发票，c:纸质普通发票，cs:纸质收购票，s:增值税专用发票，py:成品油，q:全电普票（数电普票），w:全电专票（数电专票），qs:全电收购
     */
    @TableField(exist = false)
    private String invoiceType;

    /**
     * (全电发票)不动产经营租赁服务
     */
    @TableField(exist = false)
    private BdcjyzlfwVO bdcjyzlfw;

}
