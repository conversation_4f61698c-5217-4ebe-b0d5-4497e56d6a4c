package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("net_monitor_record")
public class NetMonitorRecord {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 所属项目id
     */
    private Long parkId;
    /**
     * 检测时间
     */
    private Date monitorTime;
    /**
     * 布控点位id
     */
    private Long pointTraceId;
    /**
     * 布控人员id
     */
    private Long pointUserId;
    /**
     * 布控点位
     */
    private String pointLocation;
    /**
     * 监控名称
     */
    private String monitorName;
    /**
     * 布控人员
     */
    private String pointUser;
    /**
     * 抓拍图片/视频
     */
    private String path;
    /**
     * 创建时间
     */
    private Date createTime;

}
