package com.yuchen.saas.auth.config;

import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;

@Configuration
@AllArgsConstructor
@EnableResourceServer
public class NestResourceServerConfiguration extends ResourceServerConfigurerAdapter {

    private final AuthenticationSuccessHandler appLoginInSuccessHandler;

    @Override
    @SneakyThrows
    public void configure(HttpSecurity http) {
        http.headers().frameOptions().disable();
        http.formLogin()
                .successHandler(appLoginInSuccessHandler)
                .and()
                .authorizeRequests()
                .antMatchers(
                        "/actuator/**",
                        "/oauth/captcha",
                        "/oauth/logout",
                        "/oauth/clear-cache",
                        "/oauth/render/**",
                        "/oauth/callback/**",
                        "/parking/stub/**",
                        "/oauth/revoke/**",
                        "/oauth/refresh/**",
                        "/token/**",
                        "/mobile/**",
                        "/v2/api-docs").permitAll()
                .anyRequest().authenticated().and()
                .csrf().disable();
    }
}
