package com.yuchen.saas.device.service.iot.input;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.yuchen.saas.device.api.entity.NetOperateDevice;
import com.yuchen.saas.device.api.vo.environment.SaasEnvironmentDeviceVo;
import com.yuchen.saas.device.service.service.PmgBatDeviceDataService;
import com.yuchen.saas.device.service.service.environment.ISaasEnvironmentDeviceService;
import com.yuchen.saas.device.service.service.net.NetOperateDeviceService;
import com.yuchen.saas.park.api.entity.device.PmgBatSmartEquipment;
import com.yuchen.saas.park.api.entity.park.PmgBatDeviceData;
import com.yuchen.saas.park.api.vo.fault.DeviceProperty;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.nest.springwrap.core.tool.api.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;


@Slf4j
@Service
public class DevicePropertyInput {
    private final Logger logger = LoggerFactory.getLogger(DevicePropertyInput.class);
    @Resource
    private NestRedis nestRedis;
    @Resource
    private NetOperateDeviceService netOperateDeviceService;
    @Resource
    private PmgBatDeviceDataService pmgBatDeviceDataService;

    public final static String DEFAULT_TENANT_ID = "108994";
    /**
     * 处理 IOT平台推送的属性数据
     *
     * @param deviceProperty 入参
     */
    @Async
    public void inputData(DeviceProperty deviceProperty) {
        // 获取设备的 productKey 和 deviceName
        String productKey = deviceProperty.getProductKey();
        String deviceName = deviceProperty.getDeviceName();
        log.info("productKey：{}", productKey);
        log.info("deviceName：{}", deviceName);
        // 缓存设备最新属性信息
        nestRedis.set("deviceName:identify:" + deviceProperty.getDeviceName() + ":" + deviceProperty.getIdentify(), JSONObject.toJSONString(deviceProperty));
        log.info("deviceName:identify:" + deviceProperty.getDeviceName() + ":" + deviceProperty.getIdentify() + ":{}", (Object) nestRedis.get("deviceName:identify:" + deviceProperty.getDeviceName() + ":" + deviceProperty.getIdentify()));
        // 可以存库，需要更换其他sql存储

        NetOperateDevice deviceVo = netOperateDeviceService.getOperateDevice(deviceName);
        if(deviceVo != null){
            saveDeviceData(deviceProperty, deviceVo);
        }else {
            logger.error("找不到NetOperateDevice,productKey = " + productKey +",deviceName = " + deviceName);
        }
    }
    private void saveDeviceData(DeviceProperty deviceProperty, NetOperateDevice deviceInfo) {
        // 实体类转换
        PmgBatDeviceData pmgBatDeviceData = new PmgBatDeviceData();
        BeanUtils.copyProperties(deviceProperty, pmgBatDeviceData);
        // 秒 转 毫秒 需要 * 1000
        Date date = DateUtil.date(Long.valueOf(deviceProperty.getDeviceTime()) * 1000);
        pmgBatDeviceData.setDeviceTime(date);
        pmgBatDeviceData.setValue(String.valueOf(deviceProperty.getValue()));
        // 插入设备详细信息
        pmgBatDeviceData.setTenantId(DEFAULT_TENANT_ID);
        pmgBatDeviceData.setParkId(deviceInfo.getProjectId());
        pmgBatDeviceData.setCreateUser(deviceInfo.getCreateUser());
        pmgBatDeviceData.setCreateDept(deviceInfo.getCreateDept());

        pmgBatDeviceDataService.saveAndUpdate(pmgBatDeviceData);
    }
}