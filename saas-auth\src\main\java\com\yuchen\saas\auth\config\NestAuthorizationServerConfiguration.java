package com.yuchen.saas.auth.config;

import com.yuchen.saas.auth.constant.AuthConstant;
import com.yuchen.saas.auth.granter.NestTokenGranter;
import com.yuchen.saas.auth.service.NestClientDetailsServiceImpl;
import com.yuchen.saas.manage.user.api.feign.ISaasWechatSubscribeDataClient;
import com.yuchen.saas.manage.user.api.feign.IUserClient;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.nest.springwrap.core.social.props.SocialProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.TokenGranter;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;


@Order
@Configuration
@AllArgsConstructor
@EnableAuthorizationServer
public class NestAuthorizationServerConfiguration extends AuthorizationServerConfigurerAdapter {

	private final DataSource dataSource;

	private final AuthenticationManager authenticationManager;

	private final UserDetailsService userDetailsService;

	private final TokenStore tokenStore;

	private final TokenEnhancer jwtTokenEnhancer;

	private final JwtAccessTokenConverter jwtAccessTokenConverter;

	private final NestRedis nestRedis;

	private final IUserClient userClient;

	private final SocialProperties socialProperties;

	private final ISaasWechatSubscribeDataClient subscribeDataClient;

	@Override
	public void configure(AuthorizationServerEndpointsConfigurer endpoints) {
//获取自定义tokenGranter
		TokenGranter tokenGranter = NestTokenGranter
				.getTokenGranter(authenticationManager, endpoints, nestRedis, userClient, socialProperties,userDetailsService,subscribeDataClient);

		//配置端点
		endpoints.tokenStore(tokenStore)
			.authenticationManager(authenticationManager)
			.userDetailsService(userDetailsService)
			.tokenGranter(tokenGranter);

		//扩展token返回结果
		if (jwtAccessTokenConverter != null && jwtTokenEnhancer != null) {
			TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
			List<TokenEnhancer> enhancerList = new ArrayList<>();
			enhancerList.add(jwtTokenEnhancer);
			enhancerList.add(jwtAccessTokenConverter);
			tokenEnhancerChain.setTokenEnhancers(enhancerList);
			//jwt增强
			endpoints.tokenEnhancer(tokenEnhancerChain).accessTokenConverter(jwtAccessTokenConverter);
		}
	}

	
	@Override
	@SneakyThrows
	public void configure(ClientDetailsServiceConfigurer clients) {
		NestClientDetailsServiceImpl clientDetailsService = getClientDetailService();
		clientDetailsService.setSelectClientDetailsSql(AuthConstant.DEFAULT_SELECT_STATEMENT);
		clientDetailsService.setFindClientDetailsSql(AuthConstant.DEFAULT_FIND_STATEMENT);
		clients.withClientDetails(clientDetailsService);
	}

	@Bean
	public NestClientDetailsServiceImpl getClientDetailService(){
		return new NestClientDetailsServiceImpl(dataSource);
	}

	@Override
	public void configure(AuthorizationServerSecurityConfigurer oauthServer) {
		oauthServer
			.allowFormAuthenticationForClients()
			.tokenKeyAccess("permitAll()")
			.checkTokenAccess("isAuthenticated()");
	}
}
