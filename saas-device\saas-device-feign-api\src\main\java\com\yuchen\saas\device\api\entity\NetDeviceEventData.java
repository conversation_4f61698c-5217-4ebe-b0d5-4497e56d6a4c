package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("net_device_event_data")
public class NetDeviceEventData {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设备产品key
     */
    private String productKey;

    /**
     * 设备key
     */
    private String deviceName;

    /**
     * 事件名
     */
    private String name;

    /**
     * 设备标识符
     */
    private String identifier;

    /**
     * 属性标识
     */
    private String identify;

    /**
     * 属性值
     */
    private String value;

    /**
     * 设备时间
     */
    private Date deviceTime;

    /**
     * 服务时间
     */
    private Date serverTime;

    /**
     * 创建时间
     */
    private Date createTime;



}
