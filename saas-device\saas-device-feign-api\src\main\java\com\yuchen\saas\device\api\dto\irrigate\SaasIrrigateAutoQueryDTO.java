package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

@Data
public class SaasIrrigateAutoQueryDTO extends Query {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 浇灌名称
     */
    @ApiModelProperty(name = "irrigateName", value = "浇灌名称")
    private String irrigateName;
    /**
     * 触发方式(1:定时触发 2:设备触发)
     */
    @ApiModelProperty(name = "triggerMode", value = "触发方式(1:定时触发 2:设备触发)")
    private Integer triggerMode;
    /**
     * 启用状态(0:禁用 1:启用)
     */
    @ApiModelProperty(name = "enableStatus", value = "启用状态(0:禁用 1:启用)")
    private Integer enableStatus;

}
