package com.thinkunion.park.service.common.iot.capability.device.payload;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.security.SecureRandom;
import java.util.List;
import java.util.Map;

public class CommonRequestPayload<T> {
    protected String sessionKey;
    protected String id;
    protected String version;
    protected String method;
    protected T params;

    public CommonRequestPayload(String productKey, String deviceName) {
        this();
    }

    private CommonRequestPayload() {
        this.version = "1.0";
        this.id = String.valueOf((new SecureRandom()).nextInt(1000000000));
    }

    public T getParams() {
        return this.params;
    }

    public void setParams(T params) {
        this.params = params;
    }

    public String getSessionKey() {
        return this.sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMethod() {
        return this.method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public static class CommonRequestPayloadJsonDeSerializer implements JsonDeserializer<CommonRequestPayload> {
        public CommonRequestPayloadJsonDeSerializer() {
        }

        public CommonRequestPayload deserialize(JsonElement json, Type typeOfT,
                                                JsonDeserializationContext context) throws JsonParseException {
            if (json == null) {
                return null;
            } else {
                CommonRequestPayload payload = null;
                if (!json.isJsonObject()) {
                    return payload;
                } else {
                    JsonObject payloadObject = json.getAsJsonObject();
                    if (payloadObject == null) {
                        return payload;
                    } else {
                        payload = new CommonRequestPayload();
                        Object params = null;
                        JsonElement paramsEle = payloadObject.get("params");
                        if (paramsEle != null) {
                            if (paramsEle.isJsonArray()) {
                                params = context.deserialize(paramsEle, (new TypeToken<List<String>>() {
                                }).getType());
                            } else if (paramsEle.isJsonObject()) {
                                params = context.deserialize(paramsEle, (new TypeToken<Map<String, ValueWrapper>>() {
                                }).getType());
                            } else {
                                params = context.deserialize(paramsEle, (new TypeToken<Object>() {
                                }).getType());
                            }
                        }

                        payload.setParams(params);
                        JsonElement idEle = payloadObject.get("id");
                        if (idEle != null && idEle.isJsonPrimitive()) {
                            payload.setId(idEle.getAsJsonPrimitive().getAsString());
                        }

                        JsonElement versionEle = payloadObject.get("version");
                        if (versionEle != null && versionEle.isJsonPrimitive()) {
                            payload.setVersion(versionEle.getAsJsonPrimitive().getAsString());
                        }

                        JsonElement methodEle = payloadObject.get("action");
                        if (methodEle != null && methodEle.isJsonPrimitive()) {
                            payload.setMethod(methodEle.getAsJsonPrimitive().getAsString());
                        }

                        return payload;
                    }
                }
            }
        }
    }
}
