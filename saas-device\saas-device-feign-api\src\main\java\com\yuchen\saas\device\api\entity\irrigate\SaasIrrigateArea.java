package com.yuchen.saas.device.api.entity.irrigate;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;
import org.nest.springwrap.core.tenant.mp.TenantEntity;

/**
 * <p>
 * 浇灌区域信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_irrigate_area")
@ApiModel(value="SaasIrrigateArea对象", description="浇灌区域信息表")
public class SaasIrrigateArea extends BaseEntity {

    /**
     * 区域编码（id）
     */
    @ApiModelProperty(name = "irrigateAreaCode", value = "区域编码（id）")
    private String irrigateAreaCode;
    /**
     * 区域名称
     */
    @ApiModelProperty(name = "irrigateAreaName", value = "区域名称")
    private String irrigateAreaName;
    /**
     * 区域描述
     */
    @ApiModelProperty(name = "describeContents", value = "区域描述")
    private String describeContents;
    /**
     * 绿植id（多个逗号隔开）
     */
    @ApiModelProperty(name = "greenPlantsId", value = "绿植id（多个逗号隔开）")
    private String greenPlantsId;
    /**
     * 园区ID（项目id）
     */
    @ApiModelProperty(name = "parkId", value = "园区ID（项目id）")
    private Long parkId;
    /**
     * 创建人
     */
    @ApiModelProperty(name = "createOrgUser", value = "创建人")
    private Long createOrgUser;

}
