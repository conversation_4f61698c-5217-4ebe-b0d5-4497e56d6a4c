package com.yuchen.saas.consumer.api.entity;

import lombok.Data;

/**
 * @ClassName TempMessagePaySuccessDTO
 * @Description 模版消息-退款通知-实体类
 * <AUTHOR>
 * @Date 2022/3/2 10:06
 */
@Data
public class TempMessageRefundNoticeDTO extends TempMessageBaseDTO {
    /**
     * 模版ID
     */
    private final String TEMPLATE_ID = "L7s49AuLWmwX78MgorGymnK00QQXYiY8hMACUBmDttQ";

    /**
     * 退款金额
     * ￥799.0
     */
    private String keyword1;

    /**
     * 退款方式
     * 原路退回
     */
    private String keyword2;

    /**
     * 订单信息
     * 停车费
     */
    private String keyword3;

    /**
     * 退款状态
     * 退款（成功、失败）
     */
    private String keyword4;
}
