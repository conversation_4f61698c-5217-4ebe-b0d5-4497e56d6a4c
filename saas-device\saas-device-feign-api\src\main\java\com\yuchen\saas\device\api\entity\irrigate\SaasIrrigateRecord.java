package com.yuchen.saas.device.api.entity.irrigate;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

import java.util.Date;

/**
 * <p>
 * 浇灌记录信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_irrigate_record")
@ApiModel(value="SaasIrrigateRecord对象", description="浇灌记录信息表")
public class SaasIrrigateRecord extends BaseEntity {

    /**
     * 浇灌区域ID
     */
    @ApiModelProperty(name = "irrigateAreaId", value = "浇灌区域ID")
    private Long irrigateAreaId;
    /**
     * 浇灌类型（1：自动浇灌，2：定时浇灌，3：手动浇灌，4：临时浇灌）
     */
    @ApiModelProperty(name = "irrigateType", value = "浇灌类型（1：自动浇灌，2：定时浇灌，3：手动浇灌(远程浇灌)，4：临时浇灌）")
    private Integer irrigateType;
    /**
     * 浇灌设备ID
     */
    @ApiModelProperty(name = "irrigateDeviceId", value = "浇灌设备ID")
    private Long irrigateDeviceId;
    /**
     * 自动浇灌ID
     */
    @ApiModelProperty(name = "irrigateAutoId", value = "自动浇灌ID")
    private Long irrigateAutoId;
    /**
     * 浇灌时间
     */
    @ApiModelProperty(name = "irrigateTime", value = "浇灌时间")
    private Date irrigateTime;
    /**
     * 浇灌结束时间
     */
    @ApiModelProperty(name = "irrigateEndTime", value = "浇灌结束时间")
    private Date irrigateEndTime;
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;


}
