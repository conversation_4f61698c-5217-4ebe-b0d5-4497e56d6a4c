package com.yuchen.saas.device.api.dto.operatingEnd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

@Data
public class NetProductQryReqDTO extends Query {

    /**
     * 产品来源（1：IOT产品，2：第三方产品）
     */
    @ApiModelProperty(name = "productSource", value = "产品来源（1：IOT产品，2：第三方产品）")
    private String productSource;
    /**
     * 第三方名称
     */
    @ApiModelProperty(name = "thirdPartyName", value = "第三方名称")
    private String thirdPartyName;
    /**
     * 产品分类id
     */
    @ApiModelProperty(name = "productCategoryId", value = "产品分类id")
    private Long productCategoryId;
    /**
     * 产品标识
     */
    @ApiModelProperty(name = "productKey", value = "产品标识")
    private String productKey;
    /**
     * 产品名称
     */
    @ApiModelProperty(name = "productName", value = "产品名称")
    private String productName;
    /**
     * 产品类型
     */
    @ApiModelProperty(name = "productType", value = "产品类型")
    private Integer productType;
    /**
     * 业务状态 1开启，0关闭
     */
    @ApiModelProperty("业务状态 业务状态 1开启，0关闭")
    private Integer status;


}
