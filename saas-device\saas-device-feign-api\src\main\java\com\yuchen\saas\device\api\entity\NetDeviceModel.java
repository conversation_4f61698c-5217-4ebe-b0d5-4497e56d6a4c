package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 设备型号信息表
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_device_model")
@ApiModel(value="NetDeviceModel对象", description="设备型号信息表")
public class NetDeviceModel extends BaseEntity {

    /**
     * 设备品牌id
     */
    @ApiModelProperty(name = "deviceBrandId", value = "设备品牌id")
    private Long deviceBrandId;
    /**
     * 型号编码
     */
    @ApiModelProperty(name = "modelCode", value = "型号编码")
    private String modelCode;
    /**
     * 型号名称
     */
    @ApiModelProperty(name = "modelName", value = "型号名称")
    private String modelName;
    /**
     * 协议版本
     */
    @ApiModelProperty(name = "agreementVersion", value = "协议版本")
    private String agreementVersion;
    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "deviceCategoryId", value = "设备类型")
    private Long deviceCategoryId;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;



}
