package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/30 15:53
 * @Description
 */
@Data
@ApiModel
public class BindDTO {

    @ApiModelProperty(value = "园区id")
    @NotNull(message = "园区id不能为空")
    private Long parkId;

    @ApiModelProperty(value = "土壤温湿度设备productKey")
    @NotNull(message = "土壤温湿度设备productKey不能为空")
    private String soilProductKey;

    @ApiModelProperty(value = "土壤温湿度设备deviceName")
    @NotNull(message = "土壤温湿度设备deviceName不能为空")
    private String soilDeviceName;

    @ApiModelProperty(value = "脉冲productKey")
    @NotNull(message = "脉冲productKey不能为空")
    private String impulseProductKey;

    @NotNull(message = "脉冲deviceName不能为空")
    @ApiModelProperty(value = "脉冲deviceName")
    private String impulseDeviceName;

}
