package com.yuchen.saas.device.service.feign;

import com.yuchen.saas.device.api.feign.DeviceSceneLinkageRuleClient;
import com.yuchen.saas.device.api.vo.RunDeviceSceneLinkageRuleVo;
import com.yuchen.saas.device.service.rule.RunRuleAsyncService;
import org.nest.springwrap.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/rpc/runRule")
public class DeviceSceneLinkageRuleRpcController implements DeviceSceneLinkageRuleClient {
    @Autowired
    private RunRuleAsyncService runRuleAsyncService;

    @Override
    @PostMapping("/property")
    public R propertyRunRule(@RequestBody RunDeviceSceneLinkageRuleVo ruleVo) {
        runRuleAsyncService.runRule("property",ruleVo.getDeviceMsg());
        return R.success();
    }

    @Override
    @PostMapping("/event")
    public R eventRunRule(@RequestBody RunDeviceSceneLinkageRuleVo ruleVo) {
        runRuleAsyncService.runRule("event",ruleVo.getDeviceMsg());
        return R.success();
    }

    @Override
    @PostMapping("/deviceStatusChange")
    public R deviceStatusChangeRunRule(@RequestBody RunDeviceSceneLinkageRuleVo ruleVo) {
        runRuleAsyncService.runRule("deviceStatusChange",ruleVo.getDeviceMsg());
        return R.success();
    }

}
