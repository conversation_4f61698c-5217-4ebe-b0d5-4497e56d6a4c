package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nest.springwrap.core.mp.support.Query;

/**
 * <AUTHOR>
 * @Date 2024/9/25 15:15
 * @Description TODO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NetOperateUsersQryDto extends Query {
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 推送人员手机号
     */
    @ApiModelProperty(name = "phone", value = "推送人员手机号")
    private String phone;
    /**
     * 推送人员名称
     */
    @ApiModelProperty(name = "userName", value = "推送人员名称")
    private String userName;
}
