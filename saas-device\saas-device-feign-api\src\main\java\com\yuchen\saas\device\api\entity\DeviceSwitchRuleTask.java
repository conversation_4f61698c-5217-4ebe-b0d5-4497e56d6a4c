package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: 宋少挺
 * @CreateTime: 2022/10/13 15:24
 * @Description: 设备开关组规则执行记录表
 * @Version: 1.0
 */
@Data
@ApiModel(value = "设备开关组规则执行记录表 数据表", description = "设备开关组规则执行记录表")
@TableName("device_switch_rule_task")
public class DeviceSwitchRuleTask implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("主键id")
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 开关类型 1 开 2关
     */
    @ApiModelProperty(name = "type", value = "开关类型 1 开 2关")
    private Integer type;
    /**
     * 规则id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(name = "ruleId", value = "规则id")
    private Long ruleId;
    /**
     * 设备id
     */
    @ApiModelProperty(name = "deviceId", value = "设备id")
    private String deviceId;
    /**
     * 开关点
     */
    @ApiModelProperty(name = "time", value = "开关点")
    private String time;
    /**
     * 执行时间
     */
    @ApiModelProperty(name = "createTime", value = "执行时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 执行结果 1 成功,2 失败
     */
    @ApiModelProperty(name = "resultStatus", value = "执行结果 1 成功,2 失败")
    private Integer resultStatus;
    /**
     * 执行日志
     */
    @ApiModelProperty(name = "resultLog", value = "执行日志")
    private String resultLog;

}