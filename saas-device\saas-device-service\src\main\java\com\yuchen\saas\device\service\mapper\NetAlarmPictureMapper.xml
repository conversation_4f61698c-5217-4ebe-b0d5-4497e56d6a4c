<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.NetAlarmPictureMapper">



    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, park_id, device_id, device_no, device_category_id, product_key, device_name, person_type, person_name, time_stamp, alarm_type, gender, age_range, glass_flag, glasses_style, mask_flag, hat_flag, alarm_pictur, status, create_user, create_dept, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
