package com.yuchen.saas.device.api.dto.scene;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class NetSceneSaveDto {

    /**
     * id主键
     */
    private Long id;

    /**
     * 所属项目id
     */
    private Long projectId;

    /**
     * 业务服务id
     */
    @ApiModelProperty(name = "businessId", value = "业务服务id")
    private Long businessId;

    /**
     * 场景名称
     */
    @NotNull(message = "场景名称不能为空")
    private String sceneName;

    /**
     * 场景类型，1-设备触发，2-定时触发，3-手动触发
     */
    @NotNull(message = "场景类型不能为空")
    private Integer triggerMode;

    /**
     * 备注
     */
    private String remark;

}
