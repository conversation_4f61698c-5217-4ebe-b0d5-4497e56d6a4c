package com.yuchen.saas.auth.endpoint;

import com.thinkunion.park.service.common.cache.CacheNames;
import com.wf.captcha.SpecCaptcha;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.cache.utils.CacheUtil;
import org.nest.springwrap.core.jwt.JwtUtil;
import org.nest.springwrap.core.jwt.props.JwtProperties;
import org.nest.springwrap.core.launch.constant.TokenConstant;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.nest.springwrap.core.secure.ContextUser;
import org.nest.springwrap.core.secure.utils.AuthUtil;
import org.nest.springwrap.core.tenant.annotation.NonDS;
import org.nest.springwrap.core.tool.api.R;
import org.nest.springwrap.core.tool.support.Kv;
import org.nest.springwrap.core.tool.utils.StringUtil;
import org.nest.springwrap.core.tool.utils.WebUtil;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;

import static org.nest.springwrap.core.cache.constant.CacheConstant.*;


@NonDS
@Slf4j
@RestController
@AllArgsConstructor
public class NestTokenEndPoint {
	
	private final NestRedis nestRedis;
	private final JwtProperties jwtProperties;
	
	@GetMapping("/oauth/user-info")
	public R<Authentication> currentUser(Authentication authentication) {
		return R.data(authentication);
	}
	
	@GetMapping("/oauth/captcha")
	public Kv captcha() {
		SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
		String verCode = specCaptcha.text().toLowerCase();
		String key = StringUtil.randomUUID();
		// 存入redis并设置过期时间为30分钟
		nestRedis.setEx(CacheNames.CAPTCHA_KEY + key, verCode, Duration.ofMinutes(30));
		// 将key和base64返回给前端
		return Kv.create().set("key", key).set("image", specCaptcha.toBase64());
	}
	
	@GetMapping("/oauth/logout")
	public Kv logout() {
		ContextUser user = AuthUtil.getUser();
		if (user != null && jwtProperties.getState()) {
			// 消除两次ab密码的redis数据
			String userKey = new StringBuilder(CacheNames.USER_ACCESS_AUTH).append(user.getUserId()).toString();
			nestRedis.del(userKey);
			String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
			JwtUtil.removeAccessToken(user.getTenantId(), String.valueOf(user.getUserId()), token);
		}
		return Kv.create().set("success", "true").set("msg", "success");
	}
	
	@GetMapping("/oauth/clear-cache")
	public Kv clearCache() {
		CacheUtil.clear(BIZ_CACHE);
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(DICT_CACHE);
		CacheUtil.clear(FLOW_CACHE);
		CacheUtil.clear(SYS_CACHE);
		CacheUtil.clear(PARAM_CACHE);
		CacheUtil.clear(RESOURCE_CACHE);
		CacheUtil.clear(MENU_CACHE);
		CacheUtil.clear(DICT_CACHE, Boolean.FALSE);
		CacheUtil.clear(MENU_CACHE, Boolean.FALSE);
		CacheUtil.clear(PARAM_CACHE, Boolean.FALSE);
		return Kv.create().set("success", "true").set("msg", "success");
	}
	
}
