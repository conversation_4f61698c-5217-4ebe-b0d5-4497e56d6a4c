package com.yuchen.saas.device.api.dto.operatingEnd.product;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @create 2024/5/27 15:20
 */
@Data
public class NetProductPropertiesSubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;
    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    @NotNull(message = "产品id不能为空")
    private Long productId;
    /**
     * 属性id
     */
    @ApiModelProperty(name = "propertiesId", value = "属性id")
    @NotBlank(message = "属性id不能为空")
    private String propertiesId;
    /**
     * 属性标识
     */
    @ApiModelProperty(name = "propertiesKey", value = "属性标识")
    @NotBlank(message = "属性标识不能为空")
    private String propertiesKey;
    /**
     * 属性名称
     */
    @ApiModelProperty(name = "propertiesName", value = "属性名称")
    @NotBlank(message = "属性名称不能为空")
    private String propertiesName;
    /**
     * NetDataTypeEnum
     * 属性类型
     * 1：int(整数型)
     * 2：long(长整数型)
     * 3：float(单精度浮点型)
     * 4：double(双精度浮点型)
     * 5：text(字符串)
     * 6：bool(布尔型)
     * 7：time(时间类型)
     */
    @ApiModelProperty(name = "dataType", value = "属性类型")
    @NotNull(message = "属性类型不能为空")
    private Integer dataType;
    /**
     * 精度
     */
    @ApiModelProperty(name = "precise", value = "精度")
    private Integer precise;
    /**
     * 单位
     */
    @ApiModelProperty(name = "unit", value = "单位")
    private String unit;
    /**
     * 0-只读，1-读写
     */
    @ApiModelProperty(name = "isRead", value = "0-只读，1-读写")
    private Integer isRead;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
