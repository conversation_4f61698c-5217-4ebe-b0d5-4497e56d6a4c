package com.thinkunion.park.service.common.iot.util;


import com.thinkunion.park.service.common.iot.capability.devicemodel.DeviceModel;

import java.util.Random;

public class TextHelper {

    protected static final String TAG = "[Tmp]TextHelper";
    protected static Random sRandom = new Random();
    static final String RANDOM_BYTE = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    public TextHelper() {
    }

    public static String combineStr(String first, String second) {
        return first + second;
    }

    public static String combineStr(String first, String second, String third) {
        return first + second + third;
    }

    public static String queryEventIdentifier(String method) {
        String identifier = null;
        if (TextUtils.isEmpty(method)) {
            return identifier;
        } else if (method.equalsIgnoreCase("thing.event.prop.post")) {
            return "post";
        } else {
            String[] segmentList = method.split("\\.");
            if (segmentList != null && segmentList.length > 1) {
                if ("post".equalsIgnoreCase(segmentList[segmentList.length - 1])) {
                    identifier = segmentList[segmentList.length - 2];
                } else {
                    identifier = segmentList[segmentList.length - 1];
                }
            }

            return identifier;
        }
    }

    public static String getRandomString() {
        return getRandomString(16);
    }

    public static String getRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; ++i) {
            sb.append("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
                    .charAt(sRandom
                            .nextInt("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".length())));
        }

        return sb.toString();
    }

    public static String formatDownRawTopic(String pk, String dn) {
        return "/" + "sys" + "/" + pk + "/" + dn + "/thing" + "/model" + "/" + "down_raw";
    }

    public static String formatPostReplyTopic(String pk, String dn) {
        return "/" + "sys" + "/" + pk + "/" + dn + "/" + "thing/event/property" + "/" + "post_reply";
    }

    public static String formatDownRawId(String pk, String dn) {
        return pk + dn + "/" + "raw_data_down";
    }

    public static String formatTopic(String pk, String dn, String path) {
        return "/" + "dev" + "/" + pk + "/" + dn + path;
    }

    public static String getTopicStr(DeviceModel deviceModel, String id) {
        String topic = null;
        ResElementType type = getIdType(deviceModel, id);
        if (!TextUtils.isEmpty(id) && type != null) {
            StringBuilder builder = null;
            switch (type) {
                case PROPERTY:
                    builder = (new StringBuilder("mb")).append("/").append(deviceModel.getProfile().getProductKey())
                            .append("/").append(deviceModel.getProfile().getVersion());
                    builder.append("/").append("thing/service/prop").append("/").append(id);
                    break;
                case SERVICE:
                    builder = (new StringBuilder("mb")).append("/").append(deviceModel.getProfile().getProductKey())
                            .append("/").append(deviceModel.getProfile().getVersion());
                    builder.append("/").append("thing/service").append("/").append(id);
                    break;
                case EVENT:
                    builder = (new StringBuilder("mb")).append("/").append(deviceModel.getProfile().getProductKey())
                            .append("/").append(deviceModel.getProfile().getVersion());
                    if (id.equalsIgnoreCase("post")) {
                        builder.append("/").append("thing/event/property").append("/").append(id);
                    } else {
                        builder.append("/").append("thing/event").append("/").append(id).append("/").append("post");
                    }
            }

            if (builder != null) {
                topic = builder.toString();
            }

            return topic;
        } else {
            return topic;
        }
    }

    public static ResElementType getIdType(DeviceModel model, String identifier) {
        if (TextUtils.isEmpty(identifier)) {
            return ResElementType.SERVICE;
        } else if (identifier.equalsIgnoreCase("dev")) {
            return ResElementType.DISCOVERY;
        } else if (!identifier.equalsIgnoreCase("get") && !identifier.equalsIgnoreCase("set")) {
            if (model != null && !TextUtils.isEmpty(model.getEventAction(identifier))) {
                return ResElementType.EVENT;
            } else {
                return model != null && !TextUtils.isEmpty(model.getServiceAction(identifier)) ? ResElementType.SERVICE
                        : ResElementType.ALCS;
            }
        } else {
            return ResElementType.PROPERTY;
        }
    }

    public static enum ResElementType {
        PROPERTY,
        SERVICE,
        EVENT,
        DISCOVERY,
        ALCS;

        private ResElementType() {
        }
    }
}
