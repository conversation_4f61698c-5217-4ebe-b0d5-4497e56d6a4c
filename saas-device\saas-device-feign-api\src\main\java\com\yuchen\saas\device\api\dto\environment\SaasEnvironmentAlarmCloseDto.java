package com.yuchen.saas.device.api.dto.environment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class SaasEnvironmentAlarmCloseDto {

    /**
     * 主键ID列表
     */
    @ApiModelProperty(name = "ids", value = "主键ID列表")
    private List<Long> ids;
    /**
     * 关闭原因(1:已派工 2:已解决 3:无需处理 4:重复告警 5:其他)
     */
    @ApiModelProperty(name = "closeTime", value = "关闭原因(1:已派工 2:已解决 3:无需处理 4:重复告警 5:其他)")
    private Integer closeReason;
    /**
     * 关闭备注
     */
    @ApiModelProperty(name = "closeRemark", value = "关闭备注")
    private String closeRemark;

}
