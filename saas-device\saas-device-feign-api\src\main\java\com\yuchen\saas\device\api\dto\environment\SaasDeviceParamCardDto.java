package com.yuchen.saas.device.api.dto.environment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SaasDeviceParamCardDto {
    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID")
    private Long id;
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 设备ID
     */
    @ApiModelProperty(name = "deviceId", value = "设备ID")
    private Long deviceId;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceName", value = "设备名称")
    private String deviceName;
    /**
     * 参数ID
     */
    @ApiModelProperty(name = "paramId", value = "参数ID")
    private Long paramId;
    /**
     * 参数名称
     */
    @ApiModelProperty(name = "paramName", value = "参数名称")
    private String paramName;
    /**
     * 参数别名
     */
    @ApiModelProperty(name = "paramAlias", value = "参数别名")
    private String paramAlias;
    /**
     * 展示方式(1:开关控制 2:状态切换 3:状态显示 4:数值显示)
     */
    @ApiModelProperty(name = "showType", value = "展示方式(1:开关控制 2:状态切换 3:状态显示 4:数值显示)")
    private Integer showType;
    /**
     * 小数位数
     */
    @ApiModelProperty(name = "decimalDigit", value = "小数位数")
    private Integer decimalDigit;
    /**
     * 字体大小
     */
    @ApiModelProperty(name = "fontSize", value = "字体大小")
    private Integer fontSize;
    /**
     * 字体颜色
     */
    @ApiModelProperty(name = "fontColor", value = "字体颜色")
    private String fontColor;
    /**
     * 是否显示图标(0:否 1:是)
     */
    @ApiModelProperty(name = "isShowIcon", value = "是否显示图标(0:否 1:是)")
    private Integer isShowIcon;
    /**
     * 图标
     */
    @ApiModelProperty(name = "iconUrl", value = "图标")
    private String iconUrl;
    /**
     * 参数状态列表
     */
    @ApiModelProperty(name = "paramStatusList", value = "参数状态列表")
    private List<ParamStatus> paramStatusList;

    @Data
    public static class ParamStatus {
        /**
         * 状态
         */
        @ApiModelProperty(name = "statusStr", value = "状态")
        private String statusStr;
        /**
         * 匹配规则(1:等于 2:不等于 3:大于 4:大于等于 5:小于 6:小于等于 7:在…之间 8:不在…之间)
         */
        @ApiModelProperty(name = "matchRule", value = "匹配规则(1:等于 2:不等于 3:大于 4:大于等于 5:小于 6:小于等于 7:在…之间 8:不在…之间)")
        private Integer matchRule;
        /**
         * 匹配值
         */
        @ApiModelProperty(name = "matchValue", value = "匹配值")
        private String matchValue;
        /**
         * 状态字体颜色
         */
        @ApiModelProperty(name = "statusFontColor", value = "状态字体颜色")
        private String statusFontColor;
        /**
         * 状态字体大小
         */
        @ApiModelProperty(name = "statusFontSize", value = "状态字体大小")
        private Integer statusFontSize;
        /**
         * 显示内容(1:状态名称 2：状态值 3:混合显示(名称+值))
         */
        @ApiModelProperty(name = "showContent", value = "显示内容(1:状态名称 2：状态值 3:混合显示(名称+值))")
        private Integer showContent;
        /**
         * 使用图片
         */
        @ApiModelProperty(name = "imageUrl", value = "使用图片")
        private String imageUrl;
    }


}
