package com.thinkunion.park.service.common.utils;

/**
 * @description: TODO
 * <AUTHOR>
 * @date 2022/7/19 下午5:05
 * @version 0.3.0
 */
public class PlateNoUtil {

    public static final String EDGE_NOTIFICATION_ANONYMOUS_PLATE_NO = "无车牌";

    public static final String WS_NOTIFICATION_ANONYMOUS_PLATE_NO = "无牌车";

    public static final String ANONYMOUS_PLATE_NO_PREFIX_FOR_CREATE = "无牌车";

    public static final String ANONYMOUS_PLATE_NO_PREFIX_FOR_PASS = "无车牌";

    public static boolean isPassAnonymousPlateNo(String plateNo) {
        return plateNo != null && plateNo.contains(ANONYMOUS_PLATE_NO_PREFIX_FOR_PASS);
    }

    public static String createRegisterAnonymousPlateNo() {
        return ANONYMOUS_PLATE_NO_PREFIX_FOR_CREATE.concat(StringGenerator.GetRandomString(7).toUpperCase());
    }

    public static String createPassAnonymousPlateNo(String eventId) {
        return ANONYMOUS_PLATE_NO_PREFIX_FOR_PASS.concat(eventId.substring(eventId.length() - 7).toUpperCase());
    }
}


