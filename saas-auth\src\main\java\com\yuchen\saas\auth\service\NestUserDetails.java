package com.yuchen.saas.auth.service;

import lombok.Getter;
import org.nest.springwrap.core.jwt.entity.MenuRoot;
import org.nest.springwrap.core.tool.support.Kv;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;
import java.util.List;

@Getter
public class NestUserDetails extends User {

	private final Long userId;
	
	private final String tenantId;
	
	private final String oauthId;
	
	private final String name;
	
	private final String realName;
	
	private final String account;
	
	private final String deptId;
	
	private final String postId;
	
	private final String roleId;
	
	private final String roleName;
	
	private final String avatar;
	
	private final Kv detail;

	private final String parkId;

	private final Long userInfoId;
	private final Integer isAdmin;
	private final List<Long> menuIds;
	private final List<Long> deptRoleIds ;

	private final Long userDetailId;

	private final MenuRoot menuRoot;

	private final Long orgUserId;
	/**
	 * 客户id
	 */
	private final Long customerManageId;

	/**
	 * 是否是超级管理员客户
	 */
	private final Integer isSuperAdmin;

	/**
	 * 登录的身份,管理员,员工
	 */
	private final String idEntity;
	
	public NestUserDetails(Long userId, String tenantId, String oauthId, String name, String realName, String deptId, String postId,
						   String roleId, String roleName, String avatar, String username,
						   String password,String parkId, Kv detail, boolean enabled, boolean accountNonExpired, boolean credentialsNonExpired,
						   boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities,Long userInfoId,Integer isAdmin,List<Long> menuIds,
						   List<Long> deptRoleIds, Long userDetailId,MenuRoot menuRoot,Long orgUserId,Long customerManageId,Integer isSuperAdmin,String idEntity) {
		super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
		this.userId = userId;
		this.tenantId = tenantId;
		this.oauthId = oauthId;
		this.name = name;
		this.realName = realName;
		this.account = username;
		this.deptId = deptId;
		this.postId = postId;
		this.roleId = roleId;
		this.roleName = roleName;
		this.avatar = avatar;
		this.detail = detail;
		this.parkId = parkId;
		this.userInfoId = userInfoId;
		this.isAdmin = isAdmin;
		this.menuIds=menuIds;
		this.deptRoleIds=deptRoleIds;
		this.userDetailId=userDetailId;
		this.menuRoot=menuRoot;
		this.orgUserId=orgUserId;
		this.customerManageId=customerManageId;
		this.isSuperAdmin=isSuperAdmin;
		this.idEntity=idEntity;

	}
}
