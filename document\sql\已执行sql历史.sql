-- 此处为 更新项目所执行的sql历史记录

-- 增加停车场优惠券表
CREATE TABLE `pmg_bat_parking_coupon` (
                                          `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                          `discount_type` int(2) DEFAULT NULL COMMENT '优惠类型',
                                          `discount_number` int(32) DEFAULT NULL COMMENT '优惠数值',
                                          `total_discount_number` int(32) DEFAULT NULL COMMENT '总优惠金额数值',
                                          `begin_time` datetime DEFAULT NULL COMMENT '开始时间',
                                          `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                          `remark` varchar(256) DEFAULT null COMMENT '备注信息',

                                          `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                          `status` int(2) DEFAULT NULL COMMENT '状态',
                                          `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                          `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='停车场优惠券表';

-- 增加停车场优惠券使用记录表
CREATE TABLE `pmg_bat_parking_coupon_record` (
                                                 `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                 `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                                 `coupon_id` bigint(64) NOT NULL COMMENT '优惠券ID',
                                                 `bill_id` bigint(64) NOT NULL COMMENT '停车账单ID',
                                                 `discount_number` int(32) DEFAULT NULL COMMENT '优惠数值',
                                                 `plate_no` varchar(20) DEFAULT NULL COMMENT '车牌',

                                                 `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                                 `status` int(2) DEFAULT NULL COMMENT '状态',
                                                 `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                                 `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                 `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                                 PRIMARY KEY (`id`),
                                                 KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='停车场优惠券使用记录表';

-- 人脸抓拍事件数据表
CREATE TABLE `pmg_bat_face_capture_event_data` (
                                                   `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                   `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                                   `pic_url` varchar(255) NOT NULL COMMENT '抓拍图片url',
                                                   `face_id` varchar(255) NOT NULL COMMENT '人脸id',
                                                   `match_url` varchar(255) NOT NULL COMMENT '抓拍图片url',
                                                   `f_similarity` varchar(10) DEFAULT NULL COMMENT '相似度',

                                                   `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                                   `status` int(2) DEFAULT NULL COMMENT '状态',
                                                   `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                                   `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                                   PRIMARY KEY (`id`),
                                                   KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人脸抓拍事件数据表';

-- 用户车辆数据表
CREATE TABLE `user_vehicle_data` (
                                     `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',

                                     `pay_user_id` varchar(64) DEFAULT NULL COMMENT '移动端用户ID',
                                     `plate_no` varchar(20) DEFAULT NULL COMMENT '车牌号',


                                     `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                     `status` int(2) DEFAULT NULL COMMENT '状态',
                                     `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                     `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户车辆数据表';

-- 停车场 车辆月卡数据表
CREATE TABLE `vehicle_monthly_card_data` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                             `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                             `pay_user_id` varchar(64) DEFAULT NULL COMMENT '移动端用户ID',
                                             `parking_id` bigint(20) DEFAULT NULL COMMENT '停车场ID',
                                             `plate_no` varchar(20) DEFAULT NULL COMMENT '车牌号',
                                             `person_name` varchar(20) DEFAULT NULL COMMENT '车主姓名',
                                             `person_phone` varchar(20) DEFAULT NULL COMMENT '手机号',
                                             `vehicle_type` int(11) DEFAULT '0' COMMENT '车辆类型（0：其他车、1：小型车、2：大型车、3：摩托车）',
                                             `energy_type` int(11) DEFAULT '0' COMMENT '能源类型（0：其他类型、1：汽油车、2：新能源）',
                                             `reason` varchar(255) DEFAULT NULL COMMENT '进入理由',
                                             `file` varchar(500) DEFAULT NULL COMMENT '申请附件图片地址 逗号隔开',
                                             `review_state` int(11) DEFAULT '0' COMMENT '审核状态（0 待审核 1 审核通过 2 拒绝）',
                                             `active_state` int(11) DEFAULT '0' COMMENT '月卡状态（0 未缴费 1 正常使用 2 已过期）',
                                             `review_name` varchar(80) DEFAULT NULL COMMENT '审批人姓名',
                                             `review_time` datetime DEFAULT NULL COMMENT '审核时间',
                                             `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                             `rule_id` bigint(20) DEFAULT NULL COMMENT '包期规则ID',
                                             `begin_time` datetime DEFAULT NULL COMMENT '包租 开始时间 ',
                                             `end_time` datetime DEFAULT NULL COMMENT '包租 结束时间',
                                             `free_state` int(11) DEFAULT '0' COMMENT '免费状态 0 免费 1 不免费',

                                             `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                             `status` int(11) DEFAULT NULL COMMENT '状态',
                                             `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                             `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                             PRIMARY KEY (`id`) USING BTREE,
                                             KEY `idx_id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='停车场-车辆月卡数据表';

-- 停车场 车辆月卡支付表
CREATE TABLE `vehicle_monthly_card_bill_data` (
                                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                  `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                                  `vehicle_monthly_card_id` bigint(20) DEFAULT NULL COMMENT '月卡ID',
                                                  `car_id` bigint(20) DEFAULT NULL COMMENT '车辆ID',
                                                  `pay_user_id` varchar(64) DEFAULT NULL COMMENT '移动端用户ID',
                                                  `month_number` int(11) DEFAULT null COMMENT '包月数量',
                                                  `total_amount` bigint(20) DEFAULT NULL COMMENT '总金额 分',
                                                  `pay_type` int(11) DEFAULT NULL COMMENT '支付类型 | 0 后台充值 1 微信充值',
                                                  `pay_status` int(11) DEFAULT NULL COMMENT '支付状态 | 0 支付中 1 支付成功',
                                                  `invoicing_status` int(11) DEFAULT null COMMENT '开票状态 | 0 开票中 1 开票成功 2 开票失败 3 红冲 4 被红冲 9 未开票',
                                                  `begin_time` datetime DEFAULT NULL COMMENT '包租 开始时间 ',
                                                  `end_time` datetime DEFAULT NULL COMMENT '包租 结束时间',

                                                  `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                                  `status` int(11) DEFAULT NULL COMMENT '状态',
                                                  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                                  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                  `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                                  PRIMARY KEY (`id`) USING BTREE,
                                                  KEY `idx_id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='停车场-车辆月卡账单表';

-- 瑞宏-园区-发票配置表
CREATE TABLE `park_invoice` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                `app_code` varchar(64) DEFAULT NULL COMMENT '由电子发票平台分配的 appCode',
                                `key_store_path` varchar(255) DEFAULT NULL COMMENT '密钥库文件路径',
                                `alias` varchar(64) DEFAULT NULL COMMENT '密钥库别名',
                                `password` varchar(64) DEFAULT NULL COMMENT '密钥库密码',
                                `api_url` varchar(255) DEFAULT NULL COMMENT '请求的api地址',
                                `test_Taxpayer_code` varchar(255) DEFAULT NULL COMMENT '销售方纳税人识别号',

                                `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                `status` int(11) DEFAULT NULL COMMENT '状态',
                                `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                PRIMARY KEY (`id`) USING BTREE,
                                KEY `idx_id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='瑞宏-园区-发票配置表';

-- 给厦门银行支付配置表增加 appid 字段
ALTER TABLE pmg_bat_park_xc_pay ADD app_id varchar(64) DEFAULT NULL COMMENT '小程序APPID';

-- 给发票主表增加 园区ID 字段
ALTER TABLE pmg_bat_park_invoice_order ADD park_id bigint(64) NOT NULL COMMENT '园区ID';


-- 海康 ISC 访客权限组
CREATE TABLE `hik_isc_privilege_group` (
                                           `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                           `privilege_group_id` varchar(256) DEFAULT NULL COMMENT '权限组ID, 用于访客登记时对访客授权',
                                           `privilege_group_name` varchar(256) DEFAULT NULL COMMENT '权限组名称',
                                           `remark` varchar(256) DEFAULT NULL COMMENT '描述',
                                           `is_default` int(2) DEFAULT NULL COMMENT '是否是默认权限组 0: 是、1: 否',

                                           `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                           `status` int(2) DEFAULT NULL COMMENT '状态',
                                           `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                           `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='海康 ISC 访客权限组';

-- 海康 isc 人员 权限关联表
CREATE TABLE `hik_isc_people_privilege` (
                                            `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                            `people_id` bigint(64) DEFAULT NULL COMMENT '海康 isc 人员ID',
                                            `privilege_id` bigint(64) DEFAULT NULL COMMENT '海康 isc 访客权限ID',

                                            `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                            `status` int(2) DEFAULT NULL COMMENT '状态',
                                            `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                            `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='海康 isc 人员 权限关联表';

-- 访客授权审批表
CREATE TABLE `park_visitor_empower` (
                                        `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                        `people_id` bigint(64) DEFAULT NULL COMMENT '海康 isc 人员信息ID',
                                        `name` varchar(20) DEFAULT NULL COMMENT '授权人姓名',
                                        `phone` varchar(20) DEFAULT NULL COMMENT '授权人手机号',
                                        `begin_time` datetime DEFAULT NULL COMMENT '授权开始时间',
                                        `end_time` datetime DEFAULT NULL COMMENT '授权结束时间',

                                        `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                        `status` int(2) DEFAULT NULL COMMENT '状态',
                                        `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                        `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='访客授权审批表';

-- 给访客人员信息增加 审核人手机号 字段
ALTER TABLE pmg_bat_park_visitor ADD audit_phone varchar(20) DEFAULT NULL COMMENT '审核人手机号';

-- 给访客人员信息增加 接待房间号 字段
ALTER TABLE pmg_bat_park_visitor ADD room_number varchar(50) DEFAULT NULL COMMENT '接待房间号';

-- 临停账单表增加 账单子类型 字段
ALTER TABLE pmg_bat_park_bill ADD sub_bill_type int(11) DEFAULT NULL COMMENT '账单子类型（团队车临停）';

-- 临停账单表增加 账单子类型 字段
ALTER TABLE pmg_bat_park_bill_log ADD sub_bill_type int(11) DEFAULT NULL COMMENT '账单子类型（团队车临停）';

-- 设备属性数据表
CREATE TABLE `pmg_bat_device_data` (
                                       `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                       `product_key` varchar(64) DEFAULT NULL COMMENT '设备productKey',
                                       `device_name` varchar(64) DEFAULT NULL COMMENT '设备deviceName',
                                       `identity` varchar(128) DEFAULT NULL COMMENT '属性标识',
                                       `value` varchar(64) DEFAULT NULL COMMENT '属性值',
                                       `event_time` datetime DEFAULT NULL COMMENT '事件时间',

                                       `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                       `status` int(2) DEFAULT NULL COMMENT '状态',
                                       `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                       `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备属性数据表';

-- 充电记录表-云快充
CREATE TABLE `parking_charging_record` (
                                           `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                           `port_name` varchar(64) DEFAULT NULL COMMENT '第三方账号（道闸停车方提供）',
                                           `order_no` varchar(64) DEFAULT NULL COMMENT '订单号(充电桩订单号)',
                                           `plate_no` varchar(64) DEFAULT NULL COMMENT '车牌号(全车牌)',
                                           `start_time` varchar(64) DEFAULT NULL COMMENT '开始时间',
                                           `end_time` varchar(64) DEFAULT NULL COMMENT '结束时间',
                                           `station_id` varchar(64) DEFAULT NULL COMMENT '电站ID',
                                           `station_name` varchar(64) DEFAULT NULL COMMENT '电站名称',
                                           `device_id` varchar(64) DEFAULT NULL COMMENT '设备ID',
                                           `device_name` varchar(64) DEFAULT NULL COMMENT '设备名称',
                                           `space_no` varchar(64) DEFAULT NULL COMMENT '车位号',
                                           `power` varchar(64) DEFAULT NULL COMMENT '充电量',
                                           `elec_money` varchar(64) DEFAULT NULL COMMENT '电费',
                                           `sevice_money` varchar(64) DEFAULT NULL COMMENT '服务费/附加费',
                                           `total_money` varchar(64) DEFAULT NULL COMMENT '总费用',
                                           `sign` varchar(64) DEFAULT NULL COMMENT '签名',

                                           `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                           `status` int(2) DEFAULT NULL COMMENT '状态',
                                           `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                           `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充电记录表-云快充';


-- 给优惠券表增加 优惠券二维码 字段
ALTER TABLE pmg_bat_parking_coupon ADD coupon_img varchar(128) DEFAULT NULL COMMENT '优惠券二维码';
-- 给优惠券表增加 联系人 字段
ALTER TABLE pmg_bat_parking_coupon ADD name varchar(16) DEFAULT NULL COMMENT '联系人';
-- 给优惠券表增加 联系人电话 字段
ALTER TABLE pmg_bat_parking_coupon ADD phone varchar(11) DEFAULT NULL COMMENT '联系人电话';

-- 给园内人员信息表 新增字段: 人员组织code
ALTER TABLE pmg_bat_park_people ADD org_index_code varchar(255) DEFAULT null COMMENT '人员组织code'  AFTER people_type;

-- 添加数据清除配置表
CREATE TABLE `sys_history_del_table` (
                                         `id` bigint(20) NOT NULL COMMENT '主键',
                                         `park_id` varchar(20) DEFAULT NULL COMMENT '园区id',
                                         `table_name` varchar(45) DEFAULT NULL COMMENT '需要删除的表名',
                                         `between_month` int(11) DEFAULT NULL COMMENT '间隔月份',
                                         `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                         `status` int(11) DEFAULT NULL COMMENT '状态',
                                         `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                         `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         KEY `idx_id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='删除历史数据配置表';


-- 更换车牌审核记录表
CREATE TABLE `replace_plate_no_record` (
                                           `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                           `vehicle_monthly_card_id` bigint(64) DEFAULT NULL COMMENT '月卡登记ID',
                                           `person_name` varchar(20) DEFAULT NULL COMMENT '车主姓名',
                                           `person_phone` varchar(20) DEFAULT NULL COMMENT '手机号',
                                           `begin_time` datetime DEFAULT NULL COMMENT '包租 开始时间 ',
                                           `end_time` datetime DEFAULT NULL COMMENT '包租 结束时间',
                                           `old_plate_no` varchar(10) DEFAULT NULL COMMENT '旧车牌号码',
                                           `new_plate_no` varchar(10) DEFAULT NULL COMMENT '新车牌号码',
                                           `reason` varchar(128) DEFAULT NULL COMMENT '变更理由',
                                           `review_state` int(2) DEFAULT 0 COMMENT '审核状态（0 待审核 1 审核通过 2 拒绝）',
                                           `review_name` varchar(80) DEFAULT NULL COMMENT '审批人姓名',
                                           `review_time` datetime DEFAULT NULL COMMENT '审核时间',

                                           `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                           `status` int(2) DEFAULT NULL COMMENT '状态',
                                           `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                           `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='更换车牌审核记录表';

-- 删除无用的表
DROP TABLE pmg_bat_coupon;
DROP TABLE pmg_bat_coupon_detail;

-- 给园内人员信息表,新增字段 : free_state
ALTER TABLE `hik_isc_people` ADD `free_state` int(11) DEFAULT '0' COMMENT '免费状态 0 免费 1 不免费'  AFTER `phone`;

-- 给优惠券表添加优惠总金额字段 : total_amount
ALTER TABLE `pmg_bat_parking_coupon` ADD `total_amount` int(32) DEFAULT NULL COMMENT '总优惠金额数值'  AFTER `total_discount_number`;
-- 给优惠券记录表添加字段: create_phone user_phone use_mark begin_time expire_time coupon_img
ALTER TABLE `pmg_bat_parking_coupon_record` ADD `create_phone` varchar(11) DEFAULT NULL COMMENT '创建人号码' AFTER `plate_no`;
ALTER TABLE `pmg_bat_parking_coupon_record` ADD `user_phone` varchar(11) DEFAULT NULL COMMENT '使用人号码' AFTER `create_phone`;
ALTER TABLE `pmg_bat_parking_coupon_record` ADD `use_mark` int(2) DEFAULT 0 COMMENT '使用标记 0未使用 ,1已使用' AFTER `user_phone`;
ALTER TABLE `pmg_bat_parking_coupon_record` ADD `begin_time`  datetime DEFAULT NULL COMMENT '开始时间' AFTER `use_mark`;
ALTER TABLE `pmg_bat_parking_coupon_record` ADD `expire_time` datetime DEFAULT NULL COMMENT '过期时间' AFTER `begin_time`;
ALTER TABLE `pmg_bat_parking_coupon_record` ADD `coupon_img` varchar(128) DEFAULT NULL COMMENT '优惠券二维码' AFTER `expire_time`;

-- 去除优惠券记录表 bill_id 的is not null 约束
ALTER TABLE `pmg_bat_parking_coupon_record` MODIFY `bill_id` BIGINT(64) NULL;

-- 给优惠券表添加使用状态标记 : use_mark
ALTER TABLE `pmg_bat_parking_coupon` ADD `use_mark` int(2) DEFAULT 0 COMMENT '状态标记 0可使用 , 1已过期' AFTER `end_time`;

-- 审核记录表
CREATE TABLE `review_record` (
                                 `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                 `audit_subject_id` bigint(64) DEFAULT NULL COMMENT '审核主体ID',
                                 `audit_subject_table` varchar(128) DEFAULT NULL COMMENT '审核主体表',
                                 `review_name` varchar(64) DEFAULT NULL COMMENT '审批人姓名',
                                 `review_time` datetime DEFAULT NULL COMMENT '审核时间',
                                 `review_state` int(2) DEFAULT 0 COMMENT '审核状态',
                                 `remark` varchar(128) DEFAULT NULL COMMENT '备注',

                                 `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                 `status` int(2) DEFAULT NULL COMMENT '状态',
                                 `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                 `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                 `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审核记录表';

-- 园区空间信息表
CREATE TABLE `park_space_info` (
                                   `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                   `name` varchar(64) DEFAULT NULL COMMENT '空间名称',
                                   `parent_id` bigint(64) DEFAULT NULL COMMENT '父主键',
                                   `type` int(2) DEFAULT 0 COMMENT '类型（1：层、2：区、3：建筑）',
                                   `sort` int(2) DEFAULT 0 COMMENT '排序',
                                   `remark` varchar(128) DEFAULT NULL COMMENT '备注',

                                   `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                   `status` int(2) DEFAULT NULL COMMENT '状态',
                                   `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                   `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                   PRIMARY KEY (`id`),
                                   KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='园区空间信息表';



-- 2023-01-06 开始
alter table pmg_bat_park_pay_refund add column saas_user_id bigint default null comment '微信用户id';
alter table pmg_bat_park_pay_refund add column review_user_id bigint default null comment '审核人';
alter table pmg_bat_park_pay_refund add column review_user_name varchar(200) default null comment '审核人';
alter table pmg_bat_park_pay_refund add column review_time datetime default null comment '审核时间';


alter table pmg_bat_park_company add column province_name varchar(200) DEFAULT NULL COMMENT '省名称';
alter table pmg_bat_park_company add column city_name varchar(200) DEFAULT NULL COMMENT '市名称';
alter table pmg_bat_park_company add column county_name varchar(200) DEFAULT NULL COMMENT '区/县名称';
alter table pmg_bat_park_company add column town_name varchar(200) DEFAULT NULL COMMENT '镇/街道名称';
alter table pmg_bat_park_company add column detail_address varchar(200) DEFAULT NULL COMMENT '详细地址';

alter table pmg_bat_park_company add column settle_time datetime DEFAULT NULL COMMENT '入驻日期';
alter table pmg_bat_park_company add column settle_file varchar(200) DEFAULT NULL COMMENT '入驻附件';
alter table pmg_bat_park_company add column settle_area varchar(200) DEFAULT NULL COMMENT '入驻面积';
alter table pmg_bat_park_company add column settle_effective_time_start datetime DEFAULT NULL COMMENT '入驻有效期开始';
alter table pmg_bat_park_company add column settle_effective_time_end datetime DEFAULT NULL COMMENT '入驻有效期结束';

alter table pmg_bat_park_company add column credit_type int DEFAULT 1 COMMENT '证件类型-1：统一信用代码';
alter table pmg_bat_park_company add column credit_code varchar(100) DEFAULT NULL COMMENT '统一信用代码';

alter table pmg_bat_park_company add column parent_id bigint DEFAULT 0 COMMENT '父企业id';





CREATE TABLE `saas_user` (
                             `id` bigint(20) NOT NULL COMMENT 'id主键',
                             `name` varchar(100) DEFAULT NULL COMMENT '姓名',
                             `sex` int DEFAULT NULL COMMENT '性别',
                             `phone` varchar(100) DEFAULT NULL COMMENT '电话',
                             `source` int(11) DEFAULT 1 COMMENT '注册来源1小程序',
                             `birthday` datetime DEFAULT null COMMENT '生日',
                             `idcard` varchar(100) DEFAULT NULL COMMENT '证件号码',
                             `pic` varchar(200) DEFAULT NULL COMMENT '个人照片',
                             `contact_phone` varchar(100) DEFAULT NULL COMMENT '电话',
                             `contact_province` varchar(200) DEFAULT NULL COMMENT '省',
                             `contact_province_name` varchar(255) DEFAULT NULL COMMENT '省名称',
                             `contact_city` varchar(200) DEFAULT NULL COMMENT '市',
                             `contact_city_name` varchar(255) DEFAULT NULL COMMENT '市名称',
                             `contact_county` varchar(200) DEFAULT NULL COMMENT '区/县',
                             `contact_county_name` varchar(255) DEFAULT NULL COMMENT '区/县名称',
                             `contact_town` varchar(200) DEFAULT NULL COMMENT '镇/街道',
                             `contact_town_name` varchar(255) DEFAULT NULL COMMENT '镇/街道名称',
                             `contact_address` varchar(200) DEFAULT NULL COMMENT '详细地址',
                             `idcard_effective_time` datetime DEFAULT NULL COMMENT '身份证有效期',
                             `idcard_pic_front` varchar(200) DEFAULT NULL COMMENT '身份证正面',
                             `idcard_pic_reverse` varchar(200) DEFAULT NULL COMMENT '身份证反面',
                             `remark` datetime DEFAULT NULL COMMENT '审核备注',
                             `audit_submit_time` datetime DEFAULT NULL COMMENT '认证提交时间',
                             `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
                             `audit_user` bigint(20) DEFAULT NULL COMMENT '审核人',
                             `audit_user_name` varchar(200) DEFAULT NULL COMMENT '审核人',
                             `reason` varchar(200) DEFAULT NULL COMMENT '审核原因',
                             `cer_status` int(11) DEFAULT 0 COMMENT '审核状态',
                             `status` int(11) DEFAULT NULL COMMENT '状态 1',
                             `public_parking_bill` int(11) DEFAULT 1 COMMENT '允许他人查看我停车信息1允许，2不允许',
                             `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                             `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                             `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                             `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基本信息';

CREATE TABLE `saas_wechat_platform` (
                                        `id` bigint(20) NOT NULL COMMENT 'id主键',
                                        `name` varchar(100) DEFAULT NULL COMMENT '名称',
                                        `park_id` bigint(20) DEFAULT NULL COMMENT '园区id',
                                        `appid` varchar(100) DEFAULT NULL COMMENT 'appid',
                                        `appsecurity` int(11) DEFAULT NULL COMMENT 'appsecurity',
                                        `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                        `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        `status` int(11) DEFAULT NULL COMMENT '状态 1',
                                        `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信平台账号';

CREATE TABLE `saas_wechat_user` (
                                    `id` bigint(20) NOT NULL COMMENT 'id主键',
                                    `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
                                    `appid` varchar(100) DEFAULT NULL COMMENT 'appid',
                                    `wechat_name` varchar(100) DEFAULT NULL COMMENT '微信昵称',
                                    `wechat_pic` varchar(100) DEFAULT NULL COMMENT '微信头像',
                                    `openid` varchar(100) DEFAULT NULL COMMENT 'openid',
                                    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                    `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    `status` int(11) DEFAULT NULL COMMENT '状态 1',
                                    `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户-微信平台 openid';

CREATE TABLE `saas_company` (
                                `id` bigint(20) NOT NULL COMMENT 'id主键',
                                `parent_id` bigint(20) DEFAULT 0 COMMENT '父级id',
                                `company_name` varchar(100) DEFAULT NULL COMMENT '企业名称',
                                `company_trade` varchar(100) DEFAULT NULL COMMENT '所属行业',
                                `company_scope` varchar(100) DEFAULT NULL COMMENT '企业规模',
                                `phone` varchar(100) DEFAULT NULL COMMENT '绑定手机',
                                `password` varchar(200) DEFAULT NULL COMMENT '登录密码',
                                `link_man` varchar(100) DEFAULT NULL COMMENT '联系人',
                                `link_phone` varchar(100) DEFAULT NULL COMMENT '联系电话',
                                `province` varchar(200) DEFAULT NULL COMMENT '省',
                                `city` varchar(200) DEFAULT NULL COMMENT '市',
                                `county` varchar(200) DEFAULT NULL COMMENT '区/县',
                                `town` varchar(200) DEFAULT NULL COMMENT '镇/街道',
                                `province_name` varchar(200) DEFAULT NULL COMMENT '省名称',
                                `city_name` varchar(200) DEFAULT NULL COMMENT '市名称',
                                `county_name` varchar(200) DEFAULT NULL COMMENT '区/县名称',
                                `town_name` varchar(200) DEFAULT NULL COMMENT '镇/街道名称',
                                `address` varchar(128) DEFAULT NULL COMMENT '详细地址',
                                `organ_name` varchar(100) DEFAULT NULL COMMENT '机构名称',
                                `credit_type` int DEFAULT 1 COMMENT '证件类型-1：统一信用代码',
                                `credit_code` varchar(100) DEFAULT NULL COMMENT '统一信用代码',
                                `effective_time` datetime DEFAULT NULL COMMENT '证件有效期',
                                `organ_type` varchar(100) DEFAULT NULL COMMENT '机构类型',
                                `legal_person` varchar(100) DEFAULT NULL COMMENT '法人',
                                `audit_submit_time` datetime DEFAULT NULL COMMENT '认证提交时间',
                                `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
                                `audit_user` bigint(20) DEFAULT NULL COMMENT '审核人',
                                `audit_user_name` varchar(200) DEFAULT NULL COMMENT '审核人',
                                `reason` varchar(200) DEFAULT NULL COMMENT '审核原因',
                                `cer_status` int(11) DEFAULT 0 COMMENT '认证状态',
                                `status` int(11) DEFAULT NULL COMMENT '状态',
                                `source` int(11) DEFAULT 1 COMMENT '来源1小程序',
                                `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业用户信息';



CREATE TABLE `saas_company_park` (
                                     `id` bigint(20) NOT NULL COMMENT 'id主键',
                                     `saas_company_id` bigint(20) DEFAULT NULL COMMENT '企业id',
                                     `park_id` bigint(20) DEFAULT NULL COMMENT '园区id',
                                     `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                     `type` int(11) DEFAULT '1' COMMENT '企业类型1：内部入驻，2：企业入驻，3：个人入驻',
                                     `settle_time` datetime DEFAULT NULL COMMENT '入驻时间',
                                     `settle_address` varchar(200) DEFAULT NULL COMMENT '入驻地址',
                                     `settle_file` varchar(200) DEFAULT NULL COMMENT '登记附件',
                                     `settle_area` varchar(200) DEFAULT NULL COMMENT '入驻面积',
                                     `effective_time_start` datetime DEFAULT NULL COMMENT '入驻有效期开始',
                                     `effective_time_end` datetime DEFAULT NULL COMMENT '入驻有效期结束',
                                     `enable` int(11) DEFAULT '1' COMMENT '启用状态 1：启用，0：禁用',
                                     `status` int(11) DEFAULT NULL COMMENT '状态0:审核中,1:审核通过,2:审核拒绝',
                                     `source` int(11) DEFAULT 1 COMMENT '登记来源 1 入驻申请，2后台创建',
                                     `reason` varchar(200) DEFAULT NULL COMMENT '审核原因',
                                     `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                     `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业-园区入驻信息';

CREATE TABLE `saas_company_park_serve` (
                                           `id` bigint(20) NOT NULL COMMENT 'id主键',
                                           `company_id` bigint(20) DEFAULT NULL COMMENT '企业id',
                                           `park_id` bigint(20) DEFAULT NULL COMMENT '园区id',
                                           `organ_name` varchar(100) DEFAULT NULL COMMENT '企业名称',
                                           `credit_code` varchar(100) DEFAULT NULL COMMENT '统一信用代码',
                                           `organ_type` varchar(100) DEFAULT NULL COMMENT '机构类型',
                                           `legal_person` varchar(100) DEFAULT NULL COMMENT '法人',
                                           `service_model` varchar(200) DEFAULT NULL COMMENT '服务事项',
                                           `service_expire` varchar(200) DEFAULT NULL COMMENT '服务有效期',
                                           `settle_file` varchar(200) DEFAULT NULL COMMENT '登记附件',
                                           `status` int(11) DEFAULT NULL COMMENT '状态0:审核中,1:审核通过,2:审核拒绝',
                                           `reason` varchar(200) DEFAULT NULL COMMENT '审核原因',
                                           `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                           `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='园区服务商登记';

CREATE TABLE `saas_user_company` (
                                     `id` bigint(20) NOT NULL COMMENT 'id主键',
                                     `type` int(11) DEFAULT NULL COMMENT '人员类型1管理员，2普通员工',
                                     `saas_company_id` bigint(20) DEFAULT NULL COMMENT '企业id',
                                     `credit_code` varchar(100) DEFAULT NULL COMMENT '统一信用代码',
                                     `saas_user_id` bigint(20) DEFAULT NULL COMMENT '人员id',
                                     `name` varchar(100) DEFAULT NULL COMMENT '姓名',
                                     `pic`  varchar(200)  DEFAULT NULL COMMENT ' 照片',
                                     `sex` int(11) DEFAULT NULL COMMENT '性别1男，2女',
                                     `contact_phone` varchar(100) DEFAULT NULL COMMENT '电话',
                                     `contact_address` varchar(200) DEFAULT NULL COMMENT '联系地址',
                                     `idcard` varchar(100) DEFAULT NULL COMMENT '证件号码',
                                     `dept_name` varchar(200) DEFAULT NULL COMMENT '所在部门',
                                     `station_name` varchar(200) DEFAULT NULL COMMENT '岗位名称',
                                     `station_level` varchar(200) DEFAULT NULL COMMENT '岗位级别',
                                     `entry_time` datetime DEFAULT NULL COMMENT '入职时间',
                                     `status` int(11) DEFAULT NULL COMMENT '状态0:审核中,1:审核通过,2:审核拒绝',
                                     `reason` varchar(200) DEFAULT NULL COMMENT '审核原因',
                                     `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                     `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员-企业登记信息';





ALTER TABLE `pmg_bat_park_invoice_order` ADD `person_phone` varchar(20) DEFAULT NULL COMMENT '开票人电话';

ALTER TABLE `pmg_bat_park_invoice_order` ADD `message` varchar(256) DEFAULT NULL COMMENT '三方接口开票返回消息';

ALTER TABLE     `vehicle_monthly_card_data`   ADD  `apply_source` int(11) DEFAULT '0' COMMENT '申请来源0系统1用户（旧数据需要进行数据改动，否则默认系统）';

ALTER TABLE     `vehicle_monthly_card_data`   ADD  `review_user_info_id` bigint(20) DEFAULT NULL COMMENT '审核用户信息id,来自表nest_user_info';

ALTER TABLE     `vehicle_monthly_card_data`   ADD  `review_user_id` bigint(20) DEFAULT NULL COMMENT '审核用户账号id,来自表nest_user';

ALTER TABLE     `staggered_monthly_card_data`   ADD  `review_user_info_id` bigint(20) DEFAULT NULL COMMENT '审核用户信息id,来自表nest_user_info';

ALTER TABLE     `staggered_monthly_card_data`   ADD  `review_user_id` bigint(20) DEFAULT NULL COMMENT '审核用户账号id,来自表nest_user';


ALTER TABLE `replace_plate_no_record` ADD `is_owner` int(2) DEFAULT  '0' COMMENT '是否车主本人0否1是';
ALTER TABLE `replace_plate_no_record` ADD `new_person_name` varchar(20) DEFAULT NULL COMMENT '新车主姓名';
ALTER TABLE `replace_plate_no_record` ADD `new_person_phone` varchar(20) DEFAULT NULL COMMENT '新手机号';
ALTER TABLE `replace_plate_no_record` ADD `new_vehicle_type` int(2) DEFAULT  '0' COMMENT '新车辆类型0：其他车、1：小型车、2：大型车、3：摩托车';
ALTER TABLE `replace_plate_no_record` ADD `new_energy_type` int(2) DEFAULT  '0' COMMENT '新能源类型0：其他类型、1：汽油车、2：新能源';
ALTER TABLE `replace_plate_no_record` ADD `old_file` varchar(1024) DEFAULT NULL COMMENT '旧申请附件图片地址 逗号隔开';
ALTER TABLE `replace_plate_no_record` ADD `new_file` varchar(1024) DEFAULT NULL COMMENT '新申请附件图片地址 逗号隔开';
ALTER TABLE `replace_plate_no_record` ADD `ru_approval_data_id` varchar(64) DEFAULT NULL COMMENT '任务对应的业务数据表id';

ALTER TABLE `replace_plate_no_record` ADD  `rule_id` bigint(20) DEFAULT NULL COMMENT '包期规则ID';

ALTER TABLE `replace_plate_no_record` ADD  `review_action` int(2) DEFAULT '0' COMMENT '审核动作0拒绝1通过';

ALTER TABLE `replace_plate_no_record` ADD `review_user_id` bigint(20) DEFAULT NULL COMMENT '审核用户账号id,来自表nest_user';


/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : smart_park_torch

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 04/01/2023 15:52:12
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_approval
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_approval`;
CREATE TABLE `pmg_bat_park_act_approval`  (
                                              `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键id',
                                              `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批流程名称',
                                              `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                              `company_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '企业id',
                                              `act_type` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标识简写',
                                              `describe_` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程描述',
                                              `park_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '园区ID',
                                              `tenant_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户ID',
                                              `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                              `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                              `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                              `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改用户id',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              INDEX `companyId`(`company_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '审批表，记录审批名字' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : smart_park_torch

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 04/01/2023 15:52:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_form_data
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_form_data`;
CREATE TABLE `pmg_bat_park_act_form_data`  (
                                               `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `approval_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '审批id',
                                               `form_data` json NOT NULL COMMENT '表单数据',
                                               `format` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据格式',
                                               `create_user` bigint(20) NULL DEFAULT NULL,
                                               `update_user` bigint(20) NULL DEFAULT NULL,
                                               `create_time` datetime(0) NULL DEFAULT NULL,
                                               `update_time` datetime(0) NULL DEFAULT NULL,
                                               `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                               PRIMARY KEY (`approval_id`, `id`) USING BTREE,
                                               INDEX `approvalId`(`approval_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '审批表动态表单单表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : smart_park_torch

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 04/01/2023 15:52:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_hi_comment
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_hi_comment`;
CREATE TABLE `pmg_bat_park_act_hi_comment`  (
                                                `ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                                `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                                `TIME_` datetime(0) NULL DEFAULT NULL,
                                                `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                                `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '主任务id',
                                                `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '运行审批id(节点id)',
                                                `ACTION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                                `MESSAGE_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                                `FULL_MSG_` longblob NULL,
                                                `HI_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '历史任务id',
                                                `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                                `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                                `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
                                                `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                `create_time` datetime(0) NULL DEFAULT NULL,
                                                `user_info_id` bigint(20) NULL DEFAULT NULL COMMENT '用户信息id,来自表nest_user_info',
                                                PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '历史意见表' ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : smart_park_torch

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 04/01/2023 15:52:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_hi_task
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_hi_task`;
CREATE TABLE `pmg_bat_park_act_hi_task`  (
                                             `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                             `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程id',
                                             `ru_approval_data_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                             `ru_process_node_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点id',
                                             `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                             `user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '流程发起人',
                                             `approver_no` int(11) NOT NULL COMMENT '第几个审批节点',
                                             `state` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '流程状态',
                                             `create_time` datetime(0) NULL DEFAULT NULL,
                                             `history_time` datetime(0) NULL DEFAULT NULL,
                                             `audit_type_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批类型名称',
                                             `audit_type` tinyint(11) NULL DEFAULT NULL COMMENT '审批类型，保留字段（或签，会签）',
                                             `task_type` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '任务类型（0，是提交申请 1是审批环节）',
                                             `user_detail` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                             `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                             `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
                                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                             `tenant_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户ID',
                                             `user_info_id` bigint(20) NULL DEFAULT NULL COMMENT '用户信息id,来自表nest_user_info',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : smart_park_torch

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 04/01/2023 15:52:41
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_office_process
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_office_process`;
CREATE TABLE `pmg_bat_park_act_office_process`  (
                                                    `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                    `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道',
                                                    `office_ids` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司下面部门机构ID',
                                                    `company_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司下面部门机构ID',
                                                    `approval_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程模板id',
                                                    `approval_node` json NOT NULL COMMENT '流程模板id',
                                                    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '数据创建时间',
                                                    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                    `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                                    `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新时间',
                                                    `sort` int(11) NULL DEFAULT 0 COMMENT '优先级',
                                                    `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : smart_park_torch

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 04/01/2023 15:52:47
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_ru_approval_data
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_ru_approval_data`;
CREATE TABLE `pmg_bat_park_act_ru_approval_data`  (
                                                      `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                      `approval_data` json NULL COMMENT '用户在表单上填写的值',
                                                      `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                                      `approval_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对应的审批id',
                                                      `state` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务状态0不通过,1通过,2进行中,3取消',
                                                      `create_time` datetime(0) NULL DEFAULT NULL,
                                                      `update_time` datetime(0) NULL DEFAULT NULL,
                                                      `create_user` bigint(20) NULL DEFAULT NULL,
                                                      `update_user` bigint(20) NULL DEFAULT NULL,
                                                      `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : smart_park_torch

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 04/01/2023 15:53:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_ru_process_node
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_ru_process_node`;
CREATE TABLE `pmg_bat_park_act_ru_process_node`  (
                                                     `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                     `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务id',
                                                     `node_no` int(11) NOT NULL COMMENT '节点序号',
                                                     `approver_ids` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核人集合',
                                                     `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对应流程分支id',
                                                     `pass` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否通过（0否，1通过，2审批当中)',
                                                     `approver_type` tinyint(11) NULL DEFAULT NULL COMMENT '节点类型',
                                                     `value` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                                     `text` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                                     `action_mark` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '动作标识',
                                                     `next_node_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '下一个节点id',
                                                     `create_user` bigint(20) NULL DEFAULT NULL,
                                                     `update_user` bigint(20) NULL DEFAULT NULL,
                                                     `create_time` datetime(0) NULL DEFAULT NULL,
                                                     `update_time` datetime(0) NULL DEFAULT NULL,
                                                     `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                                     `ru_approval_data_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程节点表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : smart_park_torch

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 04/01/2023 15:52:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_ru_process
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_ru_process`;
CREATE TABLE `pmg_bat_park_act_ru_process`  (
                                                `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程id',
                                                `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务id',
                                                `audits_sum` int(12) NOT NULL COMMENT '审批人个数',
                                                `audits` longblob NOT NULL COMMENT '审批流',
                                                `create_time` datetime(0) NOT NULL COMMENT '数据创建时间',
                                                `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                `create_user` bigint(20) NOT NULL COMMENT '创建用户id',
                                                `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新时间',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : smart_park_torch

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 04/01/2023 15:53:11
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_ru_task
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_ru_task`;
CREATE TABLE `pmg_bat_park_act_ru_task`  (
                                             `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
                                             `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程id',
                                             `ru_approval_data_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务对应的业务数据表id',
                                             `ru_process_node_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点id',
                                             `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道',
                                             `user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程处理人id',
                                             `approver_no` int(11) NULL DEFAULT NULL COMMENT '第几个审批节点',
                                             `state` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程状态',
                                             `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                             `history_time` datetime(0) NULL DEFAULT NULL,
                                             `audit_type_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批类型名称',
                                             `audit_type` tinyint(11) NULL DEFAULT NULL COMMENT '审批类型，保留字段（会签，或签）',
                                             `task_type` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '任务类型（0，是提交申请 1是审批环节）',
                                             `user_detail` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                             `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                             `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
                                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                             `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                             `user_info_id` bigint(20) NULL DEFAULT NULL COMMENT '用户信息id,来自表nest_user_info',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


--  2023-01-06 结束




-- 园区人员表增加 服务来源 字段
ALTER TABLE pmg_bat_park_people ADD service_source int(11) DEFAULT NULL COMMENT '服务来源(1:新ISC、2:旧ISC)';
-- ------------------- 消防网管相关表 start -----------------------
-- 设备状态表
CREATE TABLE `equipment_status`  (
                                     `id` bigint(20) NOT NULL COMMENT '主键',
                                     `smart_equipment_id` bigint(20) NOT NULL COMMENT '设备表id',
                                     `product_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编号',
                                     `device_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备名字',
                                     `status` int(11) NULL DEFAULT NULL COMMENT '设备状态:0正常 ,1异常',
                                     `breakdown_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备故障表code',
                                     `picture_analyse_id` bigint(20) NULL DEFAULT NULL COMMENT '设备画像分析表id',
                                     `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
                                     `create_dept` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
                                     `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                     `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                     `is_deleted` int(11) NULL DEFAULT 0 COMMENT '是否已删除',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备状态表' ROW_FORMAT = Dynamic;
-- 设备故障历史表
CREATE TABLE `equipment_breakdown_history`  (
                                                `id` bigint(20) NOT NULL COMMENT '主键',
                                                `equipment_status_id` bigint(20) NOT NULL COMMENT '设备表id',
                                                `breakdown_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '故障分类code',
                                                `breakdown_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '故障分类名称',
                                                `breakdown_value` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '异常记录时数据值',
                                                `create_time` datetime(0) NULL DEFAULT NULL COMMENT '故障上报时间',
                                                `recover_time` datetime(0) NULL DEFAULT NULL COMMENT '故障修复时间',
                                                `status` int(11) NULL DEFAULT NULL COMMENT '状态',
                                                `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
                                                `create_dept` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
                                                `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime(0) NULL DEFAULT NULL COMMENT '故障恢复时间',
                                                `is_deleted` int(11) NULL DEFAULT 0 COMMENT '是否已删除',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备故障历史表' ROW_FORMAT = Dynamic;
-- 设备画像历史中间表
CREATE TABLE `equipment_breakdown_analyse_history`  (
                                                        `id` bigint(20) NOT NULL COMMENT '主键',
                                                        `analyse_id` bigint(20) NOT NULL COMMENT '设备故障画像分析表id',
                                                        `history_id` bigint(20) NOT NULL COMMENT '设备故障历史表表id',
                                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备故障历史&画像分析中间表' ROW_FORMAT = Dynamic;

-- 设备故障规则表
CREATE TABLE `equipment_breakdown_rule`  (
                                             `id` bigint(20) NOT NULL COMMENT '主键',
                                             `breakdown_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备故障分类code',
                                             `rule_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备故障名称',
                                             `product_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编号',
                                             `identify` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'IOT传递过来的值的属性名称',
                                             `value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '判断值',
                                             `standard` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '判断方向:lt / gt /eq',
                                             `des` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
                                             `status` int(11) NULL DEFAULT NULL COMMENT '状态',
                                             `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
                                             `create_dept` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
                                             `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                             `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                             `is_deleted` int(11) NULL DEFAULT 0 COMMENT '是否已删除',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备故障规范表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of equipment_breakdown_rule
-- ----------------------------
INSERT INTO `equipment_breakdown_rule` VALUES (1582289680612421634, 'battery_fault', '电压过低', '8HGCWCy49NlzRXLG', 'voltage', '3.5', 'lt', '水表电压过低故障', 1, NULL, NULL, '2022-10-18 16:37:12', NULL, '2022-10-18 16:37:12', 0);
INSERT INTO `equipment_breakdown_rule` VALUES (1582981965612195842, 'off-line_fault', '设备离线', '', 'isOnline', '0', 'eq', '设备离线故障', 1, NULL, NULL, '2022-10-20 14:28:06', NULL, '2022-10-20 14:28:06', 0);
INSERT INTO `equipment_breakdown_rule` VALUES (1582981965612195843, 'device_overload_fault', '温度过高', '', 'temperature', '80', 'gt', 'TBOX终端温度过高', 1, NULL, NULL, '2022-10-20 14:28:06', NULL, '2022-10-20 14:28:06', 0);
INSERT INTO `equipment_breakdown_rule` VALUES (1582981965612195844, 'device_overload_fault', 'CPU使用率过高', '', 'cpu_usage', '99', 'gt', 'TBOX终端CPU使用率过高', 1, NULL, NULL, '2022-10-20 14:28:06', NULL, '2022-10-20 14:28:06', 0);
INSERT INTO `equipment_breakdown_rule` VALUES (1582981965612195845, 'battery_fault', '电量等级低', '', 'electricQuantity', '15', 'lt', '阀门控制器,电量等级低', 1, NULL, NULL, '2022-10-20 14:28:06', NULL, '2022-10-20 14:28:06', 0);
INSERT INTO `equipment_breakdown_rule` VALUES (1582981965612195846, 'device_overload_fault', '内存使用率过高', '', 'memory_usage', '95', 'gt', '内存使用率过高', 1, NULL, NULL, '2022-10-20 14:28:06', NULL, '2022-10-20 14:28:06', 0);
INSERT INTO `equipment_breakdown_rule` VALUES (1582981965612195847, 'battery_fault', '电池电压低', '', 'battery_voltage', '0', 'lt', '	\r\n电池电压过低', 1, NULL, NULL, '2022-10-20 14:28:06', NULL, '2022-10-20 14:28:06', 0);
INSERT INTO `equipment_breakdown_rule` VALUES (1582981965612195848, 'weak_signal_fault', '信号弱', '', 'signalStrength', '100', 'lt', '	\r\n信号弱', 1, NULL, NULL, '2022-10-20 14:28:06', NULL, '2022-10-20 14:28:06', 0);
INSERT INTO `equipment_breakdown_rule` VALUES (1582981965612195849, 'data_exception_fault', '水压低', '', 'pressure', '0', 'lt', '	\r\n信号弱', 1, NULL, NULL, '2022-10-20 14:28:06', NULL, '2022-10-20 14:28:06', 0);
INSERT INTO `equipment_breakdown_rule` VALUES (1585572075109281793, 'device_overload_fault', 'trytest', NULL, 'asdasd', '5.0', 'gt', NULL, 1, 1544645590125375489, 1544645782430019585, '2022-10-27 18:00:16', 1544645590125375489, '2022-10-27 18:01:17', 1);
INSERT INTO `equipment_breakdown_rule` VALUES (1585572309235331073, 'device_overload_fault', 'trytest', NULL, 'asdasd', '50.0', 'gt', NULL, 1, 1544645590125375489, 1544645782430019585, '2022-10-27 18:01:12', 1544645590125375489, '2022-10-27 18:01:12', 1);
INSERT INTO `equipment_breakdown_rule` VALUES (1585581355048783874, 'battery_fault', '111', '212', 'temperature', '121.0', 'lt', NULL, 1, 1544645590125375489, 1544645782430019585, '2022-10-27 18:37:08', 1544645590125375489, '2022-10-27 18:37:08', 1);
INSERT INTO `equipment_breakdown_rule` VALUES (1585581810365698049, 'battery_fault', 'temperature', 'temperature', 'test', '1.0', 'lt', NULL, 1, 1544645590125375489, 1544645782430019585, '2022-10-27 18:38:57', 1544645590125375489, '2022-10-27 18:38:57', 1);

-- 设备故障类型表
CREATE TABLE `equipment_breakdown_type`  (
                                             `id` bigint(20) NOT NULL COMMENT '主键',
                                             `picture_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属设备画像的画像code',
                                             `breakdown_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '故障分类名称',
                                             `breakdown_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '故障分类code',
                                             `is_show` int(11) NULL DEFAULT NULL COMMENT '是否显示:0不显示 ,1显示',
                                             `status` int(11) NULL DEFAULT NULL COMMENT '状态',
                                             `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
                                             `create_dept` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
                                             `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                             `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                             `is_deleted` int(11) NULL DEFAULT 0 COMMENT '是否已删除',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备故障分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of equipment_breakdown_type
-- ----------------------------
INSERT INTO `equipment_breakdown_type` VALUES (1582289680612421634, 'low_battery', '电压过低', 'battery_fault', 1, 1, NULL, NULL, NULL, 1544645590125375489, '2022-10-28 09:56:52', 0);
INSERT INTO `equipment_breakdown_type` VALUES (1582917113472552962, 'often_breakdown', '连接故障', 'connect_fault', 1, 1, NULL, NULL, '2022-10-20 10:10:24', 1544645590125375489, '2022-10-28 09:56:52', 0);
INSERT INTO `equipment_breakdown_type` VALUES (1582917409720438786, 'often_off-online', '离线故障', 'off-line_fault', 1, 1, NULL, NULL, '2022-10-20 10:11:34', 1544645590125375489, '2022-10-28 09:56:52', 0);
INSERT INTO `equipment_breakdown_type` VALUES (1582917409720438787, '', '设备过载', 'device_overload_fault', 1, 1, NULL, NULL, '2022-10-20 10:11:34', 1544645590125375489, '2022-10-28 09:56:52', 0);
INSERT INTO `equipment_breakdown_type` VALUES (1582917409720438788, '', '数据异常', 'data_exception_fault', 1, 1, NULL, NULL, '2022-10-20 10:11:34', 1544645590125375489, '2022-10-28 09:56:52', 0);
INSERT INTO `equipment_breakdown_type` VALUES (1582917409720438789, 'often_poor_signal', '信号弱', 'weak_signal_fault', 1, 1, -1, -1, '2022-10-20 10:11:34', 1544645590125375489, '2022-10-28 09:56:52', 0);
INSERT INTO `equipment_breakdown_type` VALUES (1585542635302146049, '', 'test', 'test_code', 1, 1, 1544645590125375489, 1544645782430019585, '2022-10-27 16:03:17', 1544645590125375489, '2022-10-28 09:56:52', 1);
INSERT INTO `equipment_breakdown_type` VALUES (1585542661755621378, 'low_battery', 'test', 'low_test_fault', 1, 1, 1544645590125375489, 1544645782430019585, '2022-10-27 16:03:23', 1544645590125375489, '2022-10-27 16:40:27', 1);

-- 设备画像分析表
CREATE TABLE `equipment_picture_analyse`  (
                                              `id` bigint(20) NOT NULL COMMENT '主键',
                                              `equipment_status_id` bigint(20) NOT NULL COMMENT '设备状态表id',
                                              `park_id` bigint(20) NULL DEFAULT NULL COMMENT '园区id',
                                              `picture_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备画像code',
                                              `picture_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备画像名称',
                                              `picture_number` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统计次数',
                                              `update_time` datetime(0) NULL DEFAULT NULL COMMENT '记录时间',
                                              `status` int(11) NULL DEFAULT NULL COMMENT '状态',
                                              `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
                                              `create_dept` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
                                              `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                              `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
                                              `is_deleted` int(11) NULL DEFAULT 0 COMMENT '是否已删除',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备故障画像分析表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of equipment_picture_analyse
-- ----------------------------
INSERT INTO `equipment_picture_analyse` VALUES (1584788124233048066, 1584787287993688065, 1457553705928477026, 'often_poor_signal', '频繁信号弱', '17', '2022-10-25 14:05:09', 1, NULL, NULL, '2022-10-25 14:05:07', NULL, 0);
INSERT INTO `equipment_picture_analyse` VALUES (1584788421361737729, 1584788401996636162, 1457553705928477026, 'often_off-online', '频繁离线', '8', '2022-10-25 14:06:20', 1, NULL, NULL, '2022-10-25 14:06:18', NULL, 0);
INSERT INTO `equipment_picture_analyse` VALUES (1584789656957599746, 1584789566276747265, 1457553705928477026, 'low_battery', '低电量', '2', '2022-10-25 14:11:19', 1, NULL, NULL, '2022-10-25 14:11:13', NULL, 0);
INSERT INTO `equipment_picture_analyse` VALUES (1585204545538711554, 1585204529017348098, NULL, 'often_off-online', '频繁离线', '10', '2022-10-26 17:39:55', 1, NULL, NULL, '2022-10-26 17:39:50', NULL, 0);
INSERT INTO `equipment_picture_analyse` VALUES (1585204595127967745, 1585204578518523906, NULL, 'often_off-online', '频繁离线', '8', '2022-10-26 17:40:04', 1, NULL, NULL, '2022-10-26 17:40:02', NULL, 0);

-- 设备画像分析规则表
CREATE TABLE `equipment_picture_rule`  (
                                           `id` bigint(20) NOT NULL COMMENT '主键',
                                           `picture_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备画像名称',
                                           `picture_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备画像code',
                                           `picture_standard` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '判断标准: lt / gt /eq',
                                           `cycle_time` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统计周期,单位:天,范围1-60',
                                           `picture_number` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统计次数',
                                           `picture_value` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统计数值',
                                           `is_show` int(11) NULL DEFAULT NULL COMMENT '是否显示:0不显示 ,1显示',
                                           `status` int(11) NULL DEFAULT NULL COMMENT '状态',
                                           `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
                                           `create_dept` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
                                           `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                           `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                           `is_deleted` int(11) NULL DEFAULT 0 COMMENT '是否已删除',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备故障画像表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of equipment_picture_rule
-- ----------------------------
INSERT INTO `equipment_picture_rule` VALUES (1582666876032090113, '频繁故障', 'often_breakdown', 'gt', '30', '5', '0', 1, 1, NULL, NULL, '2022-10-19 17:36:02', 1544645590125375489, '2022-10-31 13:47:42', 0);
INSERT INTO `equipment_picture_rule` VALUES (1582667452526592002, '频繁离线', 'often_off-online', 'gt', '30', '5', '0', 1, 1, NULL, NULL, '2022-10-19 17:38:20', 1544645590125375489, '2022-10-31 10:10:40', 0);
INSERT INTO `equipment_picture_rule` VALUES (1582667982418182145, '低电量', 'low_battery', 'lt', '0', '0', '10', 1, 1, NULL, NULL, '2022-10-19 17:40:26', 1544645590125375489, '2022-10-28 09:56:49', 0);
INSERT INTO `equipment_picture_rule` VALUES (1582668405233385474, '频繁信号弱', 'often_poor_signal', 'gt', '30', '5', '0', 1, 1, NULL, NULL, '2022-10-19 17:42:07', 1544645590125375489, '2022-10-29 11:31:35', 0);

-- ------------------- 消防网管相关表 end -----------------------

-- ------------------- 审批流数据 -----------------------

ALTER TABLE `staggered_monthly_card_data` ADD `free_state` int(11) DEFAULT '1' COMMENT '免费状态 0 免费 1 不免费(冗余规则数据避免规则被修改)';

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_approval
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_approval`;
CREATE TABLE `pmg_bat_park_act_approval`  (
                                              `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键id',
                                              `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批流程名称',
                                              `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                              `company_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '企业id',
                                              `act_type` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标识简写',
                                              `describe_` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程描述',
                                              `park_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '园区ID',
                                              `tenant_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户ID',
                                              `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                              `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                              `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                              `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改用户id',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              INDEX `companyId`(`company_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '审批表，记录审批名字' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pmg_bat_park_act_approval
-- ----------------------------
INSERT INTO `pmg_bat_park_act_approval` VALUES ('1613168383275216897', '车牌变更交流程', NULL, NULL, 'cpbg', '车牌变更交流程', '1457553705928477026', '108994', NULL, NULL, NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;


SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_form_data
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_form_data`;
CREATE TABLE `pmg_bat_park_act_form_data`  (
                                               `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `approval_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '审批id',
                                               `form_data` json NOT NULL COMMENT '表单数据',
                                               `format` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据格式',
                                               `create_user` bigint(20) NULL DEFAULT NULL,
                                               `update_user` bigint(20) NULL DEFAULT NULL,
                                               `create_time` datetime(0) NULL DEFAULT NULL,
                                               `update_time` datetime(0) NULL DEFAULT NULL,
                                               `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                               PRIMARY KEY (`approval_id`, `id`) USING BTREE,
                                               INDEX `approvalId`(`approval_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '审批表动态表单单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pmg_bat_park_act_form_data
-- ----------------------------
INSERT INTO `pmg_bat_park_act_form_data` VALUES ('1613168383455571970', '1613168383275216897', '{\"ss\": \"bb\"}', NULL, 1111111111, NULL, '2023-01-11 21:38:08', NULL, '108994');

SET FOREIGN_KEY_CHECKS = 1;


SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_hi_comment
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_hi_comment`;
CREATE TABLE `pmg_bat_park_act_hi_comment`  (
                                                `ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                                `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                                `TIME_` datetime(0) NULL DEFAULT NULL,
                                                `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                                `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '主任务id',
                                                `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '运行审批id(节点id)',
                                                `ACTION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                                `MESSAGE_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                                `FULL_MSG_` longblob NULL,
                                                `HI_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '历史任务id',
                                                `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                                `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                                `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
                                                `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                `create_time` datetime(0) NULL DEFAULT NULL,
                                                `user_info_id` bigint(20) NULL DEFAULT NULL COMMENT '用户信息id,来自表nest_user_info',
                                                PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '历史意见表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of pmg_bat_park_act_hi_comment
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_hi_task
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_hi_task`;
CREATE TABLE `pmg_bat_park_act_hi_task`  (
                                             `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                             `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程id',
                                             `ru_approval_data_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                             `ru_process_node_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点id',
                                             `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                             `user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '流程发起人',
                                             `approver_no` int(11) NOT NULL COMMENT '第几个审批节点(取消的话记录为999)',
                                             `state` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '流程状态',
                                             `create_time` datetime(0) NULL DEFAULT NULL,
                                             `history_time` datetime(0) NULL DEFAULT NULL,
                                             `audit_type_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批类型名称',
                                             `audit_type` tinyint(11) NULL DEFAULT NULL COMMENT '审批类型，保留字段（或签，会签）',
                                             `task_type` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '任务类型（0，是提交申请 1是审批环节）',
                                             `user_detail` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                             `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                             `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
                                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                             `tenant_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户ID',
                                             `user_info_id` bigint(20) NULL DEFAULT NULL COMMENT '用户信息id,来自表nest_user_info',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pmg_bat_park_act_hi_task
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_office_process
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_office_process`;
CREATE TABLE `pmg_bat_park_act_office_process`  (
                                                    `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                    `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道',
                                                    `office_ids` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司下面部门机构ID',
                                                    `company_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司下面部门机构ID',
                                                    `approval_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程模板id',
                                                    `approval_node` json NOT NULL COMMENT '流程模板id',
                                                    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '数据创建时间',
                                                    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                    `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                                    `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新时间',
                                                    `sort` int(11) NULL DEFAULT 0 COMMENT '优先级',
                                                    `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pmg_bat_park_act_office_process
-- ----------------------------
INSERT INTO `pmg_bat_park_act_office_process` VALUES ('90072b9dbd854d82807798509276465e', NULL, '11111,22222', '1', '1613168383275216897', '[{\"text\": \"工单审核\", \"nodeNo\": 1, \"approverList\": [{\"id\": \"1554924613714128898\", \"name\": \"审批管理员\"}], \"approverType\": 3}]', '2023-01-11 21:38:09', '2023-01-11 21:38:09', 1111111111, 1111111111, 0, '108994');
INSERT INTO `pmg_bat_park_act_office_process` VALUES ('baff223de4164f48abb81c0d20af5352', NULL, '11111,22222', '2', '1613168383275216897', '[{\"text\": \"工单审核\", \"nodeNo\": 1, \"approverList\": [{\"id\": \"1554924613714128898\", \"name\": \"审批管理员\"}], \"approverType\": 3}]', '2023-01-11 21:38:09', '2023-01-11 21:38:09', 1111111111, 1111111111, 0, '108994');

SET FOREIGN_KEY_CHECKS = 1;


SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_ru_approval_data
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_ru_approval_data`;
CREATE TABLE `pmg_bat_park_act_ru_approval_data`  (
                                                      `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                      `approval_data` json NULL COMMENT '用户在表单上填写的值',
                                                      `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                                      `approval_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对应的审批id',
                                                      `state` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务状态0不通过,1通过,2进行中,3取消',
                                                      `create_time` datetime(0) NULL DEFAULT NULL,
                                                      `update_time` datetime(0) NULL DEFAULT NULL,
                                                      `create_user` bigint(20) NULL DEFAULT NULL,
                                                      `update_user` bigint(20) NULL DEFAULT NULL,
                                                      `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pmg_bat_park_act_ru_approval_data
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_ru_process
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_ru_process`;
CREATE TABLE `pmg_bat_park_act_ru_process`  (
                                                `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程id',
                                                `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务id',
                                                `audits_sum` int(12) NOT NULL COMMENT '审批人个数',
                                                `audits` longblob NOT NULL COMMENT '审批流',
                                                `create_time` datetime(0) NOT NULL COMMENT '数据创建时间',
                                                `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                `create_user` bigint(20) NOT NULL COMMENT '创建用户id',
                                                `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新时间',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pmg_bat_park_act_ru_process
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_ru_process_node
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_ru_process_node`;
CREATE TABLE `pmg_bat_park_act_ru_process_node`  (
                                                     `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                     `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务id',
                                                     `node_no` int(11) NOT NULL COMMENT '节点序号',
                                                     `approver_ids` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核人集合',
                                                     `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对应流程分支id',
                                                     `pass` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否通过（0否，1通过，2审批当中)',
                                                     `approver_type` tinyint(11) NULL DEFAULT NULL COMMENT '节点类型',
                                                     `value` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                                     `text` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                                     `action_mark` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '动作标识',
                                                     `next_node_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '下一个节点id',
                                                     `create_user` bigint(20) NULL DEFAULT NULL,
                                                     `update_user` bigint(20) NULL DEFAULT NULL,
                                                     `create_time` datetime(0) NULL DEFAULT NULL,
                                                     `update_time` datetime(0) NULL DEFAULT NULL,
                                                     `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                                     `ru_approval_data_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程节点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pmg_bat_park_act_ru_process_node
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmg_bat_park_act_ru_task
-- ----------------------------
DROP TABLE IF EXISTS `pmg_bat_park_act_ru_task`;
CREATE TABLE `pmg_bat_park_act_ru_task`  (
                                             `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
                                             `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程id',
                                             `ru_approval_data_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务对应的业务数据表id',
                                             `ru_process_node_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点id',
                                             `source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道',
                                             `user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程处理人id',
                                             `approver_no` int(11) NULL DEFAULT NULL COMMENT '第几个审批节点',
                                             `state` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程状态',
                                             `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                             `history_time` datetime(0) NULL DEFAULT NULL,
                                             `audit_type_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批类型名称',
                                             `audit_type` tinyint(11) NULL DEFAULT NULL COMMENT '审批类型，保留字段（会签，或签）',
                                             `task_type` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '任务类型（0，是提交申请 1是审批环节）',
                                             `user_detail` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                             `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
                                             `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
                                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                             `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                             `user_info_id` bigint(20) NULL DEFAULT NULL COMMENT '用户信息id,来自表nest_user_info',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pmg_bat_park_act_ru_task
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;









-- 人脸抓拍事件数据表
CREATE TABLE `pmg_bat_face_capture_event_data` (
                                                   `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                   `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                                   `pic_url` varchar(255) NOT NULL COMMENT '抓拍图片url',
                                                   `face_id` varchar(255) NOT NULL COMMENT '人脸id',
                                                   `match_url` varchar(255) NOT NULL COMMENT '抓拍图片url',
                                                   `f_similarity` varchar(10) DEFAULT NULL COMMENT '相似度',

                                                   `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                                   `status` int(2) DEFAULT NULL COMMENT '状态',
                                                   `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                                   `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                                   PRIMARY KEY (`id`),
                                                   KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人脸抓拍事件数据表';

-- 用户车辆数据表
CREATE TABLE `user_vehicle_data` (
                                     `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',

                                     `pay_user_id` varchar(64) DEFAULT NULL COMMENT '移动端用户ID',
                                     `plate_no` varchar(20) DEFAULT NULL COMMENT '车牌号',


                                     `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                     `status` int(2) DEFAULT NULL COMMENT '状态',
                                     `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                     `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户车辆数据表';

-- 停车场 车辆月卡数据表
CREATE TABLE `vehicle_monthly_card_data` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                             `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                             `parking_id` bigint(20) DEFAULT NULL COMMENT '停车场ID',
                                             `car_id` bigint(20) DEFAULT NULL COMMENT '车辆ID',
                                             `plate_no` varchar(20) DEFAULT NULL COMMENT '车牌号',
                                             `rule_id` bigint(20) DEFAULT NULL COMMENT '车辆ID',
                                             `begin_time` datetime DEFAULT NULL COMMENT '包租 开始时间 ',
                                             `end_time` datetime DEFAULT NULL COMMENT '包租 结束时间',
                                             `remark` varchar(500) DEFAULT NULL COMMENT '备注',

                                             `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                             `status` int(11) DEFAULT NULL COMMENT '状态',
                                             `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                             `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                             PRIMARY KEY (`id`) USING BTREE,
                                             KEY `idx_id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='停车场-车辆月卡数据表';

-- 停车场 车辆月卡支付表
CREATE TABLE `vehicle_monthly_card_bill_data` (
                                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                  `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                                  `vehicle_monthly_card_id` bigint(20) DEFAULT NULL COMMENT '月卡ID',
                                                  `car_id` bigint(20) DEFAULT NULL COMMENT '车辆ID',
                                                  `pay_user_id` varchar(64) DEFAULT NULL COMMENT '移动端用户ID',
                                                  `month_number` int(11) DEFAULT null COMMENT '包月数量',
                                                  `total_amount` bigint(20) DEFAULT NULL COMMENT '总金额 分',
                                                  `pay_type` int(11) DEFAULT NULL COMMENT '支付类型 | 0 后台充值 1 微信充值',
                                                  `pay_status` int(11) DEFAULT NULL COMMENT '支付状态 | 0 支付中 1 支付成功',
                                                  `invoicing_status` int(11) DEFAULT null COMMENT '开票状态 | 0 开票中 1 开票成功 2 开票失败 3 红冲 4 被红冲 9 未开票',
                                                  `begin_time` datetime DEFAULT NULL COMMENT '包租 开始时间 ',
                                                  `end_time` datetime DEFAULT NULL COMMENT '包租 结束时间',

                                                  `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                                  `status` int(11) DEFAULT NULL COMMENT '状态',
                                                  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                                  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                  `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                                  PRIMARY KEY (`id`) USING BTREE,
                                                  KEY `idx_id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='停车场-车辆月卡账单表';

-- 瑞宏-园区-发票配置表
CREATE TABLE `park_invoice` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                `park_id` bigint(64) NOT NULL COMMENT '园区ID',

                                `app_code` varchar(64) DEFAULT NULL COMMENT '由电子发票平台分配的 appCode',
                                `key_store_path` varchar(255) DEFAULT NULL COMMENT '密钥库文件路径',
                                `alias` varchar(64) DEFAULT NULL COMMENT '密钥库别名',
                                `password` varchar(64) DEFAULT NULL COMMENT '密钥库密码',
                                `api_url` varchar(255) DEFAULT NULL COMMENT '请求的api地址',
                                `test_Taxpayer_code` varchar(255) DEFAULT NULL COMMENT '销售方纳税人识别号',

                                `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                `status` int(11) DEFAULT NULL COMMENT '状态',
                                `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                PRIMARY KEY (`id`) USING BTREE,
                                KEY `idx_id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='瑞宏-园区-发票配置表';

-- 给厦门银行支付配置表增加 appid 字段
ALTER TABLE pmg_bat_park_xc_pay ADD app_id varchar(64) DEFAULT NULL COMMENT '小程序APPID';

-- 给发票主表增加 园区ID 字段
ALTER TABLE pmg_bat_park_invoice_order ADD park_id bigint(64) NOT NULL COMMENT '园区ID';

-- 2023年3月11日 更新已执行
-- 停车位信息表
CREATE TABLE `parking_place` (
         `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
         `park_id` bigint(64) NOT NULL COMMENT '园区ID',

         `parking_id` bigint(64) DEFAULT NULL COMMENT '停车场ID',
         `place_code` varchar(12) DEFAULT NULL COMMENT '车位code',
         `place_no` varchar(12) DEFAULT NULL COMMENT '车位号',
         `place_type` int(2) DEFAULT null COMMENT '车位类型（1：无编号车位、2：固定编号车位）',
         `place_position` varchar(60) DEFAULT NULL COMMENT '车位位置',
         `marker_state` int(2) DEFAULT 0 COMMENT '标记状态（0：未标记、1：已标记）',
         `allocated_state` int(2) DEFAULT 0 COMMENT '分配状态（0：未分配、1：已分配）',
         `place_lock` varchar(12) DEFAULT NULL COMMENT '车位锁',
         `place_camera` varchar(12) DEFAULT NULL COMMENT '车位摄像头',
         `enable_state` int(2) DEFAULT 1 COMMENT '启用状态（0：未启用、1：已启用）',
         `remark` varchar(256) DEFAULT null COMMENT '备注信息',

         `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
         `status` int(2) DEFAULT NULL COMMENT '状态',
         `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
         `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
         `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
         `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
         PRIMARY KEY (`id`),
         KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='停车位信息表';


-- 停车位绑定信息表
CREATE TABLE `parking_place_binding` (
         `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
         `park_id` bigint(64) NOT NULL COMMENT '园区ID',

         `parking_id` bigint(64) DEFAULT NULL COMMENT '停车场ID',
         `place_id` bigint(64) DEFAULT NULL COMMENT '车位ID',
         `car_id` bigint(64) DEFAULT NULL COMMENT '车辆信息ID',
         `remark` varchar(256) DEFAULT null COMMENT '备注信息',

         `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
         `status` int(2) DEFAULT NULL COMMENT '状态',
         `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
         `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
         `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
         `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
         PRIMARY KEY (`id`),
         KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='停车位绑定信息表';

-- 2023年2月15日15:19:49
ALTER TABLE `pmg_bat_charge_rule` ADD `is_const_space_no` int(11) DEFAULT '0' COMMENT '是否固定车位号:0否,1是';

ALTER TABLE `pmg_bat_charge_rule` ADD `validity_period` int(11) DEFAULT '7' COMMENT '缴费有效期(天)';

ALTER TABLE `staggered_month_charge_rule` ADD `is_const_space_no` int(11) DEFAULT '0' COMMENT '是否固定车位号:0否,1是';

ALTER TABLE `staggered_month_charge_rule` ADD `validity_period` int(11) DEFAULT '7' COMMENT '缴费有效期(天)';

ALTER TABLE `staggered_month_charge_rule` ADD `residue_card` int(11) DEFAULT '80' COMMENT '错时卡总数';


ALTER TABLE `staggered_monthly_card_data` ADD `apply_source` int(11) DEFAULT '0' COMMENT '创建方式：0后台添加、1用户申请、2后台导入';
ALTER TABLE `staggered_monthly_card_data` ADD `system_deleted` int(11) DEFAULT '0' COMMENT '后台删除(0否1是)';
ALTER TABLE `staggered_monthly_card_data` ADD `user_deleted` int(11) DEFAULT '0' COMMENT '用户删除(0否1是)';
ALTER TABLE `staggered_monthly_card_data` ADD `place_no` varchar(32) DEFAULT NULL COMMENT '为车辆分配的车位号';
ALTER TABLE `staggered_monthly_card_data` ADD `ru_approval_data_id` varchar(64) DEFAULT NULL COMMENT '任务对应的业务数据表id';
ALTER TABLE `staggered_monthly_card_data` ADD `company_id` bigint(20) DEFAULT NULL COMMENT '所属企业Id';
ALTER TABLE `staggered_monthly_card_data` ADD`is_const_space_no` int(11) DEFAULT '0' COMMENT '是否固定车位号:0否,1是';

ALTER TABLE `pmg_bat_park_act_hi_task` MODIFY `user` varchar(64) DEFAULT NULl COMMENT '流程发起人';

ALTER TABLE `pmg_bat_park_act_hi_comment` ADD  `from_data` json DEFAULT NULL COMMENT '除message外的表单数据';

ALTER TABLE `vehicle_monthly_card_data` ADD `ru_approval_data_id` varchar(64) DEFAULT NULL COMMENT '任务对应的业务数据表id';
ALTER TABLE `vehicle_monthly_card_data` ADD `is_const_space_no` int(11) DEFAULT '0' COMMENT '是否固定车位号:0否,1是';

ALTER TABLE `vehicle_monthly_card_data`  ADD `create_phone` varchar(20) DEFAULT NULL COMMENT 'c端用户手机号';
ALTER TABLE `staggered_monthly_card_data`  ADD `create_phone` varchar(20) DEFAULT NULL COMMENT 'c端用户手机号';

ALTER TABLE `replace_plate_no_record`  ADD `remark` varchar(500) DEFAULT NULL COMMENT '备注';

CREATE TABLE `vehicle_monthly_card_bill_data_jt` (
     `card_bill_data_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `id` bigint(20) NOT NULL COMMENT '月卡账单id',
     `park_id` bigint(64) NOT NULL COMMENT '园区ID',
     `vehicle_monthly_card_id` bigint(20) DEFAULT NULL COMMENT '月卡ID',
     `car_id` bigint(20) DEFAULT NULL COMMENT '车辆ID',
     `pay_user_id` varchar(64) DEFAULT NULL COMMENT '移动端用户ID',
     `month_number` int(11) DEFAULT NULL COMMENT '包月数量',
     `total_amount` bigint(20) DEFAULT NULL COMMENT '总金额 分',
     `pay_type` int(11) DEFAULT NULL COMMENT '支付类型 | 0 后台充值 1 微信充值',
     `pay_status` int(11) DEFAULT NULL COMMENT '支付状态 | 0 支付中 1 支付成功',
     `invoicing_status` int(11) DEFAULT NULL COMMENT '开票状态 | 0 开票中 1 开票成功 2 开票失败 3 红冲 4 被红冲 9 未开票',
     `begin_time` datetime DEFAULT NULL COMMENT '包租 开始时间 ',
     `end_time` datetime DEFAULT NULL COMMENT '包租 结束时间',
     `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
     `status` int(11) DEFAULT NULL COMMENT '状态',
     `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
     `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
     `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
     `parking_id` bigint(20) DEFAULT NULL COMMENT '停车场ID',
     `time` datetime DEFAULT NULL COMMENT '集团单据创建时间',
     PRIMARY KEY (`card_bill_data_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='停车场-车辆月卡账单表jt';

CREATE TABLE `vehicle_monthly_card_data_jt` (
    `card_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `id` bigint(20) NOT NULL COMMENT '月卡id',
    `park_id` bigint(64) NOT NULL COMMENT '园区ID',
    `pay_user_id` varchar(64) DEFAULT NULL COMMENT '移动端用户ID',
    `parking_id` bigint(20) DEFAULT NULL COMMENT '停车场ID',
    `plate_no` varchar(20) DEFAULT NULL COMMENT '车牌号',
    `person_name` varchar(20) DEFAULT NULL COMMENT '车主姓名',
    `person_phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `vehicle_type` int(11) DEFAULT '0' COMMENT '车辆类型（0：其他车、1：小型车、2：大型车、3：摩托车）',
    `energy_type` int(11) DEFAULT '0' COMMENT '能源类型（0：其他类型、1：汽油车、2：新能源）',
    `reason` varchar(255) DEFAULT NULL COMMENT '进入理由',
    `file` varchar(1024) DEFAULT NULL COMMENT '申请附件图片地址 逗号隔开',
    `review_state` int(11) DEFAULT '0' COMMENT '审核状态（0 待审核 1 审核通过 2 拒绝 3 进行中）',
    `active_state` int(11) DEFAULT '0' COMMENT '月卡状态（0 未缴费 1 正常使用 2 已过期,3已失效）',
    `review_name` varchar(80) DEFAULT NULL COMMENT '审批人姓名',
    `review_time` datetime DEFAULT NULL COMMENT '审核时间',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `rule_id` bigint(20) DEFAULT NULL COMMENT '包期规则ID',
    `begin_time` datetime DEFAULT NULL COMMENT '包租 开始时间 ',
    `end_time` datetime DEFAULT NULL COMMENT '包租 结束时间',
    `free_state` int(11) DEFAULT '0' COMMENT '免费状态 0 免费 1 不免费',
    `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
    `company_id` bigint(20) DEFAULT NULL COMMENT '企业ID',
    `place_no` varchar(32) DEFAULT NULL COMMENT '为车辆分配的车位号',
    `apply_source` int(11) DEFAULT '0' COMMENT '申请来源0系统1用户（旧数据需要进行数据改动，否则默认系统）',
    `review_user_info_id` bigint(20) DEFAULT NULL COMMENT '审核用户信息id,来自表nest_user_info',
    `review_user_id` bigint(20) DEFAULT NULL COMMENT '审核用户账号id,来自表nest_user',
    `is_const_space_no` int(11) DEFAULT '0' COMMENT '是否固定车位号:0否,1是',
    `ru_approval_data_id` varchar(64) DEFAULT NULL COMMENT '任务对应的业务数据表id',
    `create_phone` varchar(20) DEFAULT NULL COMMENT 'c端用户手机号',
    `place_id` bigint(64) DEFAULT NULL COMMENT '车位ID',
    `time` datetime DEFAULT NULL COMMENT '集团单据创建时间',
    PRIMARY KEY (`card_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1627996750105169924 DEFAULT CHARSET=utf8mb4 COMMENT='停车场-车辆月卡数据表';

-- 修复退款数据
insert into pmg_bat_park_bill_payment (id, plate_no, parking_id, bill_id, suppose_cost , paid_cost, total_cost, invoicing_status, paid_place_idle_cost, tenant_id , create_dept, create_time, update_time, status, is_deleted) values (1611697066273923073, '闽DD25100', 1467771100660068353, 1611695495605948418, 500 , 0, 500, 9, 0, '621172' , 1467777570811437057, TIMESTAMP '2023-01-07 20:11:39.140', TIMESTAMP '2023-01-07 20:11:39.140', 1, 0);
update pmg_bat_park_bill_payment set plate_no = '闽DD25100', parking_id = 1467771100660068353, bill_id = 1611695495605948418, pay_user_id = 'oZAm06HzenFj63eVXgrGogME7gNU', pay_type = 'WECHAT_JSAPI', suppose_cost = 0, paid_cost = 500, total_cost = 500, invoicing_status = 9, deduce_cost = 0, paid_place_idle_cost = 0, tenant_id = '621172', create_dept = 1467777570811437057, create_time = TIMESTAMP '2023-01-07 20:11:39.000', update_time = TIMESTAMP '2023-01-07 20:13:00.157', status = 1 where id = 1611697066273923073 and is_deleted = 0;

insert into pmg_bat_park_bill_payment (id, plate_no, parking_id, bill_id, suppose_cost , paid_cost, total_cost, invoicing_status, paid_place_idle_cost, tenant_id , create_dept, create_time, update_time, status, is_deleted) values (1611422687231512577, '闽DD73L8', 1402628952006193153, 1611422055544164353, 500 , 0, 500, 9, 0, '621172' , 1395310306391838722, TIMESTAMP '2023-01-07 02:01:22.079', TIMESTAMP '2023-01-07 02:01:22.079', 1, 0);
update pmg_bat_park_bill_payment set plate_no = '闽DD73L8', parking_id = 1402628952006193153, bill_id = 1611422055544164353, pay_user_id = 'oZAm06HzenFj63eVXgrGogME7gNU', pay_type = 'WECHAT_JSAPI', suppose_cost = 0, paid_cost = 500, total_cost = 500, invoicing_status = 9, deduce_cost = 0, paid_place_idle_cost = 0, tenant_id = '621172', create_dept = 1395310306391838722, create_time = TIMESTAMP '2023-01-07 02:01:22.000', update_time = TIMESTAMP '2023-01-07 02:01:35.165', status = 1 where id = 1611422687231512577 and is_deleted = 0;

insert into pmg_bat_park_bill_payment (id, plate_no, parking_id, bill_id, suppose_cost , paid_cost, total_cost, invoicing_status, paid_place_idle_cost, tenant_id , create_dept, create_time, update_time, status, is_deleted) values (1611418332606226434, '闽DD73L9', 1402628952006193153, 1611417787118424066, 500 , 0, 500, 9, 0, '621172' , 1395310306391838722, TIMESTAMP '2023-01-07 01:44:03.855', TIMESTAMP '2023-01-07 01:44:03.855', 1, 0);
update pmg_bat_park_bill_payment set plate_no = '闽DD73L9', parking_id = 1402628952006193153, bill_id = 1611417787118424066, pay_user_id = 'oZAm06HzenFj63eVXgrGogME7gNU', pay_type = 'WECHAT_JSAPI', suppose_cost = 0, paid_cost = 500, total_cost = 500, invoicing_status = 9, deduce_cost = 0, paid_place_idle_cost = 0, tenant_id = '621172', create_dept = 1395310306391838722, create_time = TIMESTAMP '2023-01-07 01:44:04.000', update_time = TIMESTAMP '2023-01-07 01:54:43.926', status = 1 where id = 1611418332606226434 and is_deleted = 0;

ALTER TABLE `pmg_bat_park_act_ru_task`  ADD COLUMN `act_type` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标识简写' AFTER `user_info_id`;

ALTER TABLE `pmg_bat_park_act_hi_task`  ADD COLUMN `act_type` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标识简写' AFTER `user_info_id`;

ALTER TABLE `pmg_bat_park_act_office_process`
    ADD COLUMN `park_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '园区ID' AFTER `tenant_id`;

ALTER TABLE `pmg_bat_park_act_form_data`
    ADD COLUMN `park_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '园区ID' AFTER `tenant_id`;

ALTER TABLE `pmg_bat_park_act_ru_approval_data`
    ADD COLUMN `park_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '园区ID' AFTER `tenant_id`;

ALTER TABLE `pmg_bat_park_act_ru_task`
    ADD COLUMN `park_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '园区ID' AFTER `act_type`;

ALTER TABLE `pmg_bat_park_act_hi_comment`
    ADD COLUMN `park_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '园区ID' AFTER `from_data`;

ALTER TABLE `pmg_bat_park_act_hi_task`
    ADD COLUMN `park_id` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '园区ID' AFTER `act_type`;

ALTER TABLE `pmg_bat_charge_rule`
    ADD COLUMN `residue_card` int(11) NULL COMMENT '月卡开放总数' AFTER `validity_period`;

ALTER TABLE `replace_plate_no_record`
    ADD COLUMN `pay_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '移动端用户ID' AFTER `remark`;

ALTER TABLE `pmg_bat_park_act_approval`
    ADD COLUMN `is_deleted` int(11) NULL DEFAULT 0 COMMENT '是否已删除' AFTER `update_user`;

-- 给月卡表添加索引
ALTER table vehicle_monthly_card_data ADD INDEX index_plate_no ( `plate_no` );

-----
ALTER TABLE `smart_park_torch`.`staggered_monthly_card_data`
    ADD COLUMN `place_no` varchar(20) NULL COMMENT '为车辆分配的车位号' AFTER `end_time`;
ALTER TABLE `smart_park_torch`.`pmg_bat_park_car`
    ADD COLUMN `energy_type` int(11) NULL COMMENT '能源类型（0：其他类型、1：汽油车、2：新能源）' AFTER `valid_end_time`;

ALTER TABLE `smart_park_torch`.`replace_plate_no_record`
    ADD COLUMN `card_type` varchar(20) NULL COMMENT '包期类型 1月卡  0错时';
ALTER TABLE `smart_park_torch`.`staggered_monthly_card_data`
    ADD COLUMN `place_no` varchar(32) NULL COMMENT '为车辆分配的车位号' AFTER `free_state`;

INSERT INTO `pmg_bat_park_act_approval` VALUES ('1613168382345216897', '月卡车牌变更流程', NULL, NULL, 'ykcpbg', '月卡车牌变更流程', '1457553705928477026', '108994', NULL, NULL, NULL, NULL);

INSERT INTO `smart_park_torch`.`pmg_bat_park_act_office_process`(`id`, `source`, `office_ids`, `company_id`, `approval_id`, `approval_node`, `sort`, `tenant_id`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('90072b9dbd854d82807794539276465e', NULL, '11111,22222', '1', '1613168382345216897', '[{\"text\": \"月卡车牌变更审核\", \"nodeNo\": 1, \"approverList\": [{\"id\": \"1554924613714128898\", \"name\": \"物业审批管理员\"}], \"approverType\": 3}, {\"text\": \"月卡车牌变更审核\", \"nodeNo\": 2, \"approverList\": [{\"id\": \"1570319760018649090\", \"name\": \"财服审批管理员\"}], \"approverType\": 3}]', 0, '108994', 1111111111, '2023-01-11 21:38:09', 1111111111, '2023-01-11 21:38:09');



INSERT INTO `pmg_bat_park_act_form_data` VALUES ('1613168343455571970', '1613168382345216897', '{\"ss\": \"bb\"}', NULL, 1111111111, NULL, '2023-01-11 21:38:08', NULL, '108994');

ALTER TABLE `smart_park_torch`.`replace_plate_no_record`
    ADD COLUMN `apply_type`  varchar(2) NULL COMMENT '申请方式 1 用户发起 0后台提交' AFTER `card_type`;



==============================================================================

DROP TABLE IF EXISTS `pmg_log_parking_car_jt`;
CREATE TABLE `pmg_log_parking_car_jt`  (
       `id_new` int(20) NOT NULL AUTO_INCREMENT,
       `plate_no` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车牌号码',
       `plate_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车牌类型',
       `plate_color` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车牌颜色',
       `vehicle_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆类型',
       `vehicle_color` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆颜色',
       `code` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆类型编码',
       `vehicle_pic_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆图片路径',
       `come_status` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '进出停车场状态',
       `id` bigint(20) NULL DEFAULT NULL COMMENT '主键',
       `tenant_id` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
       `status` int(11) NULL DEFAULT NULL COMMENT '状态',
       `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
       `create_dept` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
       `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
       `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
       `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
       `is_deleted` int(11) NULL DEFAULT 0 COMMENT '是否已删除',
       `plate_pic_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车牌URL',
       `set_position` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备位置',
       `total_cost` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出场应收金额',
       `out_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出场时间',
       `card_no` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡号',
       `event_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件类型',
       `event_index` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件ID',
       `roadway_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车道名称',
       `park_id` bigint(20) NULL DEFAULT NULL COMMENT '园区ID',
       `product_key` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品key',
       `device_name` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
       `parking_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '停车库编号',
       `roadway_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车道编号',
       `entrance_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出入口编号',
       `entrance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出入口名称',
       `parking_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '停车场名称',
       `pass_msg` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '放行原因',
       `pass_status` int(1) NULL DEFAULT NULL COMMENT '放行状态（0未放行、1已放行）',
       `bill_id` bigint(64) NULL DEFAULT NULL COMMENT '账单ID',
       `vehicle_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '临时车' COMMENT '车辆类别名称',
       `event_time` datetime(0) NULL DEFAULT NULL COMMENT '事件时间',
       PRIMARY KEY (`id_new`) USING BTREE,
       INDEX `index_plate_no`(`plate_no`) USING BTREE,
       INDEX `index_vehicle_pic_url`(`vehicle_pic_url`) USING BTREE,
       INDEX `index_px2`(`event_index`, `product_key`, `device_name`) USING BTREE,
       INDEX `index_px`(`park_id`, `event_type`, `is_deleted`, `tenant_id`) USING BTREE,
       INDEX `index_roadway_id`(`roadway_id`) USING BTREE,
       INDEX `index_event_time_roadway`(`event_time`, `roadway_id`) USING BTREE,
       INDEX `index_parking_id_event_type_is_deleted_event_time`(`parking_id`, `event_type`, `is_deleted`, `event_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42408 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '停车场过车记录表' ROW_FORMAT = Dynamic;


ALTER TABLE `vehicle_monthly_card_data`
ADD COLUMN `place_id` bigint(64) NULL DEFAULT NULL COMMENT '车位ID' AFTER `create_phone`;

ALTER TABLE `staggered_monthly_card_data`
ADD COLUMN `place_id` bigint(64) NULL DEFAULT NULL COMMENT '车位ID' AFTER `create_phone`;

-- 停车场 车辆 群组表
CREATE TABLE `vehicle_group` (
                                 `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                 `parking_id` bigint(64) DEFAULT NULL COMMENT '停车场ID',
                                 `group_name` varchar(64) DEFAULT NULL COMMENT '群组名称',
                                 `remark` varchar(500) DEFAULT NULL COMMENT '备注',

                                 `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                 `status` int(2) DEFAULT NULL COMMENT '状态',
                                 `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                 `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                 `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆群组表';

-- 停车场 车辆 群组绑定表
CREATE TABLE `vehicle_group_binding` (
                                         `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                         `parking_id` bigint(64) DEFAULT NULL COMMENT '停车场ID',
                                         `group_id` bigint(64) DEFAULT NULL COMMENT '分组ID',
                                         `vehicle_id` bigint(64) DEFAULT NULL COMMENT '车辆ID',
                                         `remark` varchar(500) DEFAULT NULL COMMENT '备注',

                                         `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                         `status` int(2) DEFAULT NULL COMMENT '状态',
                                         `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                         `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆群组绑定表';

-- 停车场 车辆 群组放行规则表
CREATE TABLE `vehicle_group_pass_rule` (
                                           `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                           `parking_id` bigint(64) DEFAULT NULL COMMENT '停车场ID',
                                           `group_id` bigint(64) DEFAULT NULL COMMENT '分组ID',
                                           `enter_pass` int(11) DEFAULT NULL COMMENT '入场放行（0：不放行，1：放行）',
                                           `enter_pass_type` int(11) DEFAULT NULL COMMENT '入场放行类型（1：全天候，2：按时段，3：按星期）',
                                           `enter_pass_time` varchar(256) DEFAULT NULL COMMENT '入场放行时间段 （入场放行类型为 2：按时段）',
                                           `enter_pass_week` varchar(256) DEFAULT NULL COMMENT '入场放行星期 （入场放行类型为 3：按星期）',
                                           `place_full` int(11) DEFAULT NULL COMMENT '车位满是否放行（0：不放行，1：放行）',
                                           `enter_led` varchar(256) DEFAULT NULL COMMENT '入场LED显示',
                                           `exit_pass` int(11) DEFAULT NULL COMMENT '出场放行（0：不放行，1：放行）',
                                           `exit_pass_type` int(11) DEFAULT NULL COMMENT '出场放行类型（1：全天候，2：按时段，3：按星期）',
                                           `exit_pass_time` varchar(256) DEFAULT NULL COMMENT '出场放行时间段 （出场放行类型为 2：按时段）',
                                           `exit_pass_week` varchar(256) DEFAULT NULL COMMENT '出场放行星期 （出场放行类型为 3：按星期）',
                                           `exit_led` varchar(256) DEFAULT NULL COMMENT '出场LED显示',

                                           `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                           `status` int(2) DEFAULT NULL COMMENT '状态',
                                           `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                           `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆群组放行规则表';

-- 停车场 AB车位信息表
CREATE TABLE `parking_share_place` (
                                       `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                       `parking_id` bigint(64) DEFAULT NULL COMMENT '停车场ID',
                                       `share_name` varchar(64) DEFAULT NULL COMMENT 'AB车位名称',
                                       `person_name` varchar(12) DEFAULT NULL COMMENT '车主姓名',
                                       `person_phone` varchar(11) DEFAULT NULL COMMENT '车主电话',
                                       `share_type` int(11) DEFAULT 1 COMMENT '共享类型（1：不限时间、2：指定期间）',
                                       `begin_time` datetime DEFAULT NULL COMMENT '共享 开始时间 ',
                                       `end_time` datetime DEFAULT NULL COMMENT '共享 结束时间',
                                       `share_status` int(11) DEFAULT 1 COMMENT '共享状态（1：未生效，2：生肖中，3：已失效）',
                                       `vehicle_monthly_card_id` bigint(64) DEFAULT NULL COMMENT '月卡ID',
                                       `place_no` varchar(12) DEFAULT NULL COMMENT '车位号',
                                       `plate_nos` varchar(64) DEFAULT NULL COMMENT '车牌号（逗号隔开）',
                                       `remark` varchar(500) DEFAULT NULL COMMENT '备注',

                                       `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                       `status` int(2) DEFAULT NULL COMMENT '状态',
                                       `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                       `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='停车场AB车位信息表';

-- 停车场 团队车信息表
CREATE TABLE `parking_team_vehicle` (
                                        `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                        `parking_id` bigint(64) DEFAULT NULL COMMENT '停车场ID',
                                        `team_name` varchar(32) DEFAULT NULL COMMENT '团队名字',
                                        `company_id` bigint(64) DEFAULT NULL COMMENT '企业ID',
                                        `amount` int(11) DEFAULT 0 COMMENT '共享车位数',
                                        `limit_amount` int(11) DEFAULT 0 COMMENT '车辆限制数',
                                        `share_type` int(11) DEFAULT 1 COMMENT '共享类型（1：不限时间、2：指定期间）',
                                        `begin_time` datetime DEFAULT NULL COMMENT '共享 开始时间 ',
                                        `end_time` datetime DEFAULT NULL COMMENT '共享 结束时间',
                                        `share_status` int(11) DEFAULT 1 COMMENT '共享状态（1：未生效，2：生肖中，3：已失效）',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',

                                        `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                        `status` int(2) DEFAULT NULL COMMENT '状态',
                                        `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                        `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='停车场团队车信息表';

-- 停车场 团队车车辆绑定表
CREATE TABLE `parking_team_vehicle_binding` (
                                                `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                                `parking_id` bigint(64) DEFAULT NULL COMMENT '停车场ID',
                                                `team_id` bigint(64) DEFAULT NULL COMMENT '团队车ID',
                                                `vehicle_id` bigint(64) DEFAULT NULL COMMENT '车辆ID',

                                                `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                                `status` int(2) DEFAULT NULL COMMENT '状态',
                                                `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                                `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                                PRIMARY KEY (`id`),
                                                KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='停车场团队车车辆绑定表';

-- 停车场 团队车车辆绑定表
CREATE TABLE `parking_team_vehicle_place` (
                                              `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                              `park_id` bigint(64) DEFAULT NULL COMMENT '园区ID',

                                              `parking_id` bigint(64) DEFAULT NULL COMMENT '停车场ID',
                                              `team_id` bigint(64) DEFAULT NULL COMMENT '团队车ID',
                                              `place_id` bigint(64) DEFAULT NULL COMMENT '车位ID',

                                              `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                              `status` int(2) DEFAULT NULL COMMENT '状态',
                                              `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                              `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                              PRIMARY KEY (`id`),
                                              KEY `idx_id` (`id`) USING HASH
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='停车场团队车车辆绑定表';

-- 出入口增加类型字段
ALTER TABLE pmg_bat_park_parking_entrance ADD type int(11) DEFAULT NULL COMMENT '出入口类型：0 进，1出';

-- 车辆通过规则表增加出入口ID字段
ALTER TABLE pmg_bat_vehicle_pass_rule ADD entrance_id bigint(64) DEFAULT NULL COMMENT '出入口ID';

-- 车辆通过规则表增加车道ID字段
ALTER TABLE pmg_bat_vehicle_pass_rule ADD roadway_id bigint(64) DEFAULT NULL COMMENT '车道ID';

-- 企业信息表增加是否入驻字段
ALTER TABLE `pmg_bat_park_company`
    ADD COLUMN `settle_state` int(11) NOT NULL DEFAULT 0 COMMENT '是否入驻 0:未知、1:未入住、2:已入驻' AFTER `parent_id`;

ALTER TABLE `user_vehicle_data`
    ADD COLUMN `frame_no` varchar(20) NULL COMMENT '车架号' AFTER `is_deleted`,
ADD COLUMN `car_type` int(11) NULL COMMENT '车辆类型' AFTER `frame_no`,
ADD COLUMN `energy_type` int(11) NULL COMMENT '能耗类型' AFTER `car_type`;


CREATE TABLE `pmg_bat_manage_pass_rule` (
                                            `id` bigint(20) NOT NULL COMMENT '主键id',
                                            `protocol_type` int(11) NOT NULL COMMENT '协议类型（1：月卡协议，2：错时协议）',
                                            `protocol_name` varchar(50) NOT NULL COMMENT '协议名称',
                                            `protocol_content` text NOT NULL COMMENT '协议类型',
                                            `park_id` bigint(20) NOT NULL COMMENT '园区id',
                                            `parking_id` bigint(20) DEFAULT NULL COMMENT '停车场id',
                                            `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                            `status` int(11) DEFAULT NULL COMMENT '状态',
                                            `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
                                            `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
                                            `remark` varchar(500) DEFAULT '0' COMMENT '备注',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆管理协议表';

ALTER TABLE `pmg_bat_parking_coupon`
    ADD COLUMN `company_id` bigint(20) NOT NULL COMMENT '公司id' AFTER `phone`;


ALTER TABLE `pmg_bat_park_car`
    ADD COLUMN `source_type` int(11) NULL DEFAULT NULL COMMENT '车辆信息来源（1：系统同步（月卡，错时，固定车），2：后台创建，3 系统导入' AFTER `place_no`;


ALTER TABLE `pmg_bat_park_parking`
    ADD COLUMN `open_total_perm_place` int(11) NULL COMMENT '开放临时停车位数' AFTER `is_test_plate_no`;
ALTER TABLE `pmg_bat_park_parking`
    ADD COLUMN `open_total_temporary_place` int(11) NULL COMMENT '开放临时停车位数' AFTER `open_total_perm_place`;

CREATE TABLE `pmg_bat_park_pay_more_order_refund` (
                                                      `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                      `saas_user_id` bigint(20) DEFAULT NULL COMMENT '微信用户id',
                                                      `pay_user_id` varchar(64) NOT NULL COMMENT '支付平台用户ID',
                                                      `order_total_cost` bigint(20) NOT NULL COMMENT '订单总金额',
                                                      `refund_total_cost` bigint(20) NOT NULL COMMENT '退款总金额 单位 分',
                                                      `refund_source` int(11) DEFAULT NULL COMMENT '退款来源（1：月卡，2：错时卡，3：钱包余额）',
                                                      `refund_type` int(11) DEFAULT NULL COMMENT '退款类型（1：全部退款，2：部分退款）',
                                                      `cost_type` int(11) DEFAULT NULL COMMENT '费用类型（1：停车卡费 2 ：停车费）',
                                                      `apply_time` datetime DEFAULT NULL COMMENT '申请时间',
                                                      `apply_mode` int(11) DEFAULT NULL COMMENT '申请方式(1:系统创建，2：用户提交)',
                                                      `name` varchar(64) DEFAULT NULL COMMENT '姓名',
                                                      `phone` varchar(64) DEFAULT NULL COMMENT '手机号',
                                                      `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款理由',
                                                      `refuse_reason` varchar(255) DEFAULT NULL COMMENT '拒绝理由',
                                                      `ru_approval_data_id` varchar(64) DEFAULT NULL COMMENT '任务对应的业务数据表id',
                                                      `review_user_id` bigint(20) DEFAULT NULL COMMENT '审核人',
                                                      `review_user_name` varchar(200) DEFAULT NULL COMMENT '审核人姓名',
                                                      `review_time` datetime DEFAULT NULL COMMENT '审核时间',
                                                      `review_state` int(1) DEFAULT '0' COMMENT '审核状态（0 待审核 1 审核通过 2 拒绝 3 进行中 4 已取消）',
                                                      `park_id` bigint(20) NOT NULL COMMENT '园区ID',
                                                      `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                                      `status` int(2) DEFAULT NULL COMMENT '状态',
                                                      `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                                      `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                      `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1612398035289071619 DEFAULT CHARSET=utf8mb4 COMMENT='多订单支付账单退款表';

CREATE TABLE `pmg_bat_park_pay_more_order_refund_record` (
                                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                             `pay_more_order_refund_id` bigint(20) DEFAULT NULL COMMENT '多订单退款id',
                                                             `pay_number` varchar(64) NOT NULL COMMENT '支付账单订单号',
                                                             `order_number` varchar(64) NOT NULL COMMENT '月卡账单id',
                                                             `order_refund_number` varchar(64) DEFAULT NULL COMMENT '退款订单号',
                                                             `order_cost` bigint(20) NOT NULL COMMENT '订单实收金额 单位 分',
                                                             `refund_cost` bigint(20) NOT NULL COMMENT '订单退款金额 单位 分',
                                                             `refund_state` int(1) DEFAULT '0' COMMENT '退款状态 0：退款中、1：退款成功、2：退款失败',
                                                             `refund_type` int(11) DEFAULT NULL COMMENT '退款类型（1：全部退款，2：部分退款）',
                                                             `cost_type` int(11) DEFAULT NULL COMMENT '费用类型（1：停车卡费 2 ：停车费）',
                                                             `refund_mode` int(11) DEFAULT NULL COMMENT '退款方式',
                                                             `refund_account` varchar(50) DEFAULT NULL COMMENT '退款账号',
                                                             `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
                                                             `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款理由',
                                                             `park_id` bigint(20) NOT NULL COMMENT '园区ID',
                                                             `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
                                                             `status` int(2) DEFAULT NULL COMMENT '状态',
                                                             `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
                                                             `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
                                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                             `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
                                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                             `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
                                                             PRIMARY KEY (`id`) USING BTREE,
                                                             KEY `idx_pay_more_order_refund_id` (`pay_more_order_refund_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1612398035289071619 DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';


-- 错时车审核增加退卡时间字段
ALTER TABLE staggered_monthly_card_data ADD refund_card_time datetime DEFAULT NULL COMMENT '退卡时间';

-- 月卡审核增加退卡时间字段
ALTER TABLE vehicle_monthly_card_data ADD refund_card_time datetime DEFAULT NULL COMMENT '退卡时间';

-- 车辆分组表增加创建人名称字段
ALTER TABLE vehicle_group ADD create_user_name varchar(50) DEFAULT NULL COMMENT '创建人名称';

-- 团队车信息表增加联系人姓名字段
ALTER TABLE parking_team_vehicle ADD person_name varchar(20) DEFAULT NULL COMMENT '联系人姓名';

-- 团队车信息表增加联系人电话字段
ALTER TABLE parking_team_vehicle ADD person_phone varchar(20) DEFAULT NULL COMMENT '联系人电话';

-- 团队车信息表增加附件字段
ALTER TABLE parking_team_vehicle ADD file varchar(500) DEFAULT NULL COMMENT '附件';

-- 团队车信息表增加统一支付字段
ALTER TABLE parking_team_vehicle ADD payment int(11) DEFAULT NULL COMMENT '统一支付（0：未开启，1：开启）';

-- 团队车信息表增加已添加车辆数量字段
ALTER TABLE parking_team_vehicle ADD vehicle_number int(11) DEFAULT '0' COMMENT '已添加车辆数量';

-- AB车位表增加附件字段
ALTER TABLE parking_share_place ADD file varchar(500) DEFAULT NULL COMMENT '附件';

-- 停车场信息表增加停车场省市区code字段
ALTER TABLE pmg_bat_park_parking ADD area_code varchar(500) DEFAULT NULL COMMENT '停车场省市区code';

-- 车辆分组通过规则表增加出入口ID字段
ALTER TABLE vehicle_group_pass_rule ADD entrance_id bigint(64) DEFAULT NULL COMMENT '出入口ID';

-- 车辆分组通过规则表增加车道ID字段
ALTER TABLE vehicle_group_pass_rule ADD roadway_id bigint(64) DEFAULT NULL COMMENT '车道ID';

-- 车辆分组通过规则表增加备注字段
ALTER TABLE vehicle_group_pass_rule ADD remark varchar(500) DEFAULT NULL COMMENT '备注';

-- 修改备注信息 张逸飞 2023年4月4日 11:44:45
ALTER TABLE pmg_bat_park_act_hi_task MODIFY COLUMN user_name varchar(20) DEFAULT NULL COMMENT '移动端用户名称';
ALTER TABLE pmg_bat_park_act_hi_task MODIFY COLUMN pay_user_id varchar(64) DEFAULT NULL COMMENT '移动端用户ID';