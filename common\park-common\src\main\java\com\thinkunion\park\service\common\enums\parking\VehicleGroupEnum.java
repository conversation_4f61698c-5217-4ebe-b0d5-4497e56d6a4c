package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 车辆分组（1 月卡车 2 错时车 3 临时车）
 * <AUTHOR>
 */
@AllArgsConstructor
public enum VehicleGroupEnum {


    THE_MOON_CAR(1, "月卡车"),
    WRONG_TIME_CAR(2, "错时车"),
    TEMPORARY_CAR(3, "临时车"),
   ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        VehicleGroupEnum[] enumArray = VehicleGroupEnum.values();
        for (VehicleGroupEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
