package com.thinkunion.park.service.common.iot.exception;

import org.nest.springwrap.core.tool.api.DeviceCode;


public class ApiRuntimeException extends RuntimeException {

    private static final long serialVersionUID = 534996425110290578L;
    protected String code;
    protected String message;

    public ApiRuntimeException(DeviceCode code) {
        this.code = code.returnTextCode();
        this.message = code.getMessage();
    }

    public ApiRuntimeException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ApiRuntimeException(int code, String message) {
        super(message);
        this.code = String.valueOf(code);
        this.message = message;
    }

    public ApiRuntimeException(String code, String message, Throwable cause) {
        super(cause);
        this.code = code;
        this.message = message;
    }

    public ApiRuntimeException(String message) {
        super(message);
    }

    public ApiRuntimeException(Throwable cause) {
        super(cause);
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
