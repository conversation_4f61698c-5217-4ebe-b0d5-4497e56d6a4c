package com.thinkunion.park.service.common.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName HikIscResponse
 * @Description 海康返回实体类
 * <AUTHOR>
 * @Date 2022年10月31日 16:20:38
 */
@Data
public class HikIscResponse implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 0 – 成功，其他- 失败
     */
    @ApiModelProperty(value = "返回码")
    private String code;
    @ApiModelProperty(value = "接口执行情况说明信息")
    private String msg;
    @ApiModelProperty(value = "返回信息结构体")
    private Object data;
}
