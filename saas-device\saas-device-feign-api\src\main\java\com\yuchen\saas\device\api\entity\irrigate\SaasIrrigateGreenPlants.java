package com.yuchen.saas.device.api.entity.irrigate;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * <p>
 * 绿植信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_irrigate_green_plants")
@ApiModel(value="SaasIrrigateGreenPlants对象", description="绿植信息表")
public class SaasIrrigateGreenPlants extends BaseEntity {

    /**
     * 绿植编码（id）
     */
    @ApiModelProperty(name = "greenPlantsCode", value = "绿植编码（id）")
    private String greenPlantsCode;
    /**
     * 绿植名称
     */
    @ApiModelProperty(name = "greenPlantsName", value = "绿植名称")
    private String greenPlantsName;
    /**
     * 备注（描述信息）
     */
    @ApiModelProperty(name = "describeContents", value = "备注（描述信息）")
    private String describeContents;
    /**
     * 养护知识文档地址
     */
    @ApiModelProperty(name = "maintainingFile", value = "养护知识文档地址")
    private String maintainingFile;
    /**
     * 养护知识富文本
     */
    @ApiModelProperty(name = "maintainingRichText", value = "养护知识富文本")
    private String maintainingRichText;
    /**
     * 园区ID（项目id）
     */
    @ApiModelProperty(name = "parkId", value = "园区ID（项目id）")
    private Long parkId;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "createOrgUser", value = "创建人")
    private Long createOrgUser;
}
