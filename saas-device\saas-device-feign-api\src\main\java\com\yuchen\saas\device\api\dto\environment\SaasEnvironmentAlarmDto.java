package com.yuchen.saas.device.api.dto.environment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SaasEnvironmentAlarmDto {
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 告警ID
     */
    @ApiModelProperty(name = "alarmId", value = "告警ID")
    private Long alarmId;
    /**
     * 告警名称
     */
    @ApiModelProperty(name = "alarmName", value = "告警名称")
    private String alarmName;
    /**
     * 告警时间
     */
    @ApiModelProperty(name = "alarmTime", value = "告警时间")
    private Date alarmTime;
    /**
     * 告警等级
     */
    @ApiModelProperty(name = "alarmLevel", value = "告警等级")
    private Integer alarmLevel;
    /**
     * 告警类型
     */
    @ApiModelProperty(name = "alarmType", value = "告警类型")
    private Integer alarmType;
    /**
     * 告警位置
     */
    @ApiModelProperty(name = "alarmLocation", value = "告警位置")
    private String alarmLocation;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 设备标识
     */
    @ApiModelProperty(name = "deviceName", value = "设备标识")
    private String deviceName;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceLocation", value = "设备位置")
    private String deviceLocation;

}
