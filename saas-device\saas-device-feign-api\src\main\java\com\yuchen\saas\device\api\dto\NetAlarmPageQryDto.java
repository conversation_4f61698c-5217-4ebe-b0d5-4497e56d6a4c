package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

@Data
public class NetAlarmPageQryDto extends Query {

    /**
     * 客户id
     */
    private String customerManageId;
    /**
     * 告警名称
     */
    private String alarmName;
    /**
     * 告警级别; 值1，2，3，4，5
     */
    private Integer alarmLevel;
    /**
     * 触发方式; 1-设备上线，2-设备离线，3-属性上报，4-事件上报
     */
    private Integer triggerMode;
    /**
     * 服务应用id
     */
    @ApiModelProperty(name = "serviceId", value = "服务应用id")
    private Long serviceId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;

}
