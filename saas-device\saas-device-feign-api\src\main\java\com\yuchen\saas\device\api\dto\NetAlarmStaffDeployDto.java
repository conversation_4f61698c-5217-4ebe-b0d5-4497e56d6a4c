package com.yuchen.saas.device.api.dto;

import com.yuchen.saas.device.api.entity.NetAlarmAutoDeployUser;
import com.yuchen.saas.device.api.entity.NetAlarmStaffDeploy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/25 14:36
 * @Description TODO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NetAlarmStaffDeployDto extends NetAlarmStaffDeploy {
    /**
     * 自动派工人员列表
     */
    @ApiModelProperty(name = "autoDeployUserList", value = "自动派工人员列表")
    List<NetAlarmAutoDeployUser> autoDeployUserList;
}
