package com.yuchen.saas.device.api.entity.product;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 产品属性
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_product_properties")
@ApiModel(value="NetProductProperties对象", description="产品属性")
public class NetProductProperties extends BaseEntity {

    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 属性id
     */
    @ApiModelProperty(name = "propertiesId", value = "属性id")
    private String propertiesId;
    /**
     * 属性标识
     */
    @ApiModelProperty(name = "propertiesKey", value = "属性标识")
    private String propertiesKey;
    /**
     * 属性名称
     */
    @ApiModelProperty(name = "propertiesName", value = "属性名称")
    private String propertiesName;
    /**
     * 属性类型
     */
    @ApiModelProperty(name = "dataType", value = "属性类型")
    private Integer dataType;
    /**
     * 精度
     */
    @ApiModelProperty(name = "precise", value = "精度")
    private Integer precise;
    /**
     * 单位
     */
    @ApiModelProperty(name = "unit", value = "单位")
    private String unit;
    /**
     * 0-只读，1-读写
     */
    @ApiModelProperty(name = "isRead", value = "0-只读，1-读写")
    private Integer isRead;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;



}
