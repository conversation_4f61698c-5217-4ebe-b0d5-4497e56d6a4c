package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/26 20:17
 * @Description TODO
 */
@Data
public class NetAlarmCloseDto implements Serializable {
    /**
     * id主键
     */
    private String ids;
    /**
     * 关闭原因
     */
    @ApiModelProperty(name = "closeReason", value = "关闭原因")
    private String closeReason;
    /**
     * 关闭备注
     */
    @ApiModelProperty(name = "closeRemark", value = "关闭备注")
    private String closeRemark;
}
