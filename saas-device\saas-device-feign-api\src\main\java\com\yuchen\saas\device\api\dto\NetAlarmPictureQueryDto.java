package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import javax.validation.constraints.NotNull;
import java.util.Date;


@Data
public class NetAlarmPictureQueryDto extends Query {

    @NotNull
    @ApiModelProperty(name = "parkId", value = "园区ID")
    private Long parkId;

    @ApiModelProperty(name = "personType", value = "人员类型")
    private String personType;

    @ApiModelProperty(name = "deviceId", value = "平台设备ID")
    private Long deviceId;

    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;

    @ApiModelProperty(name = "deviceName", value = "设备deviceName")
    private String deviceName;

    @ApiModelProperty(name = "deviceNo", value = "平台设备编码")
    private String deviceNo;

    @ApiModelProperty(name = "timeStampBegin", value = "开始时间(iot属性)")
    private Date timeStampBegin;

    @ApiModelProperty(name = "timeStampEnd", value = "结束时间(iot属性)")
    private Date timeStampEnd;

}
