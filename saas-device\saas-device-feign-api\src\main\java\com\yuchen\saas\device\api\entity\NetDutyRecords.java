package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("net_duty_records")
public class NetDutyRecords {
    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 所属性项目id
     */
    private Long parkId;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 工号
     */
    private String jobNum;
    /**
     * 职务
     */
    private String post;
    /**
     * 电话
     */
    private String phone;
    /**
     * 班次
     */
    private String shift;
    /**
     * 事件类型
     */
    private String eventType;
    /**
     * 上报时间
     */
    private String reportTime;
    /**
     * 创建时间
     */
    private Date createTime;
}
