package com.yuchen.saas.device.api.dto.environment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.List;

@Data
public class SaasEnvironmentDeviceQueryDto extends Query {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 设备分类(2:硬件设备 3:传感设备)
     */
    @ApiModelProperty(name = "deviceCategory", value = "设备分类(2:硬件设备 3:传感设备)")
    private Integer deviceCategory;
    /**
     * 场景类型(1:环境监测 3:智慧浇灌 4:智慧消防)
     */
    @ApiModelProperty(name = "sceneType", value = "场景类型(1:环境监测 3:智慧浇灌 4:智慧消防)")
    private Integer sceneType;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;
    /**
     * 所属产品
     */
    @ApiModelProperty(name = "productKey", value = "所属产品")
    private String productKey;
    /**
     * 所属产品列表
     */
    @ApiModelProperty(name = "productKeyList", value = "所属产品列表")
    List<String> productKeyList;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 设备类型ID
     */
    @ApiModelProperty(name = "deviceTypeId", value = "设备类型ID")
    private Long deviceTypeId;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceLocation", value = "设备位置")
    private String deviceLocation;
    /**
     * 安装空间ID
     */
    @ApiModelProperty(name = "installSpaceId", value = "安装空间ID")
    private Long installSpaceId;
    /**
     * 最后连接开始时间
     */
    @ApiModelProperty(name = "lastConnectStartTime", value = "最后连接开始时间")
    private String lastConnectStartTime;
    /**
     * 最后连接结束时间
     */
    @ApiModelProperty(name = "lastConnectEndTime", value = "最后连接结束时间")
    private String lastConnectEndTime;
    /**
     * 最后断开开始时间
     */
    @ApiModelProperty(name = "lastDisconnectStartTime", value = "最后断开开始时间")
    private String lastDisconnectStartTime;
    /**
     * 最后断开结束时间
     */
    @ApiModelProperty(name = "lastDisconnectEndTime", value = "最后断开结束时间")
    private String lastDisconnectEndTime;

    @ApiModelProperty(name = "brandModel", value = "品牌名称")
    private String brandModel;

}
