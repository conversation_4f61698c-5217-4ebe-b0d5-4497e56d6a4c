package com.yuchen.saas.auth.config;

import com.yuchen.saas.auth.support.NestJwtTokenEnhancer;
import org.nest.springwrap.core.jwt.props.JwtProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;


@Configuration
@ConditionalOnProperty(prefix = "nest.security.oauth2", name = "storeType", havingValue = "jwt", matchIfMissing = true)
public class JwtTokenStoreConfiguration {


    @Bean
    public TokenStore jwtTokenStore(JwtProperties jwtProperties) {
        return new JwtTokenStore(jwtAccessTokenConverter(jwtProperties));
    }


    @Bean
    public JwtAccessTokenConverter jwtAccessTokenConverter(JwtProperties jwtProperties) {
        JwtAccessTokenConverter accessTokenConverter = new JwtAccessTokenConverter();
        accessTokenConverter.setSigningKey(jwtProperties.getSignKey());
        return accessTokenConverter;
    }


    @Bean
    @ConditionalOnMissingBean(name = "jwtTokenEnhancer")
    public TokenEnhancer jwtTokenEnhancer(JwtAccessTokenConverter jwtAccessTokenConverter,
                                          JwtProperties jwtProperties) {
        return new NestJwtTokenEnhancer(jwtAccessTokenConverter, jwtProperties);
    }

}
