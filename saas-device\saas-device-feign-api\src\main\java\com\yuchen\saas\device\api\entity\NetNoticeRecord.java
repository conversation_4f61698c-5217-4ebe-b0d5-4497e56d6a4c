package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("net_notice_record")
public class NetNoticeRecord {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属客户id
     */
    private Long customerManageId;

    /**
     * 运维设置id
     */
    private Long operateSettingsId;
    /**
     * 消息发送类型
     */
    private Integer noticeType;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 消息模板id
     */
    private Long noticeTemplateId;

    /**
     * 标题
     */
    private String title;

    /**
     * 发送内容
     */
    private String content;

    /**
     * 状态：1-成功，0-失败
     */
    private Integer status;

    /**
     * 发送时间
     */
    private Date createTime;


}
