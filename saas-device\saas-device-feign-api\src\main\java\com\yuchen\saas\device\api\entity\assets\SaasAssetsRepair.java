package com.yuchen.saas.device.api.entity.assets;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

import java.util.Date;

/**
 * 资产报修表
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_assets_repair")
@ApiModel(value="SaasAssetsRepair对象", description="资产报修表")
public class SaasAssetsRepair extends BaseEntity {

    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    private Long customerId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 创建人组织用户id
     */
    @ApiModelProperty(name = "createUserOrgId", value = "创建人组织用户id")
    private Long createUserOrgId;
    /**
     * 创建人账号
     */
    @ApiModelProperty(name = "createUserAccount", value = "创建人账号")
    private String createUserAccount;
    /**
     * 故障类型(1:设备故障，2：外观损害，3：联网异常，4：其他故障)
     */
    @ApiModelProperty(name = "breakdownType", value = "故障类型(1:设备故障，2：外观损害，3：联网异常，4：其他故障)")
    private String breakdownType;
    /**
     * 申请时间
     */
    @ApiModelProperty(name = "repairTime", value = "申请时间")
    private Date repairTime;
    /**
     * 报修人userId
     */
    @ApiModelProperty(name = "repairUserId", value = "报修人userId")
    private Long repairUserId;
    /**
     * 报修人名称
     */
    @ApiModelProperty(name = "repairUserName", value = "报修人名称")
    private String repairUserName;
    /**
     * 联系电话
     */
    @ApiModelProperty(name = "phone", value = "联系电话")
    private String phone;
    /**
     * 送修位置id
     */
    @ApiModelProperty(name = "repairAddressId", value = "送修位置id")
    private Long repairAddressId;
    /**
     * 送修位置名称
     */
    @ApiModelProperty(name = "repairAddress", value = "送修位置名称")
    private String repairAddress;
    /**
     * 金额合计 单位：分
     */
    @ApiModelProperty(name = "totalCost", value = "金额合计 单位：分")
    private Long totalCost;
    /**
     * 备注信息(报修内容)
     */
    @ApiModelProperty(name = "repairRemark", value = "备注信息(报修内容)")
    private String repairRemark;
    /**
     * 附件图片地址 逗号隔开
     */
    @ApiModelProperty(name = "repairFile", value = "附件图片地址 逗号隔开")
    private String repairFile;
    /**
     * 单据状态
     */
    @ApiModelProperty(name = "receiptStatus", value = "单据状态")
    private Integer receiptStatus;
    /**
     * 单据编号
     */
    @ApiModelProperty(name = "receiptCode", value = "单据编号")
    private String receiptCode;
    /**
     * 关联工单编号
     */
    @ApiModelProperty(name = "relatedWorkOrderCode", value = "关联工单编号")
    private String relatedWorkOrderCode;
    /**
     * 流程实例id
     */
    @ApiModelProperty(name = "processInstanceId", value = "流程实例id")
    private String processInstanceId;
    /**
     * 资产数据
     */
    @ApiModelProperty(name = "assetsData", value = "资产数据")
    private String assetsData;



}
