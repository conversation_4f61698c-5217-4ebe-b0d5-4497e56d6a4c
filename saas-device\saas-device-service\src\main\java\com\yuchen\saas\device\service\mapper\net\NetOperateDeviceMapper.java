package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.dto.operatingEnd.NetDevicePageQryDTO;
import com.yuchen.saas.device.api.entity.NetOperateDevice;
import com.yuchen.saas.device.api.vo.operatingEnd.NetOperateDevicePageVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface NetOperateDeviceMapper extends BaseMapper<NetOperateDevice> {


    @Select("SELECT od.*,p.product_name from net_operate_device od" +
            " left join net_product p on od.product_key = p.product_key" +
            " ${ew.customSqlSegment}")
    Page<NetOperateDevicePageVO> selectOperateDevicePage(Page<NetOperateDevicePageVO> page, @Param(Constants.WRAPPER) QueryWrapper<String> wrapper);
}
