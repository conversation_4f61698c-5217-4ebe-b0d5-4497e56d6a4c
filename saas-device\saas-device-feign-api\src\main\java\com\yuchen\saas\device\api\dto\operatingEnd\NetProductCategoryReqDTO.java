package com.yuchen.saas.device.api.dto.operatingEnd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/5/21 10:02
 */
@Data
public class NetProductCategoryReqDTO {

    /**
     * 父节点id
     */
    @ApiModelProperty(name = "parentId", value = "父节点id")
    private Long parentId;
    /**
     * 分类名称
     */
    @ApiModelProperty(name = "categoryName", value = "分类名称")
    private String categoryName;
    /**
     * 分类标识
     */
    @ApiModelProperty(name = "categoryCode", value = "分类标识")
    private String categoryCode;

    /**
     * 业务状态
     */
    @ApiModelProperty("业务状态")
    private Integer status;

}
