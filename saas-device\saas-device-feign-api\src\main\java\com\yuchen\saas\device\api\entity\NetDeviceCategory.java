package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 设备类型信息表
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_device_category")
@ApiModel(value="NetDeviceCategory对象", description="设备类型信息表")
public class NetDeviceCategory extends BaseEntity {

    /**
     * code
     */
    @ApiModelProperty(name = "categoryCode", value = "code")
    private String categoryCode;
    /**
     * 设备类型名称
     */
    @ApiModelProperty(name = "categoryName", value = "设备类型名称")
    private String categoryName;
    /**
     * 大类（1：视频监控，2：智能硬件，3：传感设备）
     */
    @ApiModelProperty(name = "broadCategory", value = "大类（1：视频监控，2：智能硬件，3：传感设备）")
    private String broadCategory;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;



}
