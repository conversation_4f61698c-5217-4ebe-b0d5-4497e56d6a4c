package com.yuchen.saas.consumer.service;

import org.nest.springwrap.core.cloud.feign.EnableNestFeign;
import org.nest.springwrap.core.launch.NestApplication;
import org.nest.springwrap.core.launch.app.AppNameConstant;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @ClassName SaasConsumerApplication
 * @Description 移动端服务启动类
 * <AUTHOR>
 * @Date 2022年11月9日 15:50:58
 */
@EnableNestFeign
@SpringCloudApplication
@EnableScheduling
@ComponentScan("com.yuchen")
@EnableAsync
public class SaasConsumerApplication {
	public static void main(String[] args) {
		NestApplication.run(AppNameConstant.SAAS_CONSUMER, SaasConsumerApplication.class, args);
	}
}
