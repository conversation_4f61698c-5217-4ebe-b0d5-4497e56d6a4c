package com.yuchen.saas.device.api.dto.scene;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 场景联动规则设置
 */
@Data
public class SetSceneRuleDto {

    /**
     * 场景id
     */
    @NotNull(message = "场景id不能为空")
    private Long sceneId;

    /**
     * 触发类型：1-设备触发 2：定时触发。3：手动触发
     */
    @NotNull(message = "触发方式不为空")
    private int triggerMode = 1;

    /**
     * xxl定时任务cron表达式（定时触发有值）
     */
    private String cron;

    /**
     * xxl任务id
     */
    private Integer jobId;

    /**
     * 触发器列表（	"triggers": [{
     * 		"uri": "trigger/device/event",
     * 		"params": {
     * 			"productKey": "IzxBW2gWif1wZuTT",
     * 			"eventCode": "_all",
     * 			"deviceName": "ddddd11"
     *                }* 	}],）
     */
    private List<SceneTriggerCondition> triggerConditionList = new ArrayList<>();

    /**
     * 执行条件列表（conditions": [{
     * 		"uri": "condition/device/property",
     * 		"params": {
     * 			"productKey": "IzxBW2gWif1wZuTT",
     * 			"deviceName": "ddddd11",
     * 			"propertyName": "text_p4",
     * 			"compareType": "eq",
     * 			"compareValue": "11",
     * 			"propMode": "text"
     *                }* 	}    ]）
     */
    private List<SceneExecuteCondition> executeConditionList = new ArrayList<>();

    /**
     * 执行动作列表（"actions": [{
     * 		"uri": "action/device/setProperty",
     * 		"params": {
     * 			"propMode": "text",
     * 			"productKey": "IzxBW2gWif1wZuTT",
     * 			"deviceName": "ddddd11",
     * 			"propertyItems": {
     * 				"text_p4": "aa"
     *                        }* 		}
     *    }]）
     */
    @Size(min = 1,message = "执行动作不能为空")
    private List<SceneExecuteAction> executeActionList = new ArrayList<>();

}
