package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 设备型号能力信息表
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_device_model_ability")
@ApiModel(value="NetDeviceModelAbility对象", description="设备型号能力信息表")
public class NetDeviceModelAbility extends BaseEntity {

    /**
     * 设备型号id
     */
    @ApiModelProperty(name = "deviceModelId", value = "设备型号id")
    private Long deviceModelId;
    /**
     * 能力类型（1：功能，2：属性，3：事件，4：标签）
     */
    @ApiModelProperty(name = "abilityType", value = "能力类型")
    private String abilityType;
    /**
     * 能力对应id
     */
    @ApiModelProperty(name = "abilityId", value = "能力对应id")
    private Long abilityId;
    /**
     * 能力数据json（对应内容）
     * 事件：{"eventId": "","eventKey": "","eventName": "","eventType": 0,"jsonParam": ""}
     * 功能：{ "functionId": "", "functionKey": "", "functionName": "", "invokeModel": "", "url": "", "inputJsonParam": "", "outputJsonParam": "" }
     * 属性：{ "propertiesId": "", "propertiesKey": "", "propertiesName": "", "dataType": 0, "precise": 0, "unit": "", "isRead": 0,}
     */
    @ApiModelProperty(name = "abilityData", value = "能力数据json（对应内容）")
    private String abilityData;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remark", value = "备注信息")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;



}
