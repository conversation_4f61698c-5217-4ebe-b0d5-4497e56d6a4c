package com.thinkunion.park.service.common.iot.capability.device.payload;

import com.google.gson.*;
import com.thinkunion.park.service.common.iot.util.TextUtils;

import java.lang.reflect.Type;
import java.util.Iterator;
import java.util.Set;

public class KeyValuePair {

    private String key;
    private ValueWrapper valueWrapper;

    public KeyValuePair(String name, ValueWrapper value) {
        this.key = name;
        this.valueWrapper = value;
    }

    public KeyValuePair(String name, int value) {
        this.key = name;
        this.valueWrapper = new ValueWrapper.IntValueWrapper(value);
    }

    public KeyValuePair(String name, String value) {
        this.key = name;
        this.valueWrapper = new ValueWrapper.StringValueWrapper(value);
    }

    public KeyValuePair(String name, boolean value) {
        this.key = name;
        this.valueWrapper = new ValueWrapper.BooleanValueWrapper(value ? 1 : 0);
    }

    public String getKey() {
        return this.key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public ValueWrapper getValueWrapper() {
        return this.valueWrapper;
    }

    public void setValueWrapper(ValueWrapper valueWrapper) {
        this.valueWrapper = valueWrapper;
    }

    public static class KeyValuePairJsonDeSerializer implements JsonDeserializer<KeyValuePair> {

        public KeyValuePairJsonDeSerializer() {
        }

        public KeyValuePair deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
                throws JsonParseException {
            if (json == null) {
                return null;
            } else {
                KeyValuePair propertyPair = null;
                if (!json.isJsonObject()) {
                    return propertyPair;
                } else {
                    JsonObject jsonObject = json.getAsJsonObject();
                    Set<String> keySet = jsonObject.keySet();
                    if (keySet != null && jsonObject.size() >= 1) {
                        Iterator var7 = keySet.iterator();

                        while (var7.hasNext()) {
                            String key = (String) var7.next();
                            JsonElement valueElement = jsonObject.get(key);
                            if (valueElement != null && !valueElement.isJsonNull() && valueElement.isJsonPrimitive()) {
                                JsonPrimitive jsonPrimitive = (JsonPrimitive) valueElement;
                                if (jsonPrimitive.isString()) {
                                    String valueStr = valueElement.getAsString();
                                    if (!TextUtils.isEmpty(valueStr)) {
                                        propertyPair = new KeyValuePair(key, valueStr);
                                        break;
                                    }
                                } else if (jsonPrimitive.isNumber()) {
                                    Integer valueInt = valueElement.getAsInt();
                                    if (valueInt != null) {
                                        propertyPair = new KeyValuePair(key, valueInt);
                                        break;
                                    }
                                } else if (jsonPrimitive.isBoolean()) {
                                    Boolean valueBool = valueElement.getAsBoolean();
                                    if (valueBool != null) {
                                        propertyPair = new KeyValuePair(key, valueBool);
                                        break;
                                    }
                                }
                            }
                        }

                        return propertyPair;
                    } else {
                        return propertyPair;
                    }
                }
            }
        }
    }

    public static class KeyValuePairJsonSerializer implements JsonSerializer<KeyValuePair> {

        public KeyValuePairJsonSerializer() {
        }

        public JsonElement serialize(KeyValuePair src, Type typeOfSrc, JsonSerializationContext context) {
            JsonObject jsonObject = null;
            if (src == null) {
                return jsonObject;
            } else {
                if (src != null && !TextUtils.isEmpty(src.getKey()) && src.getValueWrapper() != null) {
                    jsonObject = new JsonObject();
                    if ("int".equalsIgnoreCase(src.getValueWrapper().getType())) {
                        ValueWrapper.IntValueWrapper intValue = (ValueWrapper.IntValueWrapper) src.getValueWrapper();
                        jsonObject.addProperty(src.getKey(), intValue.getValue());
                    } else if ("string".equalsIgnoreCase(src.getValueWrapper().getType())) {
                        ValueWrapper.StringValueWrapper strValue = (ValueWrapper.StringValueWrapper) src
                                .getValueWrapper();
                        jsonObject.addProperty(src.getKey(), strValue.getValue());
                    } else if ("bool".equalsIgnoreCase(src.getValueWrapper().getType())) {
                        ValueWrapper.BooleanValueWrapper boolValue = (ValueWrapper.BooleanValueWrapper) src
                                .getValueWrapper();
                        jsonObject.addProperty(src.getKey(), boolValue.getValue());
                    }
                }

                return jsonObject;
            }
        }
    }
}
