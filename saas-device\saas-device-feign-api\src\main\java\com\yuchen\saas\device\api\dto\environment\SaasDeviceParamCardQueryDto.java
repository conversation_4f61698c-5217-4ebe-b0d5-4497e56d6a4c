package com.yuchen.saas.device.api.dto.environment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

@Data
public class SaasDeviceParamCardQueryDto extends Query {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceId", value = "设备名称")
    private String deviceName;
    /**
     * 参数名称
     */
    @ApiModelProperty(name = "paramName", value = "参数名称")
    private String paramName;
    /**
     * 参数别名
     */
    @ApiModelProperty(name = "paramAlias", value = "参数别名")
    private String paramAlias;

}
