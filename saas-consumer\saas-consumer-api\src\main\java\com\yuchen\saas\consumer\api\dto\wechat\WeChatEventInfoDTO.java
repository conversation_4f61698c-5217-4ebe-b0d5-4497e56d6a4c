package com.yuchen.saas.consumer.api.dto.wechat;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 微信公众号关注取消事件信息
 * <AUTHOR>
 * @create 2024/1/2 15:00
 */
@Data
@Accessors(chain = true)
public class WeChatEventInfoDTO {

    private String toUserName;
    private String fromUserName;
    private String createTime;
    private String msgType;
    private String event;
    private String content;
    private String msgId;

}
