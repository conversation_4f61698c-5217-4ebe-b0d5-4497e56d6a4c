package com.yuchen.saas.device.api.dto.operatingEnd;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @create 2024/5/22 10:02
 */
@Data
public class NetThirdPlatformSubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;



    @ApiModelProperty(name = "thirdPlatformCode", value = "第三方平台编号")
    @NotBlank(message = "平台编号不能为空")
    private String thirdPlatformCode;
    /**
     * 对接方式
     */
    @ApiModelProperty(name = "dockingMethod", value = "对接方式")
    @NotBlank(message = "请选择对接方式")
    private String dockingMethod;
    /**
     * 消息协议
     */
    @ApiModelProperty(name = "messageProtocol", value = "消息协议")
    private String messageProtocol;

    /**
     * 业务状态(1:启动，0停止)
     */
    @ApiModelProperty("业务状态(1:启动，0停止)")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
