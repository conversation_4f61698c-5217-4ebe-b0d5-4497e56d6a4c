package com.yuchen.saas.device.service.listener;

import com.yuchen.saas.device.service.event.SetCustomerManageNameEvent;
import com.yuchen.saas.manage.system.api.entity.NestCustomerManage;
import com.yuchen.saas.manage.system.api.feign.CustomerManageFeignServer;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.tool.api.R;
import org.nest.springwrap.core.tool.utils.ClassUtil;
import org.nest.springwrap.core.tool.utils.Func;
import org.nest.springwrap.core.tool.utils.ObjectUtil;
import org.nest.springwrap.core.tool.utils.ReflectUtil;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

@Slf4j
@Service
public class SetCustomerManageNameEventListener {
    @Resource
    private CustomerManageFeignServer customerManageFeignServer;


    @EventListener
    public void setManageAreaNames(SetCustomerManageNameEvent event) throws Exception {
        List<?> dataList = event.getDataList();
        if (Func.isEmpty(dataList)) {
            return;
        }
        R<List<NestCustomerManage>> customerRes = customerManageFeignServer.getAllCustomerManageList();
        if (!customerRes.isSuccess() || customerRes.getData() == null || customerRes.getData().size() == 0) {
            return;
        }
        try {
            Object tmpData = dataList.get(0);
            Field customerManageIdField = ReflectUtil.getField(tmpData.getClass(), "customerManageId");
            Field customerManageNameField = ReflectUtil.getField(tmpData.getClass(), "customerManageName");
            if (ObjectUtil.isEmpty(customerManageIdField) || ObjectUtil.isEmpty(customerManageNameField)) {
                return;
            }
            for (Object data : dataList) {
                Method getCustomerManageId = ClassUtil.getMethod(data.getClass(), "getCustomerManageId", new Class[0]);
                String dataCustomerManageId = String.valueOf(getCustomerManageId.invoke(data));
                for (NestCustomerManage customerManage : customerRes.getData()) {
                    if (String.valueOf(customerManage.getId()).equals(dataCustomerManageId)) {
                        Method setCustomerManageName = ClassUtil.getMethod(data.getClass(), "setCustomerManageName", new Class[]{String.class});
                        setCustomerManageName.invoke(data, customerManage.getName());
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }



}
