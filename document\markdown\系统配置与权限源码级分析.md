# 系统配置与权限源码级分析

## 1. 主要涉及的实体类

- **PmgSysParam**：系统参数表，记录全局参数、业务开关等。
- **PmgSysDict**：数据字典表，记录各类枚举、类型、业务字典。
- **PmgSysTenant**：租户信息表。
- **PmgSysUser**：用户信息表。
- **PmgSysRole**：角色表。
- **PmgSysMenu**：菜单表。
- **PmgSysUserRole**、**PmgSysRoleMenu**：用户-角色、角色-菜单关联表。
- **PmgBatPassRule**：通行规则表。
- **PmgBatChargeRule**：计费规则表。
- **PmgBatFreePassRule**：免费放行规则表。
- **PmgBatHoliday**：节假日规则表。
- **PmgBatTestMode**：测试模式配置表。
- **PmgSysMessageConfig**：消息推送、短信、微信、钉钉等通知配置表。
- **PmgBatCoupon**、**PmgBatCouponRecord**：优惠券、发放、使用记录表。

## 2. 主要Controller

- `PmgSysParamController`：系统参数管理接口
- `PmgSysDictController`：数据字典管理接口
- `PmgSysTenantController`：租户管理接口
- `PmgSysUserController`：用户管理接口
- `PmgSysRoleController`：角色管理接口
- `PmgSysMenuController`：菜单管理接口
- `PmgSysUserRoleController`、`PmgSysRoleMenuController`：用户-角色、角色-菜单管理接口
- `PmgBatPassRuleController`：通行规则配置接口
- `PmgBatChargeRuleController`：计费规则配置接口
- `PmgBatFreePassRuleController`：免费放行规则配置接口
- `PmgBatHolidayController`：节假日规则配置接口
- `PmgBatTestModeController`：测试模式配置接口
- `PmgSysMessageConfigController`：消息推送、通知配置接口
- `PmgBatCouponController`：优惠券管理接口

## 3. 主要Service接口与实现

- `PmgSysParamService`：系统参数管理
- `PmgSysDictService`：数据字典管理
- `PmgSysTenantService`：租户管理
- `PmgSysUserService`：用户管理
- `PmgSysRoleService`：角色管理
- `PmgSysMenuService`：菜单管理
- `PmgSysUserRoleService`、`PmgSysRoleMenuService`：用户-角色、角色-菜单管理
- `PmgBatPassRuleService`：通行规则管理
- `PmgBatChargeRuleService`：计费规则管理
- `PmgBatFreePassRuleService`：免费放行规则管理
- `PmgBatHolidayService`：节假日规则管理
- `PmgBatTestModeService`：测试模式管理
- `PmgSysMessageConfigService`：消息推送、通知配置
- `PmgBatCouponService`：优惠券管理

## 4. 典型方法调用链与数据流转

### 4.1 系统参数、数据字典、租户管理

- 通过`PmgSysParamController`、`PmgSysDictController`、`PmgSysTenantController`进行参数、字典、租户的增删改查
- 参数、字典、租户信息存储于对应表，供全局配置与业务流程动态读取

### 4.2 用户权限、角色、菜单管理

- 用户、角色、菜单通过各自Controller和Service管理
- 用户-角色、角色-菜单通过关联表维护
- 登录、鉴权、菜单权限、数据权限等流程动态校验

### 4.3 通行规则、计费规则、免费放行、节假日、测试模式等业务规则配置

- 通过`PmgBatPassRuleController`、`PmgBatChargeRuleController`、`PmgBatFreePassRuleController`、`PmgBatHolidayController`、`PmgBatTestModeController`配置各类业务规则
- 规则存储于对应表，出入场、计费、放行等流程动态读取

### 4.4 消息推送、短信、微信、钉钉等通知配置

- 通过`PmgSysMessageConfigController`配置消息推送、短信、微信、钉钉等通知渠道
- 业务事件触发时，`PmgSysMessageConfigService`根据配置推送消息
- 支持多渠道、模板、定时、批量推送

### 4.5 优惠券管理（创建、发放、规则、记录）

- 通过`PmgBatCouponController`创建、发放优惠券，配置使用规则
- 优惠券发放、使用、核销等记录于`pmg_bat_coupon_record`
- 支持多种类型、规则、批量发放、统计分析

## 5. 关键业务流程图示

### 5.1 权限与菜单管理

```mermaid
graph TD
A[用户登录] --> B[鉴权]
B --> C[获取角色]
C --> D[加载菜单权限]
D --> E[数据权限校验]
```

### 5.2 业务规则配置与应用

```mermaid
graph TD
A[配置通行/计费/放行/节假日/测试规则] --> B[写入对应规则表]
B --> C[出入场/计费/放行等流程动态读取]
```

### 5.3 消息推送与通知

```mermaid
graph TD
A[业务事件触发] --> B[PmgSysMessageConfigService.send]
B --> C[推送到短信/微信/钉钉等渠道]
```

### 5.4 优惠券管理

```mermaid
graph TD
A[创建优惠券] --> B[配置规则]
B --> C[发放优惠券]
C --> D[用户领取/使用]
D --> E[记录核销/统计分析]
```

## 6. 其他说明

- 支持多租户、多角色、多规则、多渠道的灵活配置
- 权限、规则、通知、优惠券等流程均有详细状态流转与异常处理
- 可与各业务模块深度集成，支持批量、自动化、扩展

---
如需对某一具体方法、类或流程进一步深入源码解读，请进一步指定需求！ 