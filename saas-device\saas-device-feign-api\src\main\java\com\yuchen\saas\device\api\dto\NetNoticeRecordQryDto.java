package com.yuchen.saas.device.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;

@Data
public class NetNoticeRecordQryDto extends Query {

    /**
     * 所属客户id
     */
    private Long customerManageId;

    /**
     * 创建开始日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startCreateTime;
    /**
     * 创建结束日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCreateTime;

    /**
     * 状态 1-发送成功，0-发送失败
     */
    private Integer status;

}
