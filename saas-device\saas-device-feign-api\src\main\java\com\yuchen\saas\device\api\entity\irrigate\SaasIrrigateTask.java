package com.yuchen.saas.device.api.entity.irrigate;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * 浇灌定时任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_irrigate_task")
@ApiModel(value="SaasIrrigateTask对象", description="浇灌定时任务")
public class SaasIrrigateTask extends BaseEntity {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 任务单号
     */
    @ApiModelProperty(name = "taskNo", value = "任务单号")
    private String taskNo;
    /**
     * 关联自动浇灌ID
     */
    @ApiModelProperty(name = "irrigateAutoId", value = "关联自动浇灌ID")
    private Long irrigateAutoId;
    /**
     * 任务名称
     */
    @ApiModelProperty(name = "taskName", value = "任务名称")
    private String taskName;
    /**
     * 浇灌日期
     */
    @ApiModelProperty(name = "irrigateDate", value = "浇灌日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date irrigateDate;
    /**
     * 浇灌开始时间
     */
    @ApiModelProperty(name = "irrigateStartTime", value = "浇灌开始时间")
    private Date irrigateStartTime;
    /**
     * 浇灌结束时间
     */
    @ApiModelProperty(name = "irrigateEndTime", value = "浇灌结束时间")
    private Date irrigateEndTime;
    /**
     * 浇灌区域ID
     */
    @ApiModelProperty(name = "irrigateAreaId", value = "浇灌区域ID")
    private Long irrigateAreaId;
    /**
     * 浇灌点位ID
     */
    @ApiModelProperty(name = "irrigatePointId", value = "浇灌点位ID")
    private Long irrigatePointId;
    /**
     * 任务状态(1:待开始 2:处理中 3:已完成 4:已关闭)
     */
    @ApiModelProperty(name = "taskStatus", value = "任务状态(1:待开始 2:处理中 3:已完成 4:已关闭)")
    private Integer taskStatus;
    /**
     * 关闭原因(1:天气原因 2:养护调整 3:其他)
     */
    @ApiModelProperty(name = "closeReason", value = "关闭原因(1:天气原因 2:养护调整 3:其他)")
    private Integer closeReason;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

}































































































































































































































































































































































































































































































































































































































































































































































































































































































































































