package com.yuchen.saas.consumer.service.service.wechatTemp.impl;

import com.yuchen.saas.consumer.api.dto.wechat.SendTemplateReqDTO;
import com.yuchen.saas.consumer.service.factory.WeChatTempReportFactory;
import com.yuchen.saas.consumer.service.service.wechatTemp.WeChatOfficialPushTempService;
import com.yuchen.saas.consumer.service.service.wechatTemp.WechatPushTempReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/5 15:31
 */

@Slf4j
@Service
public class WeChatOfficialPushTempServiceImpl implements WeChatOfficialPushTempService {

    @Override
    public Boolean sendOfficialTempMessage(List<SendTemplateReqDTO> reqDTOList) {
        for (SendTemplateReqDTO reqDTO : reqDTOList) {
            WechatPushTempReportService tempReportService = WeChatTempReportFactory.getWechatPushTempReportService(reqDTO.getOfficialAppId());
            tempReportService.sendWechatTempMessage(reqDTO);
        }

        return Boolean.TRUE;
    }
}
