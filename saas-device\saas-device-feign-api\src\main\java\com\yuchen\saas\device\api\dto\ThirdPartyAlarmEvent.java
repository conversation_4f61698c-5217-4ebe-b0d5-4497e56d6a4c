package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/30 16:03
 * @Description TODO
 */
@Data
public class ThirdPartyAlarmEvent implements Serializable {
    /**
     * 第三方平台id
     */
    @ApiModelProperty(name = "thirdPartyId", value = "第三方平台id")
    private Long thirdPartyId;
    /**
     * 时间
     */
    @ApiModelProperty(name = "eventTime", value = "时间")
    private Long eventTime;
    /**
     * 告警内容
     */
    @ApiModelProperty(name = "alarmContent", value = "告警内容")
    private String alarmContent;
    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerManageId", value = "所属客户")
    private Long customerManageId;

    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 告警名称
     */
    @ApiModelProperty(name = "alarmName", value = "告警名称")
    private String alarmName;
}
