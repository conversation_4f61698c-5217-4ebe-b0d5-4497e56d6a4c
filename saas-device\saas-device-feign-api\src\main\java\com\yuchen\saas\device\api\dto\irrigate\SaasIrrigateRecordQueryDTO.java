package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;

@Data
public class SaasIrrigateRecordQueryDTO extends Query {


    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 浇灌类型（1：自动浇灌，2：定时浇灌，3：手动浇灌，4：临时浇灌）
     */
    @ApiModelProperty(name = "irrigateType", value = "浇灌类型（1：自动浇灌，2：定时浇灌，3：手动浇灌，4：临时浇灌）")
    private String irrigateType;
    /**
     * 浇灌设备ID
     */
    @ApiModelProperty(name = "irrigateDeviceId", value = "浇灌设备ID")
    private Long irrigateDeviceId;
    /**
     * 自动浇灌ID
     */
    @ApiModelProperty(name = "irrigateAutoId", value = "自动浇灌ID")
    private Long irrigateAutoId;
    /**
     * 浇灌区域ID
     */
    @ApiModelProperty(name = "irrigateAreaId", value = "浇灌区域ID")
    private String irrigateAreaId;
    /**
     * 浇灌开始时间
     */
    @ApiModelProperty(name = "irrigateStartTime", value = "浇灌开始时间")
    private Date irrigateStartTime;
    /**
     * 浇灌结束时间
     */
    @ApiModelProperty(name = "irrigateEndTime", value = "浇灌结束时间")
    private Date irrigateEndTime;



}
