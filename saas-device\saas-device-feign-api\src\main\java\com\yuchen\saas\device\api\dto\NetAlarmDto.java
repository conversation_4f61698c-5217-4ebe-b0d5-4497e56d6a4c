package com.yuchen.saas.device.api.dto;

import com.yuchen.saas.device.api.entity.NetAlarm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class NetAlarmDto extends NetAlarm {

    /**
     * 触发条件列表
     */
    @ApiModelProperty(name = "triggerConditionList", value = "触发条件列表")
    private List<TriggerCondition> triggerConditionList= new ArrayList<>();

    /**
     * 执行动作列表
     */
    @ApiModelProperty(name = "executeActionList", value = "执行动作列表")
    private List<ExecuteAction> executeActionList=new ArrayList<>();


    @Data
    public static class  TriggerCondition {
        /**
         * 触发条件项
         */
        @ApiModelProperty(name = "itemTriggerConditionList", value = "触发条件项")
        private List<ItemTriggerCondition> itemTriggerConditionList;

        @Data
        public static class  ItemTriggerCondition {
            /**
             * 参数名
             */
            @ApiModelProperty(name = "paramName", value = "参数名")
            private String paramName;
            /**
             * NetDataTypeEnum
             * 参数类型
             * 1：int(整数型)
             * 2：long(长整数型)
             * 3：float(单精度浮点型)
             * 4：double(双精度浮点型)
             * 5：text(字符串)
             * 6：bool(布尔型)
             * 7：time(时间类型)
             */
            @ApiModelProperty(name = "propMode", value = "1：int(整数型)2：long(长整数型)3：float(单精度浮点型)4：double(双精度浮点型)5：text(字符串)6：bool(布尔型) 7：time(时间类型)")
            private int propMode;
            /**
             * NetCompareTypeEnum
             * 比较符
             * 1：等于
             * 2：不等于
             * 3：大于
             * 4：大于等于
             * 5：小于
             * 6：小于等于
             */
            @ApiModelProperty(name = "compareType", value = "比较符:1：等于 2：不等于 3：大于 4：大于等于 5：小于 6：小于等于 7 之间 8 之外")
            private int compareType;
            /**
             * 条件值
             */
            @ApiModelProperty(name = "conditionValue", value = "条件值")
            private String conditionValue;

            @ApiModelProperty(name = "maxValue", value = "最大值")
            private String maxValue;

            @ApiModelProperty(name = "minValue", value = "最小值")
            private String minValue;
        }


    }


    /**
     * 执行动作项
     */
    @Data
    public static class ExecuteAction {
        /**
         * 通知分类；1-站外通知，2-站内消息，3-场景联动
         */
        @ApiModelProperty(name = "noticeCategory", value = "通知分类；1-站外通知，2-站内消息，3-场景联动")
        private int noticeCategory;

        @ApiModelProperty(name = "messageType", value = "站内 消息类型；0 弹窗 1 消息  -站外为 1")
        private int messageType;


        @ApiModelProperty(name = "noticeUserIds", value = "警告接收人员，多个用逗号隔开")
        private String noticeUserIds;
        /**
         * 通知配置id 站外类型通知配置
         */
        private Long noticeConfigId;
        private String noticeConfigName;
        /**
         * 通知模板id
         */
        private Long noticeTemplateId;
        private String noticeTemplateName;
        /**
         * 场景联动id
         */
        private Long sceneLinkageId;
        private String sceneLinkageName;
        /**
         * 消息发送类型
         */
        @ApiModelProperty(name = "noticeType", value = "消息发送类型 net_notice_method-node-key")
        private Integer noticeType;
    }

    private List<String> deviceNames;


}
