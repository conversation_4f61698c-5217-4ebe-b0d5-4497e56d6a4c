# 特殊车辆与分组管理源码级分析

## 1. 主要涉及的实体类

- **PmgBatCarGroup**：车辆分组表，记录分组名称、规则、类型等。
- **PmgBatCarGroupMember**：分组成员表，记录车辆与分组的关联。
- **PmgBatCarVip**：VIP车辆表。
- **PmgBatCarBlacklist**：黑名单车辆表。
- **PmgBatCarInternal**：内部车辆表。
- **PmgBatCarFleet**：车队信息表。
- **PmgBatCarFleetMember**：车队成员表。
- **PmgBatCarFleetRule**：车队通行、计费规则表。
- **PmgBatCarTempPass**：临时车辆放行申请、审核、记录表。

## 2. 主要Controller

- `PmgBatCarGroupController`：车辆分组创建、规则、成员管理接口
- `PmgBatCarVipController`：VIP车辆管理接口
- `PmgBatCarBlacklistController`：黑名单车辆管理接口
- `PmgBatCarInternalController`：内部车辆管理接口
- `PmgBatCarFleetController`：车队信息、成员、规则管理接口
- `PmgBatCarTempPassController`：临时车辆放行申请、审核、记录接口

## 3. 主要Service接口与实现

- `PmgBatCarGroupService`：车辆分组管理
- `PmgBatCarGroupMemberService`：分组成员管理
- `PmgBatCarVipService`：VIP车辆管理
- `PmgBatCarBlacklistService`：黑名单车辆管理
- `PmgBatCarInternalService`：内部车辆管理
- `PmgBatCarFleetService`：车队信息、成员、规则管理
- `PmgBatCarTempPassService`：临时车辆放行管理

## 4. 典型方法调用链与数据流转

### 4.1 车辆分组（创建、规则、成员管理）

- 通过`PmgBatCarGroupController`创建分组，配置规则（如通行、计费、优先级等）
- 通过`PmgBatCarGroupMemberController`添加/移除成员，成员信息存储于`pmg_bat_car_group_member`
- 分组规则动态应用于车辆出入场、计费等流程

### 4.2 VIP、黑名单、内部车辆管理

- 通过各自Controller进行车辆的添加、移除、查询
- VIP、黑名单、内部车辆信息分别存储于对应表
- 出入场、计费、权限校验时优先匹配特殊车辆规则

### 4.3 车队管理（信息、车辆、通行规则、计费规则）

- 通过`PmgBatCarFleetController`创建车队，维护成员、配置通行与计费规则
- 车队成员信息存储于`pmg_bat_car_fleet_member`，规则存储于`pmg_bat_car_fleet_rule`
- 车队车辆出入场、计费时按车队规则处理

### 4.4 临时车辆放行（申请、审核、记录）

- 通过`PmgBatCarTempPassController`提交放行申请，写入`pmg_bat_car_temp_pass`
- 管理员审核通过后，生成放行记录，允许临时车辆通行
- 放行记录可追溯，支持统计与查询

## 5. 关键业务流程图示

### 5.1 车辆分组与成员管理

```mermaid
graph TD
A[创建车辆分组] --> B[PmgBatCarGroupController.create]
B --> C[配置分组规则]
A2[添加成员] --> D[PmgBatCarGroupMemberController.add]
D --> E[写入pmg_bat_car_group_member]
```

### 5.2 VIP/黑名单/内部车辆管理

```mermaid
graph TD
A[添加特殊车辆] --> B[PmgBatCarVipController.add/PmgBatCarBlacklistController.add/PmgBatCarInternalController.add]
B --> C[写入对应表]
C --> D[出入场/计费优先匹配特殊规则]
```

### 5.3 车队管理

```mermaid
graph TD
A[创建车队] --> B[PmgBatCarFleetController.create]
B --> C[添加成员]
C --> D[配置通行/计费规则]
D --> E[车队车辆出入场/计费按规则处理]
```

### 5.4 临时车辆放行

```mermaid
graph TD
A[提交放行申请] --> B[PmgBatCarTempPassController.apply]
B --> C[管理员审核]
C -- 通过 --> D[生成放行记录]
D --> E[允许临时车辆通行]
```

## 6. 其他说明

- 支持多种特殊车辆类型与分组，规则灵活可扩展
- 分组、车队、临时放行等流程均有详细状态流转与异常处理
- 可与出入场、计费、权限等核心流程深度集成

---
如需对某一具体方法、类或流程进一步深入源码解读，请进一步指定需求！ 