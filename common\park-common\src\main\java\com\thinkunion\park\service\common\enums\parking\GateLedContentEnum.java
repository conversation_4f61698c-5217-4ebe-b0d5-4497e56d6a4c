package com.thinkunion.park.service.common.enums.parking;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/26 11:20
 */
public enum GateLedContentEnum {
    ENTER_WELCOME("欢迎光临"),
    /**
     * 无车牌禁止自动放行
     */
    ANONYMOUS_PLATE_NO_ENTER_FORBID_CONTENT("无车牌禁止自动放行"),
    ANONYMOUS_PLATE_NO_EXIT_FORBID_CONTENT("无车牌请在公众号出园放行"),
    TEMPORARY_CAR_PASS_ALLOWED("临停放行"),
    INSIDE_CAR_PASS_ALLOWED("内部车放行"),
    PARKING_IS_FULL("车位已满"),
    GROUP_OUT_TIME("群组不在规定的放行时段"),
    UNLICENSED_CARS("无牌车"),
    GROUP_CAR_PASS_FORBID("群组设置禁止放行"),
    GROUP_CAR_NEED_TO_PAY_YUAN("群组需缴费%s元"),
    GROUP_CAR_PAID_RELEASE("群组车已缴费放行"),
    NO_PLATE_NOT_PASS("无车牌禁止自动放行"),
    LEFT_SPACE("余位%s"),

    TEAM_PASS_ALLOWED("团队车放行"),

    SHARE_PLACE_PASS_ALLOWED("AB车位放行"),
    VISITOR_CAR_PASS_ALLOWED("访客车放行"),
    TEAM_PLACE_PASS_ALLOWD("团队车位放行"),

    PLACE_IDLE_COST_LED("需缴占位费%s元"),


    ;
    private String value;

    GateLedContentEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
