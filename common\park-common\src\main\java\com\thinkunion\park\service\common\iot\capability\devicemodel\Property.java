package com.thinkunion.park.service.common.iot.capability.devicemodel;

public class Property {
    private String name;
    private String id;
    private String mode;
    private boolean optional;
    private String desc;
    private boolean required;
    private DataType data;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMode() {
        return this.mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    @Deprecated
    public boolean isOptional() {
        return this.optional;
    }

    @Deprecated
    public void setOptional(boolean optional) {
        this.optional = optional;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean isRequired() {
        return this.required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public DataType getData() {
        return this.data;
    }

    public void setData(DataType data) {
        this.data = data;
    }
}

