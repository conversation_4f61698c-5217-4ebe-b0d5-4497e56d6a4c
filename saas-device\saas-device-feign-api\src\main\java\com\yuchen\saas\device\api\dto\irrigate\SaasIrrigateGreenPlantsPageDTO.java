package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/30 15:53
 * @Description
 */
@Data
@ApiModel(value = "GreenPlantsPageDTO")
public class SaasIrrigateGreenPlantsPageDTO extends Query {

    @ApiModelProperty(value = "项目id（园区id）")
    @NotNull(message = "项目id不能为空")
    private Long parkId;

    @ApiModelProperty(value = "绿植名称")
    private String greenPlantsName;



}
