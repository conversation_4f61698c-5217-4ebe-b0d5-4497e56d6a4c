package com.yuchen.saas.device.service.listener;

import com.yuchen.saas.device.service.event.ReceiveSceneLinkageDataEvent;
import com.yuchen.saas.device.service.service.alarm.NetAlarmRunRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/9/23 17:38
 * @Description TODO
 */
@Slf4j
@Service
public class ReceiveSceneLinkageDataEventListener {
    @Resource
    private NetAlarmRunRuleService netAlarmRunRuleService;
    @EventListener
    public void alarmRunRule(ReceiveSceneLinkageDataEvent event) {
        netAlarmRunRuleService.receiveSceneLinkageData(event.getSceneLinkageEvent());
    }
}
