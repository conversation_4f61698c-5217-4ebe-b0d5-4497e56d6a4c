package com.yuchen.saas.consumer.service.service;

import com.thinkunion.park.service.common.entity.PassTicket;
import com.yuchen.saas.consumer.service.entity.PmgBatParkNoPlateInfo;
import com.yuchen.saas.parking.api.entity.PmgLogParkingCar;
import org.nest.springwrap.core.tool.api.R;

/**
 * @Author: 张逸飞
 * @Date: 2021/3/13 19:10
 * @Description: 停车场公众号相关操作接口
 */
public interface ParkingService {

    /**
     * 无牌车出入园
     * @param eventType 进出类型
     * @param parkNoPlateInfo 无牌车信息
     * @param pmgLogParkingCar 无牌车压车数据信息
     * @return
     */
    R confirmLane(String eventType, PmgBatParkNoPlateInfo parkNoPlateInfo, PmgLogParkingCar pmgLogParkingCar,
                  PassTicket passTicket);
}
