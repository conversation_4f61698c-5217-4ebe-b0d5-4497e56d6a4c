package com.yuchen.saas.consumer.api.dto.wechat;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;
/**
 * <AUTHOR>
 * @create 2024/1/2 15:03
 */
@Data
public class SendTemplateReqDTO {
    /**
     * 发送的公众号id
     */
    private String officialAppId;
    /**
     * 接收者（用户）的 openid
     */
    private String touser;
    /**
     * 所需下发的模板消息的id
     */
    private String template_id;

    /**
     * 所需下发的模板消息的标识
     */
    private String templateCode;
    /**
     * 模板跳转链接（海外账号没有跳转能力）
     */
    private String url;
    /**
     * 跳小程序所需数据，不需跳小程序可不用传该数据
     * "miniprogram":{
     *              "appid":"xiaochengxuappid12345",
     *              "pagepath":"index?foo=bar"
     *            }
     * appid	必填	所需跳转到的小程序appid（该小程序appid必须与发模板消息的公众号是绑定关联关系，暂不支持小游戏）
     * pagepath	非必填	所需跳转到小程序的具体页面路径，支持带参数,（示例index?foo=bar），要求该小程序已发布，暂不支持小游戏
     */
    private JSONObject miniprogram;
    /**
     * 模板数据
     */
    private JSONObject data;

    /**
     * 防重入id。对于同一个openid + client_msg_id, 只发送一条消息,10分钟有效,超过10分钟不保证效果。若无防重入需求，可不填
     */
    private String client_msg_id;
}

