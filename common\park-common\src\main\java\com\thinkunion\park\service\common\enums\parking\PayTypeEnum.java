package com.thinkunion.park.service.common.enums.parking;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/27 19:20
 */
public enum PayTypeEnum {
    WECHAT("微信支付","WHCHAT_JSAPI"),ALI("支付宝支付","ali_pay"),UNKNOW("支付类型未知","pay type unknow");

    private String name;
    private String value;

    PayTypeEnum(String name, String value){
        this.value = value;
        this.name = name;
    }
    public String getValue() {
        return value;
    }
    public String getName(){
        return name;
    }

}
