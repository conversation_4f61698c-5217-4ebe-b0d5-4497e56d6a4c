package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@TableName("net_alarm_device")
public class NetAlarmDevice {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 告警id
     */
    @NotNull(message = "告警id不能为空")
    private Long alarmId;

    /**
     * 客户设备id
     */
    @NotNull(message = "客户设备id不能为空")
    private Long clientDeviceId;

}
