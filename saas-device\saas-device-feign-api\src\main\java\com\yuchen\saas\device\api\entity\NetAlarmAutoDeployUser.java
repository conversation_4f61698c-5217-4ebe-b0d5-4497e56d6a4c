package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.tenant.mp.TenantEntity;

/**
 * <p>
 * 告警自动派遣工单用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_alarm_auto_deploy_user")
@ApiModel(value="NetAlarmAutoDeployUser对象", description="告警自动派遣工单用户表")
public class NetAlarmAutoDeployUser extends TenantEntity {
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;

    /**
     * 告警规则ids
     */
    @ApiModelProperty(name = "alarmIds", value = "告警规则ids")
    private String alarmIds;
    /**
     * 自动派工用户ids
     */
    @ApiModelProperty(name = "userIds", value = "自动派工用户ids")
    private String userIds;

    /**
     * 工单时限ID
     */
    @ApiModelProperty(name = "timeLimitId", value = "工单时限ID")
    private Long timeLimitId;
    /**
     * 工单处理时限(单位:小时)
     */
    @ApiModelProperty(name = "timeLimitCount", value = "工单处理时限(单位:小时)")
    private Integer timeLimitCount;



}
