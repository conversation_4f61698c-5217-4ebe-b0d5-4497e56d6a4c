package com.yuchen.saas.device.api.dto.assets;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @create 2024/2/27 16:48
 */
@Data
public class AssetsDataExportReqDTO extends AssetsDataPageReqDTO {

    /**
     * 导出类型（1：导出模板，2：导出内容）
     */
    @Max(value = 2, message = "导出类型只能为1和2，1=导出导入模板，2=导出数据")
    @Min(value = 1, message = "导出类型只能为1和2，1=导出导入模板，2=导出数据")
    private Integer exportType;

}
