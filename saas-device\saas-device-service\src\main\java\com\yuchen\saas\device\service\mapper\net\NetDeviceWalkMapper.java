package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.entity.NetDeviceWalk;
import com.yuchen.saas.device.api.vo.NetDeviceWalkPageVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface NetDeviceWalkMapper extends BaseMapper<NetDeviceWalk> {

    @Select("SELECT dw.*,p.product_name,od.is_online,od.last_connect_time,od.last_disconnect_time,pi.park_name,cm.name as customer_manage_name," +
            " od.file_path,nd.dict_value as device_type_name from net_device_walk dw" +
            " LEFT JOIN net_operate_device od on dw.device_name = od.device_name" +
            " LEFT JOIN net_product p on dw.product_key = p.product_key" +
            " LEFT JOIN pmg_bat_park_info pi on dw.park_id = pi.id" +
            " LEFT JOIN nest_customer_manage cm on dw.customer_manage_id = cm.id" +
            " LEFT JOIN nest_dict nd on dw.device_type = nd.dict_key and nd.`code`='net_device_type' and nd.is_deleted=0 and nd.parent_id <> 0" +
            " ${ew.customSqlSegment}")
    Page<NetDeviceWalkPageVo> selectPageDeviceWalk(Page<NetDeviceWalkPageVo> page, @Param(Constants.WRAPPER) QueryWrapper<String> wrapper);

}
