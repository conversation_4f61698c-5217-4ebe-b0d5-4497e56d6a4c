package com.yuchen.saas.consumer.service.util;

import com.thinkunion.park.service.common.iot.exception.DataErrorException;
import org.apache.commons.lang3.StringUtils;
import org.nest.springwrap.core.log.exception.ServiceException;
import org.nest.springwrap.core.tool.api.IResultCode;
import org.nest.springwrap.core.tool.api.R;
import org.nest.springwrap.core.tool.api.ResultCode;

/**
 * <AUTHOR>
 * @date 2023/03/14
 **/
public class ValidUtils {

    public static<T> void verifyResult(R<T> result) {
        if (ResultCode.SUCCESS.getCode() != result.getCode()) {
            throw new DataErrorException(String.valueOf(result.getCode()), result.getMessage());
        }
    }

    public static void validString(String... params) {
        for (String each : params) {
            if (StringUtils.isEmpty(each)) {
                throw new DataErrorException(ResultCode.PARAM_VALID_ERROR.getMessage());
            }
        }
    }

    public static void validString(IResultCode errorCode, String... params) {
        for (String each : params) {
            if (StringUtils.isEmpty(each)) {
                throw new DataErrorException(String.valueOf(errorCode.getCode()));
            }
        }
    }

    public static void validString(int code, String msg, String... params) {
        for (String each : params) {
            if (StringUtils.isEmpty(each)) {
                throw new DataErrorException(String.valueOf(code), msg);
            }
        }
    }

    public static void validFrameNo(String frameNo) {
        if (frameNo.length() != 17) {
            throw new ServiceException("请输入17位车架号");
        }
    }
}
