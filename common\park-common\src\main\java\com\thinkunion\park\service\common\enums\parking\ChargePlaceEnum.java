package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * cee8a0c0-38d8-11ed-b75c-2b79dedf07db  4号车位
 * cee8a08e-38d8-11ed-b75b-ef9375302500  3号车位
 * cee8a020-38d8-11ed-b75a-173468cba460  2号车位
 * cee89e72-38d8-11ed-b759-63a2426b2294  1号车位
 * <AUTHOR>
 */
@AllArgsConstructor
public enum ChargePlaceEnum {

    //地库异常出场,地面正常结算时地库账单记录这个编码
    ONE("cee89e72-38d8-11ed-b759-63a2426b2294", "1号车位"),
    //地库异常出场,地面正常结算时地面账单记录这个编码
    TWO("cee8a020-38d8-11ed-b75a-173468cba460", "2号车位"),
    THREE("cee8a08e-38d8-11ed-b75b-ef9375302500", "3号车位"),
    FOUR("cee8a0c0-38d8-11ed-b75c-2b79dedf07db", "4号车位"),

   ;

    private String code;

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(String code) {
        ChargePlaceEnum[] enumArray = ChargePlaceEnum.values();
        for (ChargePlaceEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return code;
    }
}
