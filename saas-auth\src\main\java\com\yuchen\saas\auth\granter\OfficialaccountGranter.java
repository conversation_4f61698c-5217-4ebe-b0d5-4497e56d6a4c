package com.yuchen.saas.auth.granter;

import cn.hutool.core.lang.Validator;
import com.yuchen.saas.manage.user.api.entity.SaasWechatUser;
import com.yuchen.saas.manage.user.api.entity.User;
import com.yuchen.saas.manage.user.api.entity.UserDetail;
import com.yuchen.saas.manage.user.api.entity.UserInfo;
import com.yuchen.saas.manage.user.api.enums.UserEnum;
import com.yuchen.saas.manage.user.api.feign.IUserClient;
import org.apache.commons.lang.StringUtils;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.nest.springwrap.core.tool.utils.Func;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import java.util.LinkedHashMap;
import java.util.Map;

public class OfficialaccountGranter extends AbstractTokenGranter {
    private static final String WECHAT_GRANT_TYPE = "officialAccount";

    private UserDetailsService userDetailsService;

    private NestRedis nestRedis;

    private IUserClient userClient;




    public OfficialaccountGranter(AuthorizationServerTokenServices tokenServices,
                                  ClientDetailsService clientDetailsService,
                                  OAuth2RequestFactory requestFactory,
                                  UserDetailsService userDetailsService,
                                  NestRedis nestRedis,
                                  IUserClient userClient) {
        super(tokenServices, clientDetailsService, requestFactory, WECHAT_GRANT_TYPE);
        this.userDetailsService = userDetailsService;
        this.nestRedis = nestRedis;
        this.userClient = userClient;
    }


    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client,
                                                           TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<String, String>(tokenRequest.getRequestParameters());

        // 客户端提交的手机号码
        String phoneNumber = parameters.get("phone");
        if (StringUtils.isBlank(phoneNumber)) {
            throw new UserDeniedAuthorizationException("电话号码是空的！");
        }
        //验证手机号格式
        if (!Validator.isMobile(phoneNumber)) {
            throw new UserDeniedAuthorizationException("请输入正确的手机号码");
        }
        String openId = parameters.get("openId");
  /*      //验证手机号格式
        if (StringUtils.isBlank(openId)) {
            throw new UserDeniedAuthorizationException("请微信授权登录");
        }*/
        String appId = parameters.get("appId");

        String tenantId = parameters.get("tenantId");
        Integer applySource = Integer.valueOf(parameters.get("applySource"));
          logger.info("applySource====="+applySource);
        Long orgUserId=null;
        if(Func.isNotEmpty(parameters.get("orgUserId"))){
            orgUserId = Long.valueOf(parameters.get("orgUserId"));
        }

        UserDetail userDetail=userClient.userDetailByPhone(phoneNumber).getData();

        if(userDetail.getId()==null){
            UserDetail userDetail2=  userClient.submitUserDetail(phoneNumber,"").getData();
            User user=  userClient.submitUser(phoneNumber,"",tenantId,userDetail2.getId(),applySource).getData();
            saveWeChatUser(openId, appId, user.getId(),phoneNumber);
        }else {
            UserInfo account = userClient.userInfoByPhone(tenantId,phoneNumber,"","","", UserEnum.WEB.getName(),applySource,orgUserId).getData();
            if(account.getId()==null){
                User user=  userClient.submitUser(phoneNumber,"",tenantId,userDetail.getId(), applySource).getData();
                saveWeChatUser(openId, appId, user.getId(),phoneNumber);
            }else{
                UserInfo wxaccount = userClient.userInfoByPhone(tenantId,phoneNumber,"",openId,appId, UserEnum.WECHAT.getName(),applySource,orgUserId).getData();
                if(wxaccount.getId()==null){
                    saveWeChatUser(openId, appId, account.getId(),phoneNumber);
                }{
                    //更新小程序用户昵称
                }
            }
        }


        UserDetails user = userDetailsService.loadUserByUsername(phoneNumber);
        AbstractAuthenticationToken userAuth = new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());
        userAuth.setDetails(parameters);
        OAuth2Request oAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(oAuth2Request, userAuth);
    }

    private void saveWeChatUser(String openId, String appId, Long id,String phoneNumber) {
        SaasWechatUser saasWechatUser = new SaasWechatUser();
        saasWechatUser.setAppid(appId);
        saasWechatUser.setOpenid(openId);
        saasWechatUser.setUserId(id);
        saasWechatUser.setWechatName(phoneNumber);
        saasWechatUser.setWechatPic("");
        userClient.submitWeChatUser(saasWechatUser);
    }
}
