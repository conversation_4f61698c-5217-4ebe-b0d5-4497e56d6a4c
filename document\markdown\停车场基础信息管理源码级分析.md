# 停车场基础信息管理源码级分析

## 1. 主要涉及的实体类

- **PmgBatParkParking**：停车场信息表，包含园区ID、名称、地址、车位数、运营状态等字段。
- **PmgBatParkParkingEntrance**：停车场出入口表，包含所属停车场ID、出入口名称、类型等。
- **PmgBatParkParkingRoadway**：停车场车道表，包含所属出入口ID、车道名称、类型、设备绑定等。

## 2. 主要Controller

- `ParkingController`（路径：/parking/PmgBatParkParking）
  - 负责停车场、出入口、车道的增删改查、树结构、状态变更、设备绑定等接口。

## 3. 主要Service接口与实现

- `ParkingService`（接口）
  - `saveParking(PmgBatParkParking)` 新增停车场
  - `updateParking(PmgBatParkParking)` 修改停车场
  - `delParking(Long id)` 删除停车场
  - `getParkingPage(ParkingPageDTO)` 分页查询
  - `getParkingTree()` 获取树结构
  - `saveEntrance(PmgBatParkParkingEntrance)` 新增出入口
  - `updateEntrance(Long id, String name)` 修改出入口
  - `delEntrance(Long id)` 删除出入口
  - `getEntranceList(Long parkingId)` 查询出入口列表
  - `saveRoadway(PmgBatParkParkingRoadway)` 新增车道
  - `updateRoadway(PmgBatParkParkingRoadway)` 修改车道
  - `delRoadway(Long id)` 删除车道
  - `getRoadwayList(...)` 查询车道列表
  - `bundingDevice(PmgBatParkParkingConnectDevice)` 设备绑定
  - `unboundDevice(String ids)` 设备解绑
  - `updateStatus(ParkingInfoStatusUpdateDTO)` 运营状态变更
  - `updateCharging(ParkingInfoChargingUpdateDTO)` 收费状态变更

- `ParkingServiceImpl`（实现类）
  - 负责具体的业务逻辑实现，包括参数校验、数据库操作、级联删除、缓存维护等。

## 4. 典型方法调用链与数据流转

### 4.1 新增停车场

1. 前端请求 POST `/parking/PmgBatParkParking/saveParkParking`，传入`PmgBatParkParking`对象。
2. `ParkingController.saveParkParking`接收请求，调用`ParkingService.saveParking`。
3. `ParkingServiceImpl.saveParking`：
   - 校验参数（如名称唯一性、必填项等）
   - 设置默认值（如运营状态、menuType等）
   - 插入数据库（`pmg_bat_park_parking`表）
   - 初始化统计信息（如车位数）
   - 返回操作结果

### 4.2 删除停车场

1. 前端请求 DELETE `/parking/PmgBatParkParking/delete?id=xxx`。
2. `ParkingController.delete`调用`ParkingService.delParking`。
3. `ParkingServiceImpl.delParking`：
   - 检查停车场是否存在
   - 级联删除出入口、车道（调用`deleteByParkingId`等）
   - 删除停车场记录
   - 清理相关缓存

### 4.3 查询停车场树结构

1. 前端请求 GET `/parking/PmgBatParkParking/getParkingTree`。
2. `ParkingController.getParkingTree`调用`ParkingService.getParkingTree`。
3. `ParkingServiceImpl.getParkingTree`：
   - 查询所有停车场、出入口、车道
   - 组装为树形结构（停车场-出入口-车道）
   - 返回树结构数据

### 4.4 设备绑定/解绑

- 设备绑定：PUT `/parking/PmgBatParkParking/bundingDevice`，传入`PmgBatParkParkingConnectDevice`对象
- 设备解绑：DELETE `/parking/PmgBatParkParking/unboundDevice?ids=...`
- Service实现：校验设备、保存/删除绑定关系、更新相关表

## 5. 关键业务流程图示

### 5.1 新增停车场流程

```mermaid
graph TD
A[前端发起新增停车场请求] --> B[ParkingController.saveParkParking]
B --> C[ParkingService.saveParking]
C --> D[参数校验/设置默认值]
D --> E[插入数据库]
E --> F[初始化统计信息]
F --> G[返回操作结果]
```

### 5.2 删除停车场流程

```mermaid
graph TD
A[前端发起删除停车场请求] --> B[ParkingController.delete]
B --> C[ParkingService.delParking]
C --> D[检查存在性]
D --> E[级联删除出入口/车道]
E --> F[删除停车场记录]
F --> G[清理缓存]
G --> H[返回操作结果]
```

### 5.3 查询树结构流程

```mermaid
graph TD
A[前端请求树结构] --> B[ParkingController.getParkingTree]
B --> C[ParkingService.getParkingTree]
C --> D[查询所有数据]
D --> E[组装树结构]
E --> F[返回树结构数据]
```

## 6. 其他说明

- 所有接口均有详细的参数校验和异常处理，保证数据一致性和业务安全。
- 相关实体、DTO、VO、Mapper等均在`saas-parking-api`和`saas-parking-service`下有详细定义。
- 业务扩展性强，支持多园区、多层级、多类型停车场的灵活管理。

---
如需对某一具体方法或流程进行更细致的源码解读，请进一步指定需求！ 