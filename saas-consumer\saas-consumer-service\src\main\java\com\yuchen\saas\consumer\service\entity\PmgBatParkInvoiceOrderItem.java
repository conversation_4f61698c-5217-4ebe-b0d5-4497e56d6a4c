package com.yuchen.saas.consumer.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年3月17日 16:28:59
 * 发票订单子表
 */
@Data
@TableName("pmg_bat_park_invoice_order_item")
@ApiModel(value = "发票订单子表", description = "发票订单子表")
public class PmgBatParkInvoiceOrderItem extends TenantEntity {

    /**
     * 开票主订单编号
     */
    @ApiModelProperty(name = "orderNo", value = "开票主订单编号")
    private String orderNo;
    /**
     * 	支付订单唯一标识
     */
    @ApiModelProperty(name = "payOrderNumber", value = "支付订单唯一标识")
    private String payOrderNumber;
    /**
     * 商品名称
     */
    @ApiModelProperty(name = "name", value = "商品名称")
    private String name;
    /**
     * 税率 | 只能为0、0.03、0.04、0.06、0.10或0.16
     */
    @ApiModelProperty(name = "taxRate", value = "税率 | 只能为0、0.03、0.04、0.06、0.10或0.16")
    private BigDecimal taxRate;
    /**
     * 税价合计金额
     */
    @ApiModelProperty(name = "amount", value = "税价合计金额")
    private BigDecimal amount;


    /**
     * 商品编码
     */
    @TableField(exist = false)
    private String code;

    /**
     * 商品分类编码
     */
    @TableField(exist = false)
    private String catalogCode;
}
