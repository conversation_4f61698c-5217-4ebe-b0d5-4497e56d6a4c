package com.yuchen.saas.device.api.entity.assets;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * 资产清单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_assets_data_info")
@ApiModel(value="SaasAssetsDataInfo对象", description="资产清单")
public class SaasAssetsDataInfo extends BaseEntity {

    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    private Long customerId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 资产状态编码
     */
    @ApiModelProperty(name = "assetsStateCode", value = "资产状态编码")
    private String assetsStateCode;
    /**
     * 资产状态名称
     */
    @ApiModelProperty(name = "assetsStateName", value = "资产状态名称")
    private String assetsStateName;
    /**
     * 资产类型（1：固定资产，2设备资产）
     */
    @ApiModelProperty(name = "assetsCategory", value = "资产类型（1：固定资产，2设备资产）")
    private String assetsCategory;
    /**
     * 资产上一个最终状态
     */
    @ApiModelProperty(name = "assetsLastState", value = "资产上一个最终状态")
    private String assetsLastState;
    /**
     * 资产编码
     */
    @ApiModelProperty(name = "assetsCode", value = "资产编码")
    private String assetsCode;
    /**
     * 资产流水序号
     */
    @ApiModelProperty(name = "assetsSerialNum", value = "资产流水序号")
    private String assetsSerialNum;
    /**
     * 资产名称
     */
    @ApiModelProperty(name = "assetsName", value = "资产名称")
    private String assetsName;
    /**
     * 资产分类id
     */
    @ApiModelProperty(name = "assetsSortId", value = "资产分类id")
    private Long assetsSortId;
    /**
     * 资产分类
     */
    @ApiModelProperty(name = "assetsSort", value = "资产分类")
    private String assetsSort;
    /**
     * 资产管理员
     */
    @ApiModelProperty(name = "assetsAdminUser", value = "资产管理员")
    private String assetsAdminUser;
    /**
     * 资产管理员id
     */
    @ApiModelProperty(name = "assetsAdminUserId", value = "资产管理员id")
    private Long assetsAdminUserId;
    /**
     * 资产品牌
     */
    @ApiModelProperty(name = "assetsBrand", value = "资产品牌")
    private String assetsBrand;
    /**
     * 资产型号
     */
    @ApiModelProperty(name = "assetsModel", value = "资产型号")
    private String assetsModel;
    /**
     * 所属/承租公司
     */
    @ApiModelProperty(name = "assetsAffiliatedCompany", value = "所属/承租公司")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsAffiliatedCompany;
    /**
     * 所属/承租公司id
     */
    @ApiModelProperty(name = "assetsAffiliatedCompanyId", value = "所属/承租公司id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long assetsAffiliatedCompanyId;
    /**
     * 使用状态编码
     */
    @ApiModelProperty(name = "assetsUseStatusCode", value = "使用状态编码")
    private String assetsUseStatusCode;
    /**
     * 使用状态
     */
    @ApiModelProperty(name = "assetsUseStatus", value = "使用状态")
    private String assetsUseStatus;
    /**
     * 所属位置id
     */
    @ApiModelProperty(name = "assetsLocationId", value = "所属位置id")
    private Long assetsLocationId;
    /**
     * 所属位置
     */
    @ApiModelProperty(name = "assetsLocation", value = "所属位置")
    private String assetsLocation;
    /**
     * 预计使用期限
     */
    @ApiModelProperty(name = "assetsPredictUseMonth", value = "预计使用期限")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer assetsPredictUseMonth;
    /**
     * 资产金额
     */
    @ApiModelProperty(name = "assetsAmount", value = "资产金额")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long assetsAmount;
    /**
     * 购置/起租日期
     */
    @ApiModelProperty(name = "assetsPurchaseDate", value = "购置/起租日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsPurchaseDate;
    /**
     * 购置方式编码
     */
    @ApiModelProperty(name = "assetsPurchaseModelCode", value = "购置方式编码")
    private String assetsPurchaseModelCode;
    /**
     * 购置方式名称
     */
    @ApiModelProperty(name = "assetsPurchaseModel", value = "购置方式名称")
    private String assetsPurchaseModel;
    /**
     * 订单号
     */
    @ApiModelProperty(name = "assetsOrderNo", value = "订单号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsOrderNo;
    /**
     * 计量单位
     */
    @ApiModelProperty(name = "assetsMeasureUnit", value = "计量单位")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsMeasureUnit;
    /**
     * 未含税金额
     */
    @ApiModelProperty(name = "assetsNotTaxAmount", value = "未含税金额")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long assetsNotTaxAmount;
    /**
     * 供应商编码
     */
    @ApiModelProperty(name = "assetsSupplierCode", value = "供应商编码")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsSupplierCode;
    /**
     * 设备序列号
     */
    @ApiModelProperty(name = "assetsEquipmentSerialNumber", value = "设备序列号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsEquipmentSerialNumber;
    /**
     * 生产厂商
     */
    @ApiModelProperty(name = "manufacturer", value = "生产厂商")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String manufacturer;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "assetsRemark", value = "备注信息")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsRemark;
    /**
     * 资产图片
     */
    @ApiModelProperty(name = "assetsImagesUrl", value = "资产图片")
    private String assetsImagesUrl;
    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "assetsSupplierName", value = "供应商名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsSupplierName;
    /**
     * 联系人
     */
    @ApiModelProperty(name = "assetsLinkman", value = "联系人")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsLinkman;
    /**
     * 联系方式
     */
    @ApiModelProperty(name = "assetsLinkPhone", value = "联系方式")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsLinkPhone;
    /**
     * 邮箱
     */
    @ApiModelProperty(name = "assetsLinkEmail", value = "邮箱")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsLinkEmail;
    /**
     * 维保到期日期
     */
    @ApiModelProperty(name = "assetsMaintenanceExpireDate", value = "维保到期日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assetsMaintenanceExpireDate;
    /**
     * 维保说明
     */
    @ApiModelProperty(name = "assetsMaintenanceExplain", value = "维保说明")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsMaintenanceExplain;
    /**
     * 维修次数
     */
    @ApiModelProperty(name = "assetsMaintenanceFrequency", value = "维修次数")
    private Integer assetsMaintenanceFrequency;
    /**
     * 最新一笔维修金额
     */
    @ApiModelProperty(name = "assetsLastMaintenanceAmount", value = "最新一笔维修金额")
    private Long assetsLastMaintenanceAmount;
    /**
     * 维修金额合计
     */
    @ApiModelProperty(name = "assetsMaintenanceAmountSum", value = "维修金额合计")
    private Long assetsMaintenanceAmountSum;
    /**
     * 使用人
     */
    @ApiModelProperty(name = "assetsUserName", value = "使用人")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsUserName;
    /**
     * 使用人id
     */
    @ApiModelProperty(name = "assetsUserId", value = "使用人id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long assetsUserId;
    /**
     * 使用部门
     */
    @ApiModelProperty(name = "assetsUseDept", value = "使用部门")
    private String assetsUseDept;
    /**
     * 使用部门id
     */
    @ApiModelProperty(name = "assetsUseDeptId", value = "使用部门id")
    private Long assetsUseDeptId;
    /**
     * 领用日期
     */
    @ApiModelProperty(name = "assetsClaimingDate", value = "领用日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date assetsClaimingDate;
    /**
     * 硬盘
     */
    @ApiModelProperty(name = "assetsDisk", value = "硬盘")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsDisk;
    /**
     * 内存
     */
    @ApiModelProperty(name = "assetsInternal", value = "内存")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsInternal;
    /**
     * CPU型号
     */
    @ApiModelProperty(name = "assetsCpuModel", value = "CPU型号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String assetsCpuModel;
    /**
     * 资产处置费用
     */
    @ApiModelProperty(name = "assetsDisposeCost", value = "资产处置费用")
    private Long assetsDisposeCost;
    /**
     * 资产处置费用
     */
    @ApiModelProperty(name = "assetsDisposeExpense", value = "资产处置费用")
    private Long assetsDisposeExpense;
    /**
     * 绑定智能硬件（0：否，1：是）
     */
    @ApiModelProperty(name = "bindIntelligentHardware", value = "绑定智能硬件（0：否，1：是）")
    private String bindIntelligentHardware;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "equipmentCode", value = "设备编码")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String equipmentCode;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "equipmentType", value = "设备类型")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String equipmentType;
    /**
     * 设备类型id
     */
    @ApiModelProperty(name = "equipmentTypeId", value = "设备类型id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long equipmentTypeId;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "equipmentName", value = "设备名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String equipmentName;
    /**
     * 调拨原因
     */
    @ApiModelProperty(name = "allotReason", value = "调拨原因")
    private String allotReason;
    /**
     * 快递单号
     */
    @ApiModelProperty(name = "courierNum", value = "快递单号")
    private String courierNum;
    /**
     * 故障类型(1:设备故障，2：外观损害，3：联网异常，4：其他故障)
     */
    @ApiModelProperty(name = "breakdownType", value = "故障类型(1:设备故障，2：外观损害，3：联网异常，4：其他故障)")
    private String breakdownType;
    /**
     * 故障等级（1：严重，2：中等，3：一般）
     */
    @ApiModelProperty(name = "breakdownLevel", value = "故障等级（1：严重，2：中等，3：一般）")
    private String breakdownLevel;
    /**
     * 是否联网（0：否，1：是）
     */
    @ApiModelProperty(name = "networkingFlag", value = "是否联网（0：否，1：是）")
    private String networkingFlag;
    /**
     * 是否联机（0：否，1：是）
     */
    @ApiModelProperty(name = "onlineFlag", value = "是否联机（0：否，1：是）")
    private String onlineFlag;
    /**
     * 创建人
     */
    @ApiModelProperty(name = "createOrgUser", value = "创建人")
    private Long createOrgUser;
    /**
     * 修改人
     */
    @ApiModelProperty(name = "updateOrgUser", value = "修改人")
    private Long updateOrgUser;



}
