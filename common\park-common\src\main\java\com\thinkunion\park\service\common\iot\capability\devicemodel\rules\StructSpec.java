package com.thinkunion.park.service.common.iot.capability.devicemodel.rules;

import com.thinkunion.park.service.common.iot.capability.devicemodel.DataType;

@Deprecated
public class StructSpec {
    private String name;
    private String identifier;
    private DataType<MetaRule> dataType;

    public String getName() {
        return this.name;
    }

    public String getIdentifier() {
        return this.identifier;
    }

    public DataType<MetaRule> getDataType() {
        return this.dataType;
    }
}

