package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;

@Data
public class SaasIrrigateAutoEnableDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID")
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * 启用状态(0:禁用 1:启用)
     */
    @ApiModelProperty(name = "enableStatus", value = "启用状态(0:禁用 1:启用)")
    @NotNull(message = "启用状态不能为空")
    private Integer enableStatus;

}
