package com.yuchen.saas.consumer.service.service.wechat;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.thinkunion.park.service.common.enums.parking.AppEnum;
import com.yuchen.saas.consumer.api.dto.wechat.SendTemplateReqDTO;
import com.yuchen.saas.consumer.api.dto.wechat.SendTemplateRespDTO;
import com.yuchen.saas.consumer.api.dto.wechat.WechatOfficialAccountMessageParamDTO;
import com.yuchen.saas.consumer.service.config.WeChatConfig;
import com.yuchen.saas.consumer.service.service.WeChatService;
import com.yuchen.saas.consumer.service.util.wechat.WeChatCustomMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.log.exception.ServiceException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2024/1/2 15:34
 */
@Component
@Slf4j
public class WechatOfficialAccountMessageServer {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private WeChatService weChatService;

    @Resource
    private WeChatConfig weChatConfig;

    private  String accessToken;

    private static final String SEND_URL = "%s/cgi-bin/message/template/send?access_token=%s";

    private static final String MESSAGE_URL = "%s/cgi-bin/message/custom/send?access_token=%s";


    /**
     * 消息推送
     *
     * @param templateId  消息模板id
     * @param openId      用户openId
     * @param param 推送对象
     */
    public void pushMessage(String appId, String templateId, String openId, WechatOfficialAccountMessageParamDTO param) {
        getInstance(appId);
        param.getMessageDataList().forEach(item->{
            pushMessage(appId,templateId,openId,item,param.getPage(), param.getMiniAppId());
        });
    }

    private void getInstance(String appId){
        //获取access_token
//        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(TOKEN_URL,new JSONObject().fluentPut("grant_type","client_credential").fluentPut("appid",appId).fluentPut("secret",appSecret).toJSONString(), JSONObject.class);
//        JSONObject json = Optional.ofNullable(responseEntity.getBody()).orElse(new JSONObject());
//        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        this.accessToken = weChatService.getWXAccessToken(appId, weChatConfig.getAppSecretByAppId(appId));
    }

    /**
     * 消息推送
     *
     * @param templateId  消息模板id
     * @param openId      用户openId
     * @param messageData 消息体
     * @param page
     */
    private void pushMessage(String appId, String templateId, String openId, JSONObject messageData, String page, String miniAppId) {
        String url = String.format(SEND_URL, weChatConfig.getDomainByAppId(appId), this.accessToken);
        //拼接推送的模版
        SendTemplateReqDTO reqDTO = new SendTemplateReqDTO();
        reqDTO.setTouser(openId);
        reqDTO.setTemplate_id(templateId);
        reqDTO.setData(messageData);
        if (StrUtil.isNotBlank(page)){
            JSONObject miniProgram = new JSONObject().fluentPut("appid", miniAppId)
                    .fluentPut("pagepath", page);
            reqDTO.setMiniprogram(miniProgram);
        }

        ResponseEntity<SendTemplateRespDTO> responseEntity = restTemplate.postForEntity(url, JSONObject.toJSONString(reqDTO), SendTemplateRespDTO.class);
        SendTemplateRespDTO response = Optional.ofNullable(responseEntity.getBody()).orElse(SendTemplateRespDTO.getInstance());
        if (Objects.requireNonNull(response).isSuccess()) {
            log.info("公众号模板消息推送成功");
            return;
        } else if (response.getErrcode() == 41001 && response.getErrmsg().contains("access_token missing rid")) {
            this.accessToken = weChatService.getWXAccessToken(appId, weChatConfig.getAppSecretByAppId(appId));
            pushMessage(appId, templateId, openId, messageData, page, miniAppId);
        }
        log.info("公众号推送失败：{} {}", response.getErrcode(), response.getErrmsg());
        throw new ServiceException("公众号推送失败：" + response.getErrcode() + response.getErrmsg());
    }

    @Async("taskPoolExecutor")
    public void pushWeChatCustomMessage(String appId, String openId, String accessToken, String content) {
        WeChatCustomMessageUtil customMessageUtil = new WeChatCustomMessageUtil(String.format(MESSAGE_URL, weChatConfig.getDomainByAppId(appId), accessToken), restTemplate);
        String result = customMessageUtil.doSendMessage(openId, content);
        log.info("{}回复结果返回{}", AppEnum.getDescByCode(appId), result);
    }
}
