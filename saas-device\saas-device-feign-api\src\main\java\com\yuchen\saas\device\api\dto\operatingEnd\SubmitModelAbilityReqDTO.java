package com.yuchen.saas.device.api.dto.operatingEnd;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/6/3 11:03
 */
@Data
public class SubmitModelAbilityReqDTO {

    /**
     * 设备型号id
     */
    @ApiModelProperty(name = "deviceModelId", value = "设备型号id")
    @NotNull(message = "设备型号id不能为空")
    private Long deviceModelId;

    /**
     * 能力类型（1：功能，2：属性，3：事件，4：标签）
     */
    @ApiModelProperty(name = "abilityType", value = "能力类型")
    private String abilityType;

    /**
     * 能力列表
     */
    @ApiModelProperty(name = "abilityList", value = "能力列表")
    private List<SubmitModelAbilityDataDTO> abilityList;

    @Data
    public static class SubmitModelAbilityDataDTO {

        /**
         * 能力对应id
         */
        @ApiModelProperty(name = "abilityId", value = "能力对应id")
        private Long abilityId;
        /**
         * 能力数据json（对应内容）
         * 事件：{"eventId": "","eventKey": "","eventName": "","eventType": 0,"jsonParam": ""}
         * 功能：{ "functionId": "", "functionKey": "", "functionName": "", "invokeModel": "", "url": "", "inputJsonParam": "", "outputJsonParam": "" }
         * 属性：{ "propertiesId": "", "propertiesKey": "", "propertiesName": "", "dataType": 0, "precise": 0, "unit": "", "isRead": 0,}
         */
        @ApiModelProperty(name = "abilityData", value = "能力数据json（对应内容）")
        private String abilityData;
        /**
         * 备注信息
         */
        @ApiModelProperty(name = "remark", value = "备注信息")
        @TableField(updateStrategy = FieldStrategy.IGNORED)
        private String remark;
    }
}
