<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.environment.SaasEnvironmentAlarmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.environment.SaasEnvironmentAlarm">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="alarm_id" property="alarmId" />
        <result column="alarm_name" property="alarmName" />
        <result column="alarm_time" property="alarmTime" />
        <result column="alarm_level" property="alarmLevel" />
        <result column="alarm_type" property="alarmType" />
        <result column="alarm_location" property="alarmLocation" />
        <result column="device_alias" property="deviceAlias" />
        <result column="device_name" property="deviceName" />
        <result column="device_location" property="deviceLocation" />
        <result column="close_time" property="closeTime" />
        <result column="close_reason" property="closeReason" />
        <result column="close_remark" property="closeRemark" />
        <result column="handle_status" property="handleStatus" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, alarm_id, alarm_name, alarm_time, alarm_level, alarm_type, alarm_location, device_alias, device_name, device_location, close_time, close_reason, closeRemark, handle_status, create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

</mapper>
