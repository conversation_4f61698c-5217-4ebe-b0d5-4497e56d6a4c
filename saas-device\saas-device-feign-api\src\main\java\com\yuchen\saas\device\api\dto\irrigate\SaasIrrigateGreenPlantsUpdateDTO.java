package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/30 15:53
 * @Description
 */
@Data
@ApiModel(value = "GreenPlantsUpdateTO")
public class SaasIrrigateGreenPlantsUpdateDTO {
    /**
     * id
     */
    @ApiModelProperty(name = "id", value = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 绿植编码（id）
     */
    @ApiModelProperty(name = "greenPlantsCode", value = "绿植编码（id）")
    @NotNull(message = "绿植编码不能为空")
    private String greenPlantsCode;
    /**
     * 绿植名称
     */
    @ApiModelProperty(name = "greenPlantsName", value = "绿植名称")
    @NotNull(message = "绿植名称不能为空")
    private String greenPlantsName;
    /**
     * 备注（描述信息）
     */
    @ApiModelProperty(name = "describeContents", value = "备注（描述信息）")
    private String describeContents;
    /**
     * 养护知识文档地址
     */
    @ApiModelProperty(name = "maintainingFile", value = "养护知识文档地址")
    private String maintainingFile;
    /**
     * 养护知识富文本
     */
    @ApiModelProperty(name = "maintainingRichText", value = "养护知识富文本")
    private String maintainingRichText;
    /**
     * 园区ID（项目id）
     */
    @ApiModelProperty(name = "parkId", value = "园区ID（项目id）")
    @NotNull(message = "项目id不能为空")
    private Long parkId;

}
