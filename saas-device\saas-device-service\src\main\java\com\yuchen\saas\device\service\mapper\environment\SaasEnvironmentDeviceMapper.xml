<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.environment.SaasEnvironmentDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.environment.SaasEnvironmentDevice">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="scene_type" property="sceneType" />
        <result column="device_category" property="deviceCategory" />
        <result column="product_key" property="productKey" />
        <result column="device_type_id" property="deviceTypeId" />
        <result column="device_name" property="deviceName" />
        <result column="device_alias" property="deviceAlias" />
        <result column="brand_model" property="brandModel" />
        <result column="device_location" property="deviceLocation" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="install_space_id" property="installSpaceId" />
        <result column="install_space_name" property="installSpaceName" />
        <result column="device_image_url" property="deviceImageUrl" />
        <result column="device_desc" property="deviceDesc" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, scene_type, device_category, product_key, device_type_id, device_name, device_alias, brand_model, device_location, longitude, latitude, install_space_id, install_space_name, device_image_url, device_desc, create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

</mapper>
