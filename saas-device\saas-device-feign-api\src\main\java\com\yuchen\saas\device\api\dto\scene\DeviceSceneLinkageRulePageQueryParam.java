package com.yuchen.saas.device.api.dto.scene;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DeviceSceneLinkageRulePageQueryParam {
    /**
     * 所属项目id
     */
    private Long projectId;

    /**
     * 业务服务id
     */
    private Long businessId;
    /**
     * 场景名称
     */
    private String sceneName;
    /**
     * 触发类型，1-设备触发，2-定时触发，3-手动触发
     */
    private Integer triggerMode;

    /**
     * 启用状态; 1-成功，0-失败
     */
    private Integer status;

    /**
     * 规则输出告警（0：否，1：是）
     */
    @ApiModelProperty(name = "ruleAlarm", value = "规则输出告警")
    private String ruleOutAlarm;

    /**
     * 已选择场景id列表
     */
    private List<Long> selectedSceneIdList;
}
