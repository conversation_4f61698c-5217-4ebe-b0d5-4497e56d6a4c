package com.yuchen.saas.consumer.service.service;

import com.yuchen.saas.consumer.api.entity.WeChatSchemeParam;
import com.yuchen.saas.consumer.service.dto.WeChatSubscribeMessage;
import com.yuchen.saas.consumer.service.dto.WeChatTempMessage;
import org.nest.springwrap.core.tool.api.R;

/**
 * @Author: 张逸飞
 * @Date: 2021/3/12 10:40
 * @Description: 微信相关接口
 */
public interface WeChatService {

    /**
     * 根据code获取微信token信息（包含openid）
     * @param code 微信返回的code
     * @return
     */
    R getToken(String code);

    /**
     * 根据 accessToken 获取微信JS接口的临时票据
     * @return
     */
    R getTicket();

    /**
     * 发送模版消息
     * @param weChatTempMessage 模版消息实体类
     * @return
     */
    R sendTempMessage(WeChatTempMessage weChatTempMessage);

    /**
     * 发送订阅消息
     * @param weChatSubscribeMessage 订阅消息实体类
     * @return
     */
    R sendSubscribeMessage(WeChatSubscribeMessage weChatSubscribeMessage);



    R sendSubscribeMessageNew(String appId,WeChatSubscribeMessage weChatSubscribeMessage);

    /**
     * 微信JSAPI签名
     * @param url 调用的url
     * @return
     */
    R jsApiSign(String url);

    /**
     * 小程序获取用户openID
     * @param code 小程序的code
     * @return
     */
    R getOpenId(String code);

    R<String> getUnionIdByOpenId(String openId);

    String getWXAccessToken(String appId, String appSecret);

    /**
     * 小程序获取用户手机号
     * @param code 小程序的code
     * @return
     */
    R getPhoneNumber(String code);

    /**
     * 获取不限制的小程序码
     * @param page 默认是主页，页面 page，例如 pages/index/index，根路径前不要填加 /，不能携带参数（参数请放在 scene 字段里）
     * @param scene 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符
     * @param env_version 要打开的小程序版本。正式版为 release，体验版为 trial，开发版为 develop
     * @return
     */
    R getUnlimitedQRCode(String page, String scene, String env_version);

    R getUnlimitedQRCodeByApplyId(String page, String scene, String env_version,Long applyId);

    R getTokenPms(String code);

    R getTicketPms();

    /**
     * 获取小程序跳转scheme码
     */
    R getJumpUrl(WeChatSchemeParam param);


    String getAccessTokenByAppId(String appId);

    R sendTempMessageNew(WeChatTempMessage weChatTempMessage,String accessToken,String appId);


}
