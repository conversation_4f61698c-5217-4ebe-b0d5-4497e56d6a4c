<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.environment.SaasDeviceParamCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.environment.SaasDeviceParamCard">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="device_id" property="deviceId" />
        <result column="device_name" property="deviceName" />
        <result column="param_id" property="paramId" />
        <result column="param_name" property="paramName" />
        <result column="param_alias" property="paramAlias" />
        <result column="show_type" property="showType" />
        <result column="decimal_digit" property="decimalDigit" />
        <result column="font_size" property="fontSize" />
        <result column="font_color" property="fontColor" />
        <result column="is_show_icon" property="isShowIcon" />
        <result column="icon_url" property="iconUrl" />
        <result column="enable_status" property="enableStatus" />
        <result column="param_status_detail" property="paramStatusDetail" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, device_id, device_name, param_id, param_name, param_alias, show_type, decimal_digit, font_size, font_color, is_show_icon, icon_url, enable_status, param_status_detail, create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

</mapper>
