package com.yuchen.saas.consumer.service.enums;

/**
 * @Author: 张逸飞
 * @Date: 2021/3/23 15:18
 * @Description: 发票状态枚举类型
 */
public enum InvoStatusEnum {

    // 开票状态 | 0 开票中 1 开票成功 2 开票失败 3 红冲 4 被红冲 9 未开票

    /**
     * 开票中
     */
    WAITING(0),

    /**
     * 开票成功
     */
    SUCCESS(1),

    /**
     * 开票失败
     */
    FAIL(2),

    /**
     * 未开票
     */
    NO(9);


    private int value;

    InvoStatusEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
