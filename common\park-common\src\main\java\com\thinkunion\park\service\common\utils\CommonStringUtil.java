package com.thinkunion.park.service.common.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/10 18:03
 */
public class CommonStringUtil {
    /**
     * 字符串长度，半角1，全角2。
     * @param str
     * @return
     */
    public static int length(String str){
        try{
            str = new String (str.getBytes("gb2312"),"iso-8859-1");
        } catch (Exception e) {
            return 0;
        }
        return str.length();
    }

    public static DecimalFormat moneyFormat (){
        return new DecimalFormat("######0.00");
    }

    /**
     * 将金额格式化为小数位数为2
     * @param s 格式化的字符串
     * @return 格式化后的字符串
     */
    public static String formatMoneyString(String s) {

        if (s == null || "".equals(s.trim())) {
            return "0.00";
        }
        double d = Double.parseDouble(s);
        return moneyFormat ().format(d);
    }

    /**
     * double类型的值格式化为小数位数为2
     * @param d 格式化的值
     * @return 格式化后的值
     */
    public static Double formatDouble(double d) {

        String s = moneyFormat ().format(d);
        return Double.parseDouble(s);
    }


    /**

     * 格式化数字 long分转元

     * @param fen

     * @return

     */

    public static String formatFenToYuan(Long fen){
        if(Objects.isNull(fen)){
            return "";
        }
        return BigDecimal.valueOf(fen).divide(new BigDecimal(100)).toPlainString();

    }

    public static String formatFenToYuan(Integer fen){
        if(Objects.isNull(fen)){
            return "";
        }
        return new BigDecimal(fen).divide(new BigDecimal(100)).toPlainString();

    }
}
