package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/9/24 11:43
 * @Description TODO
 */
@Data
public class NetAlarmTrendDto implements Serializable {
    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerManageId", value = "所属客户")
    private Long customerManageId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 服务应用id
     */
    @ApiModelProperty(name = "serviceId", value = "服务应用id")
    private Long serviceId;
    /**
     * 告警来源; 1-设备触发，2-场景联动，3-业务规则
     */
    @ApiModelProperty(name = "alarmSource", value = "告警来源; 1-设备触发，2-场景联动，3-业务规则")
    private Integer alarmSource;
    /**
     * 开始时间
     */
    @ApiModelProperty(name = "beginTime", value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private Date beginTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(name = "endTime", value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;
    /**
     * 时间类型(1按年，2按月，3按日)
     */
    @ApiModelProperty(name = "timeType", value = "时间类型(1按年，2按月，3按日)")
    @NotNull(message = "时间类型")
    private Integer timeType;
}
