package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 车行道闸设备表
 */
@Data
@TableName("net_device_car")
public class NetDeviceCar {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 所属客户(组织)
     */
//    @NotNull(message = "所属客户不能为空")
    private Long customerManageId;
    /**
     * 所属性项目id
     */
    @NotNull(message = "所属性项目不能为空")
    private Long parkId;
    /**
     * 所属停车场id
     */
    @NotNull(message = "所属停车场不能为空")
    private Long parkParkingId;
    /**
     * 产品key
     */
    @NotNull(message = "产品key不能为空")
    private String productKey;
    /**
     * 设备标识
     */
    @NotNull(message = "设备编码不能为空")
    private String deviceName;
    /**
     * 设备名称
     */
    @NotNull(message = "设备名称不能为空")
    private String deviceAlias;
    /**
     * 设备类型
     */
    @NotNull(message = "设备类型不能为空")
    private Integer deviceType;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 型号
     */
    private String model;
    /**
     * 地理位置
     */
    private String location;
    /**
     * 安装方向；0-出口，1-入口
     */
    @NotNull(message = "安装方向不能为空")
    private int direction;
    /**
     * 安装车道id
     */
    @NotNull(message = "安装车道不能为空")
    private Long parkingRoadwayId;
    /**
     * 绑定资产id
     */
    private Long bindAssetsId;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 是否删除；0-否，1-是
     */
    @TableLogic
    private Integer isDeleted;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
