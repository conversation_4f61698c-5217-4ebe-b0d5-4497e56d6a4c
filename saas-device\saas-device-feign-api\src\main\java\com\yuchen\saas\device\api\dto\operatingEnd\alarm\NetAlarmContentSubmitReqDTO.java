package com.yuchen.saas.device.api.dto.operatingEnd.alarm;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @create 2024/6/25 10:49
 */
@Data
public class NetAlarmContentSubmitReqDTO {

    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;
    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerId", value = "所属客户")
    private Long customerId;
    /**
     * 告警内容
     */
    @ApiModelProperty(name = "alarmContent", value = "告警内容")
    private String alarmContent;
    /**
     * 告警内容编码
     */
    @ApiModelProperty(name = "alarmContentCode", value = "告警内容编码")
    private String alarmContentCode;
    /**
     * 告警内容分类（1：物联告警、2：设备告警、3：业务告警）
     */
    @ApiModelProperty(name = "alarmContentCategory", value = "告警内容分类（1：物联告警、2：设备告警、3：业务告警）")
    private String alarmContentCategory;
    /**
     * 设备类型
     */
    @ApiModelProperty(name = "deviceCategoryId", value = "设备类型")
    private Long deviceCategoryId;
    /**
     * 产品标识
     */
    @ApiModelProperty(name = "productKey", value = "产品标识")
    private String productKey;
    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
