package com.yuchen.saas.consumer.service.dto;

import com.yuchen.saas.parking.api.entity.PmgBatParkBill;
import com.yuchen.saas.parking.api.vo.StaggeredMonthlyCardBillDataVo;
import com.yuchen.saas.parking.api.vo.VehicleMonthlyCardBillDataVo;
import com.yuchen.saas.parking.charge.api.entity.charge.SpcChargeBill;
import com.yuchen.saas.parking.charge.api.vo.charge.SpcChargeBillVO;
import com.yuchen.saas.payment.api.entity.PmgBatParkPay;
import com.yuchen.saas.payment.api.entity.PmgBatParkPayRefund;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 账单详细VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021年12月21日 17:11:20
 */
@Data
public class BillDetailVO extends PmgBatParkPay {

    /**
     * 临停账单信息
     */
    @ApiModelProperty(name = "pmgBatParkBill", value = "临停账单信息")
    private PmgBatParkBill pmgBatParkBill;

    /**
     * 包期账单信息
     */
    @ApiModelProperty(name = "vehicleMonthlyCardBillDataVo", value = "包期账单信息")
    private VehicleMonthlyCardBillDataVo vehicleMonthlyCardBillDataVo;


    @ApiModelProperty(name = "staggeredMonthlyCardBillDataVo", value = "错时停车账单信息")
    private StaggeredMonthlyCardBillDataVo staggeredMonthlyCardBillDataVo;
    /**
     * 退款账单信息
     */
    @ApiModelProperty(name = "pmgBatParkPayRefund", value = "退款账单信息")
    private PmgBatParkPayRefund pmgBatParkPayRefund;
    /**
     * 充电账单信息
     */
    @ApiModelProperty(name = "spcChargeBillVo", value = "充电账单信息")
    private SpcChargeBillVO spcChargeBillVo;
}
