package com.yuchen.saas.device.api.entity.assets;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 资产处置表
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_assets_dispose")
@ApiModel(value="SaasAssetsDispose对象", description="资产处置表")
public class SaasAssetsDispose extends BaseEntity {

    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    private Long customerId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 创建人账号
     */
    @ApiModelProperty(name = "createUserAccount", value = "创建人账号")
    private String createUserAccount;
    /**
     * 创建人组织用户id
     */
    @ApiModelProperty(name = "createUserOrgId", value = "创建人组织用户id")
    private Long createUserOrgId;
    /**
     * 创建入公司id
     */
    @ApiModelProperty(name = "disposeOrgId", value = "创建入公司id")
    private Long disposeOrgId;
    /**
     * 创建入公司名称
     */
    @ApiModelProperty(name = "disposeOrgName", value = "创建入公司名称")
    private String disposeOrgName;
    /**
     * 创建入部门id
     */
    @ApiModelProperty(name = "disposeDeptId", value = "创建入部门id")
    private Long disposeDeptId;
    /**
     * 创建入部门名称
     */
    @ApiModelProperty(name = "disposeDeptName", value = "创建入部门名称")
    private String disposeDeptName;
    /**
     * 处置类型:1退租,2报废清理,3盘亏处理,4转让出售,5损毁清理,6捐赠,7丢失,8其他
     */
    @ApiModelProperty(name = "disposeType", value = "处置类型:1退租,2报废清理,3盘亏处理,4转让出售,5损毁清理,6捐赠,7丢失,8其他")
    private Integer disposeType;
    /**
     * 处置类型名称
     */
    @ApiModelProperty(name = "disposeTypeName", value = "处置类型名称")
    private String disposeTypeName;
    /**
     * 处置金额合计 单位：分
     */
    @ApiModelProperty(name = "disposeTotalCost", value = "处置金额合计 单位：分")
    private Long disposeTotalCost;
    /**
     * 处置费用合计 单位：分
     */
    @ApiModelProperty(name = "disposeTotalExpense", value = "处置费用合计 单位：分")
    private Long disposeTotalExpense;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remark", value = "备注信息")
    private String remark;
    /**
     * 附件图片地址 逗号隔开
     */
    @ApiModelProperty(name = "file", value = "附件图片地址 逗号隔开")
    private String file;
    /**
     * 单据状态（0：草稿，1：进行中。2：审批完成，3：已撤回，4：审批失败）
     */
    @ApiModelProperty(name = "receiptStatus", value = "单据状态")
    private Integer receiptStatus;
    /**
     * 单据编号
     */
    @ApiModelProperty(name = "receiptCode", value = "单据编号")
    private String receiptCode;
    /**
     * 流程实例id
     */
    @ApiModelProperty(name = "processInstanceId", value = "流程实例id")
    private String processInstanceId;
    /**
     * 资产数据json
     */
    @ApiModelProperty(name = "assetsData", value = "资产数据json")
    private String assetsData;


}
