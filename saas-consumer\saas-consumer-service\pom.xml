<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>saas-consumer</artifactId>
        <groupId>com.yuchen.saas</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>saas-consumer-service</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>starter-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>xmlpull</groupId>
            <artifactId>xmlpull</artifactId>
            <version>1.1.3.1</version>
        </dependency>

        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- 微信支付官方Apache HttpClient装饰器 -->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-apache-httpclient</artifactId>
            <version>0.2.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alipay.sdk/alipay-easysdk -->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-easysdk</artifactId>
            <version>2.1.2</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.3</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>

        <dependency>
            <groupId>org.nest.springwrap</groupId>
            <artifactId>starter-social</artifactId>
        </dependency>

        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>3.0.10</version>
        </dependency>

        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-consumer-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-parking-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-parking-charge-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas.park.api</groupId>
            <artifactId>saas-park-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-user-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>constant-common</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-system-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas.message.api</groupId>
            <artifactId>saas-message-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-payment-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-device-feign-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-dict-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>miniprogram-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>

        <!-- 验证码 -->
        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuchen.saas</groupId>
            <artifactId>saas-integration-feign-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
