package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

@Data
public class NetAlarmTypeQryDto extends Query {

    /**
     * 告警类型名称
     */
    @ApiModelProperty(name = "alarmTypeName", value = "告警类型名称")
    private String alarmTypeName;
    /**
     * 唯一标识
     */
    @ApiModelProperty(name = "alarmCode", value = "告警类型唯一标识")
    private String alarmCode;
    /**
     * 告警分类; 3-业务告警 4-第三方告警
     */
    @ApiModelProperty(name = "category", value = "告警分类; 3-业务告警 4-第三方告警")
    private Integer category;
    /**
     * 状态; 0-停用，1-启用
     */
    @ApiModelProperty(name = "status", value = "状态; 0-停用，1-启用")
    private Integer status;
    /**
     * 服务应用id
     */
    @ApiModelProperty(name = "serviceId", value = "服务应用id")
    private Long serviceId;

}
