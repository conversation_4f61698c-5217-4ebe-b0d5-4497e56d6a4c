package com.yuchen.saas.device.api.entity.environment;

import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * <p>
 * 环境告警表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_environment_alarm")
@ApiModel(value="SaasEnvironmentAlarm对象", description="环境告警表")
public class SaasEnvironmentAlarm extends BaseEntity {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 告警ID
     */
    @ApiModelProperty(name = "alarmId", value = "告警ID")
    private Long alarmId;
    /**
     * 告警名称
     */
    @ApiModelProperty(name = "alarmName", value = "告警名称")
    private String alarmName;
    /**
     * 告警时间
     */
    @ApiModelProperty(name = "alarmTime", value = "告警时间")
    private Date alarmTime;
    /**
     * 告警等级
     */
    @ApiModelProperty(name = "alarmLevel", value = "告警等级")
    private Integer alarmLevel;
    /**
     * 告警类型
     */
    @ApiModelProperty(name = "alarmType", value = "告警类型")
    private Integer alarmType;
    /**
     * 告警位置
     */
    @ApiModelProperty(name = "alarmLocation", value = "告警位置")
    private String alarmLocation;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 设备标识
     */
    @ApiModelProperty(name = "deviceName", value = "设备标识")
    private String deviceName;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceLocation", value = "设备位置")
    private String deviceLocation;
    /**
     * 关闭时间
     */
    @ApiModelProperty(name = "closeTime", value = "关闭时间")
    private Date closeTime;
    /**
     * 关闭原因(1:已派工 2:已解决 3:无需处理 4:重复告警 5:其他)
     */
    @ApiModelProperty(name = "closeTime", value = "关闭原因(1:已派工 2:已解决 3:无需处理 4:重复告警 5:其他)")
    private Integer closeReason;
    /**
     * 关闭备注
     */
    @ApiModelProperty(name = "closeRemark", value = "关闭备注")
    private String closeRemark;
    /**
     * 处理状态(1:待处理 2:已处理 3:已关闭)
     */
    @ApiModelProperty(name = "handleStatus", value = "处理状态(1:待处理 2:已处理 3:已关闭)")
    private Integer handleStatus;

}
