package com.yuchen.saas.consumer.service.util;

import java.text.DecimalFormat;

public class SecondToTimeUtil {


    public static String secondToTime(double second) {
        if (second <= 0) return "0";
        final String[] units = new String[]{"秒", "分钟", "小时"};
        int digitGroups = (int) (Math.log10(second) / Math.log10(60));
        String value = "0";
        if(digitGroups<3) {
            value = new DecimalFormat("#,##0.#").format(second / Math.pow(60, digitGroups)) + "" + units[digitGroups];
        }else if(digitGroups >= 3) {
            //如果超过了小时的表达范围则，则转换为天，小时，分，秒格式

            value = secondToDate(second);
        }

        return value;
    }

    public static String secondToDate(double second) {
        Long time =  new Long(new Double(second).longValue());
        String strTime = null;
        Long days = time / (60 * 60 * 24);
        Long hours = (time % (60 * 60 * 24)) / (60 * 60);
        Long minutes = (time % (60 * 60)) / 60;
        Long seconds = time % 60;
        if (days > 0) {
            strTime = days + "天" + hours + "小时" + minutes + "分钟";
        } else if (hours > 0) {
            strTime = hours + "小时" + minutes + "分钟";
        } else if (minutes > 0) {
            strTime = minutes + "分钟" + seconds + "秒";
        } else {
            strTime = second + "秒";
        }
        return strTime;
    }


 /*   public static void main(String[] args) {
        System.out.println(secondToTime(1));
        System.out.println(secondToTime(120));
        System.out.println(secondToTime(3600));
        System.out.println(secondToTime(286990));
        System.out.println(secondToTime(864206));
    }*/
}