package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class SaasIrrigatePointLocationBindDTO {

    /**
     * 主键id
     */
    @ApiModelProperty(name = "id", value = "主键id")
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 绑定设备类型(1:硬件设备 2:传感设备)
     */
    @ApiModelProperty(name = "bindType", value = "绑定设备类型(1:硬件设备 2:传感设备)")
    @NotNull(message = "绑定类型不能为空")
    private Integer bindType;
    /**
     * 绑定阀门设备ID
     */
    @ApiModelProperty(name = "valveDeviceId", value = "绑定阀门设备ID")
    private Long valveDeviceId;
    /**
     * 绑定阀门设备编码
     */
    @ApiModelProperty(name = "valveDeviceName", value = "绑定阀门设备编码")
    private String valveDeviceName;
    /**
     * 绑定传感设备ID
     */
    @ApiModelProperty(name = "sensorDeviceId", value = "绑定传感设备ID")
    private Long sensorDeviceId;
    /**
     * 绑定传感设备编码
     */
    @ApiModelProperty(name = "sensorDeviceName", value = "绑定传感设备编码")
    private String sensorDeviceName;


}
