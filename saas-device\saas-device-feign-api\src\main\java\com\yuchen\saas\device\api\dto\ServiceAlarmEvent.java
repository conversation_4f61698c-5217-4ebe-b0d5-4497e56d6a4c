package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/26 18:32
 * @Description TODO
 */
@Data
public class ServiceAlarmEvent implements Serializable {
    /**
     * 服务id
     */
    @ApiModelProperty(name = "serviceId", value = "服务id")
    private Long serviceId;
    /**
     * 时间
     */
    @ApiModelProperty(name = "eventTime", value = "时间")
    private Long eventTime;
    /**
     * 告警类型唯一标识
     */
    @ApiModelProperty(name = "alarmCode", value = "告警类型唯一标识")
    private String alarmCode;
//    /**
//     * 告警名称
//     */
//    @ApiModelProperty(name = "alarmName", value = "告警名称")
//    private String alarmName;
    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerManageId", value = "所属客户")
    private Long customerManageId;

    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;

    /**
     * 媒体url(包含图片，视频，音频等)
     */
    @ApiModelProperty(name = "mediaUrl", value = "媒体url(包含图片，视频，音频等)")
    private String mediaUrl;
}
