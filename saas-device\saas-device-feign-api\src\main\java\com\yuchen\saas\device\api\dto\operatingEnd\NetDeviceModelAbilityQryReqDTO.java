package com.yuchen.saas.device.api.dto.operatingEnd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

/**
 * <AUTHOR>
 * @create 2024/6/3 10:57
 */
@Data
public class NetDeviceModelAbilityQryReqDTO extends Query {

    /**
     * iot设备标识
     */
    @ApiModelProperty(name = "deviceName", value = "iot设备标识")
    private String deviceName;

    /**
     * 设备型号id
     */
    @ApiModelProperty(name = "deviceModelId", value = "设备型号id")
    private Long deviceModelId;
    /**
     * 功能类型
     */
    @ApiModelProperty(name = "abilityType", value = "功能类型")
    private String abilityType;
    /**
     * 功能名称
     */
    @ApiModelProperty(name = "abilityName", value = "功能名称")
    private String abilityName;
}
