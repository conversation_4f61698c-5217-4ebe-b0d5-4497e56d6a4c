package com.yuchen.saas.device.api.dto.assets;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.tool.validator.group.AddGroup;
import org.nest.springwrap.core.tool.validator.group.UpdateGroup;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "SaasAssetsAddressAddDTO", description = "SaasAssetsAddressAddDTO")
public class SaasAssetsAddressAddDTO {
    @ApiModelProperty(name = "id", value = "id")
    @NotNull(message="id不能为空",groups = {UpdateGroup.class})
    private Long id;
    /**
     * 资产位置编码
     */
    @ApiModelProperty(name = "addressCode", value = "资产位置编码")
    @NotBlank(message="资产位置编码不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private String addressCode;
    /**
     * 资产位置名称
     */
    @ApiModelProperty(name = "addressName", value = "资产位置名称")
    @NotBlank(message="资产位置名称不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private String addressName;
    /**
     * 父级id
     */
    @ApiModelProperty(name = "parentId", value = "父级id")
    @NotNull(message="父级id不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private Long parentId;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
    /**
     * 排序
     */
    @ApiModelProperty(name = "sort", value = "排序")
    private Integer sort;
    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    @NotNull(message="客户id不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private Long customerId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    @NotNull(message="项目id不能为空",groups = {UpdateGroup.class, AddGroup.class})
    private Long projectId;

}
