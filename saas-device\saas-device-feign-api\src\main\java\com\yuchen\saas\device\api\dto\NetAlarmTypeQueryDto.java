package com.yuchen.saas.device.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/10/29 14:26
 * @Description TODO
 */
@Data
public class NetAlarmTypeQueryDto implements Serializable {
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 服务应用id
     */
    @ApiModelProperty(name = "serviceId", value = "服务应用id")
    private Long serviceId;
    /**
     * 告警分类; 3-业务告警 4-第三方告警
     */
    @ApiModelProperty(name = "category", value = "告警分类; 3-业务告警 4-第三方告警")
    private Integer category;
}
