package com.yuchen.saas.device.api.dto.assets;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/4/16 15:44
 */
@Data
public class AssetDataFieldsDTO {

    @ApiModelProperty(name = "fieldsName", value = "字段名称")
    private String fieldsName;

    @ApiModelProperty(name = "fieldsValue", value = "字段值")
    private String fieldsValue;

    @ApiModelProperty(name = "fieldsQueryRelation", value = "查询关系（1：等于，2：包含，3：区间，4：在列表）")
    private String fieldsQueryRelation;

    @ApiModelProperty(name = "fieldsQuerySource", value = "查询数据来源（1：基础表，2：业务表单）")
    private String fieldsQuerySource;
}
