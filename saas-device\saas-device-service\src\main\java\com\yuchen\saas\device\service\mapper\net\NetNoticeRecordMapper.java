package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.entity.NetNoticeRecord;
import com.yuchen.saas.device.api.vo.NetNoticeRecordPageVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface NetNoticeRecordMapper extends BaseMapper<NetNoticeRecord> {


    @Select("SELECT nr.*, nm.node_key, nm.node_name from net_notice_record nr" +
            " left join net_notice_template nt on nr.notice_template_id = nt.id" +
            " left join net_notice_config nc on nt.notice_config_id = nc.id" +
            " left join net_notice_method nm on nc.notice_type = nm.node_key" +
            " ${ew.customSqlSegment}")
    Page<NetNoticeRecordPageVo> selectPageRecord(Page<NetNoticeRecordPageVo> page, @Param(Constants.WRAPPER) QueryWrapper<String> wrapper);


}
