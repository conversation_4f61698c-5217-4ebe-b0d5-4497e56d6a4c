package com.thinkunion.park.service.common.iot.capability.devicemodel;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.List;

public class Service<T> {
    private String name;
    private String id;
    private String desc;
    private String action;
    private String type;
    private List<T> in;
    private List<Arg> out;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAction() {
        return this.action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public List<T> getIn() {
        return this.in;
    }

    public void setIn(List<T> in) {
        this.in = in;
    }

    public List<Arg> getOut() {
        return this.out;
    }

    public void setOut(List<Arg> out) {
        this.out = out;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public static class ServiceJsonDeSerializer
            implements JsonDeserializer<Service> {
        @Override
        public Service deserialize(JsonElement json, Type typeOfT,
                                   JsonDeserializationContext context) throws JsonParseException {
            JsonElement outputParamsEle;
            JsonElement callTypeEle;
            JsonElement descEle;
            JsonElement methodEle;
            if (json == null) {
                return null;
            }
            Service service = null;
            if (!json.isJsonObject()) {
                return service;
            }
            JsonObject payloadObject = json.getAsJsonObject();
            if (payloadObject == null) {
                return service;
            }
            JsonElement identifierEle = payloadObject.get("id");
            String identifier = null;
            if (identifierEle != null && identifierEle.isJsonPrimitive()) {
                identifier = identifierEle.getAsJsonPrimitive().getAsString();
            }
            JsonElement inputParamsEle = payloadObject.get("in");
            if ("get".equalsIgnoreCase(identifier)) {
                service = new GetService();
                if (inputParamsEle != null) {
                    service.setIn(context.deserialize(inputParamsEle, new TypeToken<List<String>>() {
                    }.getType()));
                }
            } else {
                service = new NormalService();
                if (inputParamsEle != null) {
                    service.setIn(context.deserialize(inputParamsEle, new TypeToken<List<Arg>>() {
                    }.getType()));
                }
            }
            service.setId(identifier);
            JsonElement nameEle = payloadObject.get("name");
            if (nameEle != null && nameEle.isJsonPrimitive()) {
                service.setName(nameEle.getAsJsonPrimitive().getAsString());
            }
            if ((methodEle = payloadObject.get("action")) != null && methodEle.isJsonPrimitive()) {
                service.setAction(methodEle.getAsJsonPrimitive().getAsString());
            }
            if ((descEle = payloadObject.get("desc")) != null && descEle.isJsonPrimitive()) {
                service.setDesc(descEle.getAsJsonPrimitive().getAsString());
            }
            if ((outputParamsEle = payloadObject.get("out")) != null) {
                service.setOut(context.deserialize(outputParamsEle, new TypeToken<List<Arg>>() {
                }.getType()));
            }
            if ((callTypeEle = payloadObject.get("type")) == null) {
                return service;
            }
            service.setType(callTypeEle.getAsJsonPrimitive().getAsString());
            return service;
        }

    }

}

