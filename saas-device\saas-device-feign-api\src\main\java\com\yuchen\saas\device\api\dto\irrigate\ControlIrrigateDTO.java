package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/7/1 14:58
 * @Description
 */
@Data
@ApiModel(value = "控制浇灌设备开关")
public class ControlIrrigateDTO {

    @ApiModelProperty(value = "园区id")
    @NotNull(message = "园区id不能为空")
    private Long parkId;

    @ApiModelProperty(value = "产品productKey")
    @NotNull(message = "产品productKey不能为空")
    private String productKey;

    @ApiModelProperty(value = "设备deviceName")
    @NotNull(message = "设备deviceName不能为空")
    private String deviceName;
}
