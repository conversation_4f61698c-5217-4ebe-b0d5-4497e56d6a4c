package com.thinkunion.park.service.common.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.nest.springwrap.core.mp.support.NestPage;

/**
 * 分页对象转换类
 */
public class PageConverterUtils {

    public static <T> NestPage<T> convert(Page<T> page) {
        NestPage<T> result = new NestPage<>();
        result.setTotal(page.getTotal());
        result.setSize(page.getSize());
        result.setRecords(page.getRecords());
        result.setCurrent(page.getCurrent());
        return result;
    }
}
