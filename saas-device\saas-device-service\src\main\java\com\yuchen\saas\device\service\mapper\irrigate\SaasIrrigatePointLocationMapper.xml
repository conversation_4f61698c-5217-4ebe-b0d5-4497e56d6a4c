<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.irrigate.SaasIrrigatePointLocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.irrigate.SaasIrrigatePointLocation">
        <id column="id" property="id" />
        <result column="irrigate_point_location_name" property="irrigatePointLocationName" />
        <result column="irrigate_area_id" property="irrigateAreaId" />
        <result column="remarks" property="remarks" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="address" property="address" />
        <result column="park_point_location_id" property="parkPointLocationId" />
        <result column="park_id" property="parkId" />
        <result column="valve_device_id" property="valveDeviceId" />
        <result column="valve_device_name" property="valveDeviceName" />
        <result column="sensor_device_id" property="sensorDeviceId" />
        <result column="sensor_device_name" property="sensorDeviceName" />
        <result column="bind_auto_irrigate" property="bindAutoIrrigate" />
        <result column="irrigate_auto_id" property="irrigateAutoId" />
        <result column="status" property="status" />
        <result column="create_org_user" property="createOrgUser" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="plane_position" property="planePosition" />
        <result column="video_id" property="videoId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, irrigate_point_location_name, irrigate_area_id, remarks, longitude, latitude, address, park_point_location_id, park_id, valve_device_id, valve_device_name, sensor_device_id, sensor_device_name, bind_auto_irrigate, irrigate_auto_id, status, create_org_user, create_user, create_dept, create_time, update_user, update_time, is_deleted, plane_position, video_id
    </sql>

</mapper>
