package com.yuchen.saas.consumer.service.enums;

/**
 * @Author: ouyang
 * @Date: 2021/3/13 10:49
 * @Description: 支付状态枚举
 */
public enum PayStatusEnum {


    /**
     * 待支付
     */
    UNPAID(0),

    /**
     * 支付中
     */
    PROCESS(1),

    /**
     * 支付成功
     */
    SUCCESS(2),
    /**
     * 支付失败
     */
    FAILURE(3);

    private int value;

    PayStatusEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
