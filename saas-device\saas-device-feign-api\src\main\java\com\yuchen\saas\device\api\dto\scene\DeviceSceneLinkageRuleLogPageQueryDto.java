package com.yuchen.saas.device.api.dto.scene;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nest.springwrap.core.mp.support.Query;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/11/5 11:00
 * @Description TODO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceSceneLinkageRuleLogPageQueryDto extends Query {
    /**
     * 所属项目id
     */
    @ApiModelProperty(name = "projectId", value = "所属项目id")
    private Long projectId;

    @ApiModelProperty(name = "triggerMode", value = "触发类型 property设备属性，event设备事件，deviceStatusChange设备上下线触发")
    private Integer triggerMode;

    @ApiModelProperty(name = "triggerStartTime", value = "执行开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date triggerStartTime;

    @ApiModelProperty(name = "triggerEndTime", value = "执行结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date triggerEndTime;

    @ApiModelProperty(name = "status", value = "规则执行状态 0:执行中，1：执行成功，-1：执行失败")
    private Integer status;

    @ApiModelProperty(name = "sceneName", value = "触发规则名称")
    private String sceneName;
}
