package com.yuchen.saas.auth.support;

import org.nest.springwrap.core.tool.utils.DigestUtil;
import org.springframework.security.crypto.password.PasswordEncoder;


public class NestPasswordEncoder implements PasswordEncoder {

    @Override
    public String encode(CharSequence rawPassword) {
        return DigestUtil.hex((String) rawPassword);
    }

    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        return encodedPassword.equals(encode(rawPassword));
    }

}
