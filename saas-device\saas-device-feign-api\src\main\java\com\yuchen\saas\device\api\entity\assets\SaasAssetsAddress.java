package com.yuchen.saas.device.api.entity.assets;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * <p>
 * 资产位置信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_assets_address")
@ApiModel(value="SaasAssetsAddress对象", description="资产位置信息表")
public class SaasAssetsAddress extends BaseEntity {

    /**
     * 资产位置编码
     */
    @ApiModelProperty(name = "addressCode", value = "资产位置编码")
    private String addressCode;
    /**
     * 资产位置名称
     */
    @ApiModelProperty(name = "addressName", value = "资产位置名称")
    private String addressName;
    /**
     * 父级id
     */
    @ApiModelProperty(name = "parentId", value = "父级id")
    private Long parentId;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
    /**
     * 排序
     */
    @ApiModelProperty(name = "sort", value = "排序")
    private Integer sort;
    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    private Long customerId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;



}
