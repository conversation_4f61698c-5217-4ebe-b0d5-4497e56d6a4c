package com.yuchen.saas.device.api.entity.environment;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * <p>
 * 视频设备表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_video_device")
@ApiModel(value="SaasVideoDevice对象", description="视频设备表")
public class SaasVideoDevice extends BaseEntity {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 所属产品
     */
    @ApiModelProperty(name = "productKey", value = "所属产品")
    private String productKey;
    /**
     * 设备类型ID
     */
    @ApiModelProperty(name = "deviceTypeId", value = "设备类型ID")
    private Long deviceTypeId;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 品牌型号
     */
    @ApiModelProperty(name = "brandModel", value = "品牌型号")
    private String brandModel;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceLocation", value = "设备位置")
    private String deviceLocation;
    /**
     * 经度
     */
    @ApiModelProperty(name = "longitude", value = "经度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(name = "latitude", value = "经度")
    private String latitude;
    /**
     * IP&SN
     */
    @ApiModelProperty(name = "ipSn", value = "IP&SN")
    private String ipSn;
    /**
     * 端口号
     */
    @ApiModelProperty(name = "portNo", value = "端口号")
    private Integer portNo;
    /**
     * 场景类型(1:环境监测 2:智慧环卫 3:智慧浇灌 4:智慧消防)
     */
    @ApiModelProperty(name = "sceneType", value = "场景类型(1:环境监测 2:智慧环卫 3:智慧浇灌 4:智慧消防)")
    private Integer sceneType;
    /**
     * 通道号
     */
    @ApiModelProperty(name = "channelNo", value = "通道号")
    private Integer channelNo;
    /**
     * 实时码流(1:多码流 2:主码流 3:子码流)
     */
    @ApiModelProperty(name = "realTimeStream", value = "实时码流(1:多码流 2:主码流 3:子码流)")
    private Integer realTimeStream;
    /**
     * 录像码流(1:主码流 2:子码流)
     */
    @ApiModelProperty(name = "videoStream", value = "录像码流(1:主码流 2:子码流)")
    private Integer videoStream;
    /**
     * 用户名
     */
    @ApiModelProperty(name = "userName", value = "用户名")
    private String userName;
    /**
     * 密码
     */
    @ApiModelProperty(name = "password", value = "密码")
    private String password;
    /**
     * 安装空间ID
     */
    @ApiModelProperty(name = "installSpaceId", value = "安装空间ID")
    private Long installSpaceId;
    /**
     * 安装空间名称
     */
    @ApiModelProperty(name = "installSpaceName", value = "安装空间名称")
    private String installSpaceName;
    /**
     * 设备图片
     */
    @ApiModelProperty(name = "deviceImageUrl", value = "设备图片")
    private String deviceImageUrl;
    /**
     * 设备描述
     */
    @ApiModelProperty(name = "deviceDesc", value = "设备描述")
    private String deviceDesc;

}
