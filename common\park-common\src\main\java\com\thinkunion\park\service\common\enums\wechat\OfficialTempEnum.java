package com.thinkunion.park.service.common.enums.wechat;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/1/5 16:59
 */
@Getter
@AllArgsConstructor
public enum OfficialTempEnum {

    PARKING_IN_OUT_TEMP_MESSAGE("parking_in_out_temp_message"),

    PARKING_BUSINESS_TRANSACT_TEMP_MESSAGE("parking_business_transact_temp_message"),
    CHARGING_START_TEMP_MESSAGE("charging_start_temp_message"),
    CHARGING_STOP_TEMP_MESSAGE("charging_stop_temp_message"),





    PAY_SUCCEED_TEMP_MESSAGE("pay_succeed_temp_message"),

    REFUND_SUCCEED_TEMP_MESSAGE("refund_succeed_temp_message"),

    CHARGING_OCCUPY_TEMP_MESSAGE("charging_occupy_temp_message"),
    CHARGING_DEVICE_WARN_TEMP_MESSAGE("charging_device_warn_message"),
    PROPERTY_BILL_REMINDER_MESSAGE("property_bill_reminder_message"),//催缴(物业账单)
    ;

    private final String tempCode;

    public static OfficialTempEnum getEnum(String tempCode) {
        if (StrUtil.isNotBlank(tempCode)) {
            for (OfficialTempEnum e : OfficialTempEnum.values()) {
                if (e.getTempCode().equals(tempCode)) {
                    return e;
                }
            }
        }
        return null;
    }

}
