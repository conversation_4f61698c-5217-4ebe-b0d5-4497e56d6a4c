package com.yuchen.saas.consumer.api.entity;

import lombok.Data;

/**
 * @description: 模版消息-预约提醒通知(管理员)-实体类
 * <AUTHOR>
 * @date 2023/6/30 16:14
 * @version 1.0
 */
@Data
public class TempMessageVisitorManageDTO extends TempMessageBaseDTO {

    /**
     * 模版ID
     */
    private final String TEMPLATE_ID = "m2C9rlV5D6_LN7OoButSvBRd2OC_2h4MzAYImsFBNqQ";

    /**
     * appId
     */
    private String appId;

    /**
     * 预约单位
     */
    private String keyword1;

    /**
     * 访客姓名
     */
    private String keyword2;

    /**
     * 访客人数
     */
    private String keyword3;
    /**
     * 车牌号码
     */
    private String keyword4;
    /**
     * 有效时间
     */
    private String keyword5;
    /**
     * 详情url
     */
    private String url;
}
