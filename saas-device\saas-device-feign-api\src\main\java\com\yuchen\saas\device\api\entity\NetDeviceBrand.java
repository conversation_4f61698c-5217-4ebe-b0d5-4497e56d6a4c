package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 设备品牌信息表
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_device_brand")
@ApiModel(value="NetDeviceBrand对象", description="设备品牌信息表")
public class NetDeviceBrand extends BaseEntity {

    /**
     * 品牌标识
     */
    @ApiModelProperty(name = "brandCode", value = "品牌标识")
    private String brandCode;
    /**
     * 品牌名称
     */
    @ApiModelProperty(name = "brandName", value = "品牌名称")
    private String brandName;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;



}
