package com.yuchen.saas.device.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class NetAlarmRecordQryDto extends Query {

    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerManageId", value = "客户id")
    private Long customerManageId;
    /**
     * 服务id
     */
    @ApiModelProperty(name = "serviceId", value = "服务id")
    private Long serviceId;

    /**
     * 管理区
     */
    @ApiModelProperty(name = "managementArea", value = "管理区")
    private Long managementArea;
    /**
     * 告警等级
     */
    @ApiModelProperty(name = "alarmLevel", value = "告警等级")
    private Integer alarmLevel;
    /**
     * 告警名称
     */
    @ApiModelProperty(name = "alarmName", value = "告警名称")
    private String alarmName;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceName", value = "设备名称")
    private String deviceName;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceLocation", value = "设备位置")
    private String deviceLocation;
    /**
     * 告警位置
     */
    @ApiModelProperty(name = "spatialPosition", value = "告警位置")
    private String spatialPosition;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 告警开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "startAlarmTime", value = "告警开始时间")
    private Date startAlarmTime;
    /**
     * 告警结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "endAlarmTime", value = "告警结束时间")
    private Date endAlarmTime;
    /**
     * 关闭开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "startCloseTime", value = "关闭开始时间")
    private Date startCloseTime;
    /**
     * 关闭结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "endCloseTime", value = "关闭结束时间")
    private Date endCloseTime;

    /**
     * 状态; 1-待处理，2-已关闭，3-已派工
     */
    @ApiModelProperty(name = "status", value = "状态; 1-待处理，2-已关闭，3-已派工")
    private Integer status;


}
