package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 车辆类型（0：其他车、1：小型车、2：大型车、3：摩托车）
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnergyTypeEnum {


    OTHER_CARS(0, "其他车"),
    SMALL_CAR(1, "小型车"),
    LARGE_CAR(2, "大型车"),
    MOTORCYCLE(3, "摩托车"),
   ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        EnergyTypeEnum[] enumArray = EnergyTypeEnum.values();
        for (EnergyTypeEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
