package com.thinkunion.park.service.common.enums.parking;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/15 17:37
 */
public enum CouponEnum {
    Parking("停车优惠",1);

    private String name;
    private int value;

    CouponEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public String getName(){
        return name;
    }

    public int getValue(){
        return value;
    }
}
