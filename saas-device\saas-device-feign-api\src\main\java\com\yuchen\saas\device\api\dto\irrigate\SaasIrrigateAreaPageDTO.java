package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/30 15:53
 * @Description
 */
@Data
@ApiModel(value = "IrrigateAreaPageDTO")
public class SaasIrrigateAreaPageDTO extends Query {

    @ApiModelProperty(value = "项目id不能为空")
    @NotNull(message = "园区id不能为空")
    private Long parkId;

    @ApiModelProperty(value = "区域名称")
    private String irrigateAreaName;


    @ApiModelProperty(name = "irrigateAreaCode", value = "区域编码（id）")
    private String irrigateAreaCode;

}
