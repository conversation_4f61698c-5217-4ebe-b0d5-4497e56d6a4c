package com.yuchen.saas.device.api.entity.product;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * 产品功能
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_product_function")
@ApiModel(value="NetProductFunction对象", description="产品功能")
public class NetProductFunction extends BaseEntity {

    /**
     * 产品id
     */
    @ApiModelProperty(name = "productId", value = "产品id")
    private Long productId;
    /**
     * 功能id
     */
    @ApiModelProperty(name = "functionId", value = "功能id")
    private String functionId;
    /**
     * 功能url
     */
    @ApiModelProperty(name = "url", value = "功能url")
    private String url;
    /**
     * 功能标识
     */
    @ApiModelProperty(name = "functionKey", value = "功能标识")
    private String functionKey;
    /**
     * 功能名称
     */
    @ApiModelProperty(name = "functionName", value = "功能名称")
    private String functionName;
    /**
     * 调用方式(1:同步，2：异步)
     */
    @ApiModelProperty(name = "invokeModel", value = "调用方式(1:同步，2：异步)")
    private String invokeModel;
    /**
     * 输入参数Json字符串
     */
    @ApiModelProperty(name = "inputJsonParam", value = "输出参数Json字符串")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String inputJsonParam;

    /**
     * 输出参数Json字符串
     */
    @ApiModelProperty(name = "outputJsonParam", value = "输出参数Json字符串")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String outputJsonParam;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;




}
