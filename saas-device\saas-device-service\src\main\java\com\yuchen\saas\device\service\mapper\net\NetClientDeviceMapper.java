package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.entity.NetClientDevice;
import com.yuchen.saas.device.api.vo.NetClientDevicePageVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface NetClientDeviceMapper extends BaseMapper<NetClientDevice> {

    @Select("SELECT cd.*,pc.category_name,p.product_name,g.grouping_name from net_client_device cd" +
            " left join net_product p on cd.product_id = p.id" +
            " left join net_product_category pc on p.product_category_id = pc.id" +
            " left join net_grouping g on cd.grouping_id = g.id" +
            " ${ew.customSqlSegment}")
    Page<NetClientDevicePageVo> selectClientDevicePage(Page<NetClientDevicePageVo> page, @Param(Constants.WRAPPER) QueryWrapper<String> wrapper);


}
