package com.yuchen.saas.device.api.dto.assets;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nest.springwrap.core.mp.support.Query;
import org.nest.springwrap.core.tool.validator.group.AddGroup;
import org.nest.springwrap.core.tool.validator.group.UpdateGroup;

import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SaasAssetsCategoryListDTO", description = "SaasAssetsCategoryListDTO")
public class SaasAssetsCategoryListDTO extends Query {
    @ApiModelProperty(name = "customerId", value = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 资产分类编码
     */
    @ApiModelProperty(name = "categoryCode", value = "资产分类编码")
    private String categoryCode;
    /**
     * 资产分类名称
     */
    @ApiModelProperty(name = "categoryName", value = "资产分类名称")
    private String categoryName;
    /**
     * 父级id
     */
    @ApiModelProperty(name = "parentId", value = "父级id")
    private Long parentId;
}
