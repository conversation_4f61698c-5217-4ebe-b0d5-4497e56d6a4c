package com.yuchen.saas.device.api.entity.sanitation;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * <p>
 * 环卫设备表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_sanitation_device")
@ApiModel(value="SaasSanitationDevice对象", description="环卫设备表")
public class SaasSanitationDevice extends BaseEntity {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 设备分类(2:硬件设备 3:传感设备)
     */
    @ApiModelProperty(name = "deviceCategory", value = "设备分类(2:硬件设备 3:传感设备)")
    private Integer deviceCategory;
    /**
     * 所属产品
     */
    @ApiModelProperty(name = "productKey", value = "所属产品")
    private String productKey;
    /**
     * 设备类型ID
     */
    @ApiModelProperty(name = "deviceTypeId", value = "设备类型ID")
    private Long deviceTypeId;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 品牌型号
     */
    @ApiModelProperty(name = "brandModel", value = "品牌型号")
    private String brandModel;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceLocation", value = "设备位置")
    private String deviceLocation;
    /**
     * 经度
     */
    @ApiModelProperty(name = "longitude", value = "经度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(name = "latitude", value = "纬度")
    private String latitude;
    /**
     * 安装空间
     */
    @ApiModelProperty(name = "installSpaceId", value = "安装空间")
    private Long installSpaceId;
    /**
     * 设备图片
     */
    @ApiModelProperty(name = "deviceImageUrl", value = "设备图片")
    private String deviceImageUrl;
    /**
     * 设备描述
     */
    @ApiModelProperty(name = "deviceDesc", value = "设备描述")
    private String deviceDesc;
    /**
     * 扫地机器人账号
     */
    @ApiModelProperty(name = "account", value = "扫地机器人账号")
    private String account;
    /**
     * 扫地机器人密码
     */
    @ApiModelProperty(name = "password", value = "扫地机器人密码")
    private String password;



}
