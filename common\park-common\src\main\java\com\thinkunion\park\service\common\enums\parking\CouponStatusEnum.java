package com.thinkunion.park.service.common.enums.parking;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/15 17:55
 */
public enum CouponStatusEnum {
    NOT_USE("未使用",0),
    USED("已使用",1);

    private String description;
    private int value;

    CouponStatusEnum(String description, int value){
        this.description = description;
        this.value = value;
    }

    public String getDescription(){
        return description;
    }

    public int getValue(){
        return value;
    }

}
