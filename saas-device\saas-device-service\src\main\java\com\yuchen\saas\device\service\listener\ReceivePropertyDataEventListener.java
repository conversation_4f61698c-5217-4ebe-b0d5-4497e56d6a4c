package com.yuchen.saas.device.service.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.yuchen.saas.device.api.entity.NetDevicePropertyData;
import com.yuchen.saas.device.api.enums.NetProductEnum;
import com.yuchen.saas.device.service.event.ReceivePropertyDataEvent;
import com.yuchen.saas.device.service.service.iot.property.PropertyBizService;
import com.yuchen.saas.device.service.service.alarm.NetAlarmRunRuleService;
import com.yuchen.saas.device.service.service.net.NetDevicePropertyDataService;
import com.yuchen.saas.park.api.vo.fault.DeviceProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 设备属性数据上报监听
 */
@Slf4j
@Service
public class ReceivePropertyDataEventListener {
    @Resource
    private NetAlarmRunRuleService netAlarmRunRuleService;
    @Resource
    private NetDevicePropertyDataService netDevicePropertyDataService;


    /**
     * 保存设备属性上报数据，TODO 后续优化 mongodb 或 hbase 存储
     *
     * @param event
     * @throws Exception
     */
    @TransactionalEventListener
    public void savePropertyData(ReceivePropertyDataEvent event) {
        DeviceProperty deviceProperty = event.getDeviceProperty();
        NetDevicePropertyData data = BeanUtil.copyProperties(deviceProperty, NetDevicePropertyData.class);
        Date date = DateUtil.date(Long.parseLong(deviceProperty.getDeviceTime()) * 1000);
        data.setDeviceTime(date);
        data.setValue(String.valueOf(deviceProperty.getValue()));
        data.setCreateTime(new Date());
        netDevicePropertyDataService.save(data);
    }


    /**
     * 执行报警规则
     *
     * @param event
     * @throws Exception
     */
    @EventListener
    public void alarmRunRule(ReceivePropertyDataEvent event) {
        netAlarmRunRuleService.receivePropertyData(event.getDeviceProperty());
    }


    /**
     * 属性上报处理各产品业务
     *
     * @param event
     * @throws Exception
     */
    @TransactionalEventListener
    public void doPropertyBiz(ReceivePropertyDataEvent event) {
        DeviceProperty deviceProperty = event.getDeviceProperty();
        NetProductEnum productEnum = NetProductEnum.convert(deviceProperty.getProductKey());
        PropertyBizService propertyBizService = SpringUtil.getBean(productEnum.getServiceNamePrefix() + "PropertyBizServiceImpl");
        if (propertyBizService != null) {
            propertyBizService.doBiz(deviceProperty);
        }
    }

}
