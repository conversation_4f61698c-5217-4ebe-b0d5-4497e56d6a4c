package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("net_device_property_data")
public class NetDevicePropertyData {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设备产品key
     */
    private String productKey;

    /**
     * 设备key
     */
    private String deviceName;

    /**
     * 属性标识
     */
    private String identify;

    /**
     * 属性值
     */
    private String value;

    /**
     * 设备时间
     */
    private Date deviceTime;

    /**
     * 创建时间
     */
    private Date createTime;



}
