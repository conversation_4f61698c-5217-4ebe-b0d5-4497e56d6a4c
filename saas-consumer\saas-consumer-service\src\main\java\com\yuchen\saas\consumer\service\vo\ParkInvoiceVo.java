package com.yuchen.saas.consumer.service.vo;

import com.yuchen.saas.consumer.service.entity.PmgBatParkInvoiceTitle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年3月17日 16:28:59
 * 用户开票参数VO
 */
@Data
public class ParkInvoiceVo extends PmgBatParkInvoiceTitle {
    /**
     * 邮箱
     */
    private String email;

    /**
     * 需要开票的账单标识
     */
    private String billIds;
    /**
     * 备注
     */
    private String remark;
    /**
     * 账单类型
     */
    private Integer billType;
    /**
     * 园区ID
     */
    private Long parkId;

    @ApiModelProperty(value = "开票人电话")
    private String personPhone;
}
