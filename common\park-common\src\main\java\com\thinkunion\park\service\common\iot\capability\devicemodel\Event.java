package com.thinkunion.park.service.common.iot.capability.devicemodel;

import java.util.List;

public class Event {

    private String name;
    private String id;
    private String desc;
    private String type;
    private String action;
    private List<Arg> out;

    public Arg getArg(String id) {
        if (out == null || out.isEmpty()) {
            return null;
        }
        for (Arg arg : out) {
            if (arg.getId().equals(id)) {
                return arg;
            }
        }
        return null;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getAction() {
        return this.action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<Arg> getOut() {
        return this.out;
    }

    public void setOut(List<Arg> out) {
        this.out = out;
    }
}

