package com.yuchen.saas.device.api.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 设置 iot 设备属性值
 */
@Data
public class SetIotDevicePropertyDto {

    @NotNull(message = "productKey不能为空")
    private String productKey;
    @NotNull(message = "deviceName不能为空")
    private String deviceName;
    /**
     * 设置设备属性值参数
     */
    @NotNull(message = "备属性值参数不能为空")
    private Map<String,Object> propertyItems;

}
