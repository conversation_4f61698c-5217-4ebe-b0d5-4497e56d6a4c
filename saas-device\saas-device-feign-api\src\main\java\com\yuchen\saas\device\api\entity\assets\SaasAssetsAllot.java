package com.yuchen.saas.device.api.entity.assets;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * 资产调拨表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_assets_allot")
@ApiModel(value="SaasAssetsAllot对象", description="资产调拨表")
public class SaasAssetsAllot extends BaseEntity {

    /**
     * 客户id
     */
    @ApiModelProperty(name = "customerId", value = "客户id")
    private Long customerId;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;
    /**
     * 创建人账号
     */
    @ApiModelProperty(name = "createUserAccount", value = "创建人账号")
    private String createUserAccount;
    /**
     * 创建人组织用户id
     */
    @ApiModelProperty(name = "createUserOrgId", value = "创建人组织用户id")
    private Long createUserOrgId;
    /**
     * 调入管理员组织成员id
     */
    @ApiModelProperty(name = "adminOrgUserId", value = "调入管理员组织成员id")
    private Long adminOrgUserId;
    /**
     * 调入管理员userId
     */
    @ApiModelProperty(name = "adminUserId", value = "调入管理员userId")
    private Long adminUserId;
    /**
     * 调入管理员名称
     */
    @ApiModelProperty(name = "adminUserName", value = "调入管理员名称")
    private String adminUserName;
    /**
     * 调拨后所属公司:1 调拨前公司,2调入公司
     */
    @ApiModelProperty(name = "allotDeptType", value = "调拨后所属公司:1 调拨前公司,2调入公司")
    private Integer allotDeptType;
    /**
     * 调入公司id
     */
    @ApiModelProperty(name = "allotUseOrgId", value = "调入公司id")
    private Long allotUseOrgId;
    /**
     * 调入公司名称
     */
    @ApiModelProperty(name = "allotUseOrgName", value = "调入公司名称")
    private String allotUseOrgName;
    /**
     * 调入部门id
     */
    @ApiModelProperty(name = "allotUseDeptId", value = "调入部门id")
    private Long allotUseDeptId;
    /**
     * 调入日期
     */
    @ApiModelProperty(name = "allotDate", value = "调入日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date allotDate;
    /**
     * 调入部门名称
     */
    @ApiModelProperty(name = "allotUseDeptName", value = "调入部门名称")
    private String allotUseDeptName;
    /**
     * 调入位置id
     */
    @ApiModelProperty(name = "allotAddressId", value = "调入位置id")
    private Long allotAddressId;
    /**
     * 调入位置名称
     */
    @ApiModelProperty(name = "allotAddress", value = "调入位置名称")
    private String allotAddress;
    /**
     * 金额合计 单位：分
     */
    @ApiModelProperty(name = "totalCost", value = "金额合计 单位：分")
    private Long totalCost;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remark", value = "备注信息")
    private String remark;
    /**
     * 单据状态
     */
    @ApiModelProperty(name = "receiptStatus", value = "单据状态")
    private Integer receiptStatus;
    /**
     * 单据编号
     */
    @ApiModelProperty(name = "receiptCode", value = "单据编号")
    private String receiptCode;
    /**
     * 流程实例id
     */
    @ApiModelProperty(name = "processInstanceId", value = "流程实例id")
    private String processInstanceId;
    /**
     * 资产数据json
     */
    @ApiModelProperty(name = "assetsData", value = "资产数据json")
    private String assetsData;



}
