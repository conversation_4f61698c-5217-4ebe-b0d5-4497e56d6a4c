package com.yuchen.saas.device.api.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class NetDeviceMoveGroupDto {

    /**
     * id
     */
    @NotNull(message = "ids不能为空")
    @Size(min = 1,message = "ids不能为空")
    private List<Long> ids;

    /**
     * 分组id
     */
    @NotNull(message = "分组id不能为空")
    private Long groupingId;


}
