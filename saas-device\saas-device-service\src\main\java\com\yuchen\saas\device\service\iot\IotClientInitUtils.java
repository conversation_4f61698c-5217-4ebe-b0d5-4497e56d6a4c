//package com.yuchen.saas.device.service.iot;
//
//import com.thinkunion.sdk.iot.IoTClient;
//import com.thinkunion.sdk.iot.bean.BaseRequest;
//import com.thinkunion.sdk.iot.bean.BaseResponse;
//import com.thinkunion.sdk.iot.exception.IotClientException;
//import com.thinkunion.sdk.iot.exception.IotServerException;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * 连接IOT工具类初始化
// */
//@Component
//public class IotClientInitUtils {
//
//    @Resource
//    private IotMqttConfig IotMqttConfig;
//
//    @Autowired
//    private IoTClient initClient(){
//        return new IoTClient(IotMqttConfig.getOpenapiUri(), IotMqttConfig.getOpenapiAccessKey(), IotMqttConfig.getOpenapiAccessSecret());
//    }
//
//    public <T extends BaseResponse> T connect(BaseRequest<T> request) {
//        try {
//            return initClient().send(request);
//        } catch (IotServerException e) {
//            e.printStackTrace();
//        } catch (IotClientException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//}