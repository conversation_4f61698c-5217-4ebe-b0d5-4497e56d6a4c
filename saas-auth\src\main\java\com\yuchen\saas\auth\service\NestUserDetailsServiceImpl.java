package com.yuchen.saas.auth.service;

import com.yuchen.saas.auth.constant.AuthConstant;
import com.yuchen.saas.auth.utils.TokenUtil;
import com.yuchen.saas.manage.system.api.enums.NestFeatureEnum;
import com.yuchen.saas.manage.system.api.vo.RoleScopeVO;
import com.yuchen.saas.manage.user.api.entity.UserInfo;
import com.yuchen.saas.manage.user.api.enums.AccountStatusEnum;
import com.yuchen.saas.manage.user.api.enums.UserEnum;
import com.yuchen.saas.manage.user.api.feign.IUserClient;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.nest.springwrap.core.jwt.entity.MenuRoot;
import org.nest.springwrap.core.secure.enums.IdEntityEnum;
import org.nest.springwrap.core.secure.utils.AuthUtil;
import org.nest.springwrap.core.tool.api.R;
import org.nest.springwrap.core.tool.constant.RoleConstant;
import org.nest.springwrap.core.tool.utils.Func;
import org.nest.springwrap.core.tool.utils.StringPool;
import org.nest.springwrap.core.tool.utils.StringUtil;
import org.nest.springwrap.core.tool.utils.WebUtil;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@AllArgsConstructor
public class NestUserDetailsServiceImpl implements UserDetailsService {

    private final IUserClient userClient;


    @Override
    @SneakyThrows
    public NestUserDetails loadUserByUsername(String phone) {
        HttpServletRequest request = WebUtil.getRequest();
        // 获取租户ID
        String headerTenant = request.getParameter("tenantId");
        String paramTenant = request.getParameter(TokenUtil.TENANT_PARAM_KEY);
        String deptId = request.getParameter("deptId")==null?"":request.getParameter("deptId");
        String openId = request.getParameter("openId");
        String appId = request.getParameter("appId");
        Integer applySource=-1;
        if(Func.isNotEmpty(request.getParameter("applySource"))){
            applySource = Integer.valueOf(request.getParameter("applySource"));
        }
        Long orgUserId=null;
        if(Func.isNotEmpty(request.getParameter("orgUserId"))){
            orgUserId = Long.valueOf(request.getParameter("orgUserId"));
        }
        if (StringUtil.isAllBlank(headerTenant, paramTenant)) {
            //throw new UserDeniedAuthorizationException(TokenUtil.TENANT_NOT_FOUND);
        }
        String idEntity=StringPool.EMPTY;
        if(Func.isNotEmpty(request.getParameter("idEntity"))){
            idEntity = IdEntityEnum.getCodeByCode(request.getParameter("idEntity"));
        }

        //清除原本的缓存
        /*ContextUser user = AuthUtil.getUser(request);
        if (user != null && jwtProperties.getState()) {
            // 消除两次ab密码的redis数据
            String userKey = new StringBuilder(CacheNames.USER_ACCESS_AUTH).append(user.getUserId()).toString();
            nestRedis.del(userKey);
            String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
            JwtUtil.updateAccessTokenTimeout(user.getTenantId(), String.valueOf(user.getUserId()), token);
        }*/

        // 获取用户类型
        String userType = Func.toStr(request.getHeader(TokenUtil.USER_TYPE_HEADER_KEY), TokenUtil.DEFAULT_USER_TYPE);

        // 远程调用返回数据
        R<UserInfo> result;
        // 根据不同用户类型调用对应的接口返回数据，用户可自行拓展
        if (userType.equals(UserEnum.WEB.getName())) {
            result = userClient.userInfoByPhone(headerTenant, phone, deptId,openId,appId,UserEnum.WEB.getName(),applySource,orgUserId);
        } else if (userType.equals(UserEnum.APP.getName())) {
            result = userClient.userInfoByPhone(headerTenant, phone, deptId,openId,appId,UserEnum.WEB.getName(),applySource,orgUserId);
        } else if (userType.equals(UserEnum.WECHAT.getName())) {
            result = userClient.userInfoByPhone(headerTenant, phone, deptId,openId,appId,UserEnum.WECHAT.getName(),applySource,orgUserId);
        }else {
            result = userClient.userInfoByPhone(headerTenant, phone, deptId,openId,appId,UserEnum.WEB.getName(),applySource,orgUserId);
        }

        // 判断返回信息
        if (result.isSuccess()) {
            UserInfo userInfo = result.getData();
            //适配底层超级管理员权限设定，钰辰运营商主管理员或者子管理员不对数据进行租户过滤
            if(Func.isNotEmpty(userInfo)&&Func.isNotEmpty(userInfo.getIsSuperAdmin())&&userInfo.getIsSuperAdmin()==1||TokenUtil.ADMIN_DEPT.equals(userInfo.getDeptId())) {
                if (userInfo.getIsAdmin() == TokenUtil.IS_ADMIN_VALUE || (userInfo.getRoleVOS() != null && userInfo.getRoleVOS().size() > 0)) {
                    userInfo.setRoles(Func.toStrList(RoleConstant.ADMINISTRATOR));
                }
            }

            if(Objects.equals(userInfo.getIsDisable(), AccountStatusEnum.DISABLE.getType())){
                throw new UserDeniedAuthorizationException("此账号被禁用了");
            }
            orgUserId=Func.isNotEmpty(userInfo.getOrgUser())?userInfo.getOrgUser().getId():null;
            //获取通用权限,用户权限拼装
            MenuRoot menuRoot=new MenuRoot();
            List<RoleScopeVO> roleScopeVOs = userInfo.getRoleScopeVOs();
            roleScopeVOs.forEach(roleScopeVO->{
                roleScopeVO.getFeatures().forEach(featureExpireVO -> {
                    List<String> apiPaths = featureExpireVO.getApiPaths();
                    MenuRoot.Expand expand = new MenuRoot.Expand();
                    expand.setExpiration(featureExpireVO.getExpireDate());
                    if (NestFeatureEnum.TypeEnum.DEFAULT.getCode() .equals(featureExpireVO.getType()) ||NestFeatureEnum.TypeEnum.PLATFORM.getCode().equals(featureExpireVO.getType())){
                        expand.setIfDefault(Boolean.TRUE);
                    }else {
                        expand.setIfDefault(Boolean.FALSE);
                    }
                    Map<String, MenuRoot.Expand> menuMap = menuRoot.getMenuMap();
                    apiPaths.forEach(apiPath-> {
                        MenuRoot.Expand expandOld = menuMap.get(apiPath);
                        if(Func.isEmpty(expandOld)){
                            menuMap.put(apiPath, expand);
                        }else if (Func.isEmpty(expandOld.getExpiration())) {
                            menuMap.put(apiPath, expand);
                        } else if (expandOld.getExpiration().getTime() < expand.getExpiration().getTime()) {
                            menuMap.put(apiPath, expand);
                        }
                    });
                });
            });

            return new NestUserDetails(userInfo.getId(),
                    userInfo.getTenantId(), StringPool.EMPTY, userInfo.getDeptRealName(), userInfo.getRealName(), userInfo.getDeptId(),
                    userInfo.getPostId(),userInfo.getRoleId(), Func.join(result.getData().getRoles()),
                    Func.toStr(userInfo.getAvatar(), TokenUtil.DEFAULT_AVATAR),
                    phone, AuthConstant.ENCRYPT + userInfo.getPassword(),Func.toStr(userInfo.getParkId(), TokenUtil.DEFAULT_AVATAR), userInfo.getDetail(), true, true, true, true,
                    AuthorityUtils.commaSeparatedStringToAuthorityList(Func.join(result.getData().getRoles())),userInfo.getUserInfoId(),userInfo.getIsAdmin(),userInfo.getMenuIds(),userInfo.getDeptRoleIds(), userInfo.getUserDetailId(),menuRoot,orgUserId,
                    userInfo.getCustomerManageId(),userInfo.getIsSuperAdmin(),idEntity);
        } else {
            throw new UsernameNotFoundException(result.getMessage());
        }
    }
}
