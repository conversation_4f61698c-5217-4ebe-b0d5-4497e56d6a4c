package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.entity.NetOperateSettings;
import com.yuchen.saas.device.api.vo.NetOperateSettingsPageVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface NetOperateSettingsMapper extends BaseMapper<NetOperateSettings> {

    @Select("SELECT os.*,a.alarm_name,t.business_ids from net_operate_settings os" +
            " left join net_alarm a on os.alarm_config_id = a.id" +
            " left join net_alarm_type t on a.alarm_type_id = t.id" +
            " ${ew.customSqlSegment}")
    Page<NetOperateSettingsPageVo> selectPageSettings(Page<NetOperateSettingsPageVo> page, @Param(Constants.WRAPPER) QueryWrapper<String> wrapper);
}
