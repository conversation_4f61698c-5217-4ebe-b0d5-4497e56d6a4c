package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@TableName("net_alarm")
public class NetAlarm {
      /**
       * id主键
       */
      @TableId(type = IdType.ASSIGN_ID)
      private Long id;

      /**
       * 所属客户
       */
      @NotNull(message = "所属客户")
      @ApiModelProperty(name = "customerManageId", value = "所属客户")
      private Long customerManageId;

      /**
       * 项目id
       */
      @ApiModelProperty(name = "projectId", value = "项目id")
      private Long projectId;

      /**
       * 告警名称
       */
      @NotNull(message = "告警名称不能为空")
      @ApiModelProperty(name = "alarmName", value = "告警名称")
      private String alarmName;

      /**
       * 告警级别; 值为 1，2，3，4，5
       */
      @NotNull(message = "告警级别不能为空")
      @ApiModelProperty(name = "alarmLevel", value = "告警级别; 值为 1，2，3，4，5")
      private Integer alarmLevel;

      /**
       * 告警类型id
       */
      @ApiModelProperty(name = "alarmTypeId", value = "告警类型id")
      private Long alarmTypeId;

      /**
       * 告警类型标识
       */
      @ApiModelProperty(name = "alarmTypeCode", value = "告警类型标识")
      private String alarmTypeCode;

      /**
       * 告警来源; 1-设备触发，2-场景联动，3-业务规则
       */
      @NotNull(message = "告警来源不能为空")
      @ApiModelProperty(name = "alarmSource", value = "告警来源; 1-设备触发，2-场景联动，3-业务规则，4-第三方平台")
      private Integer alarmSource;

      /**
       * 产品id
       */
      @ApiModelProperty(name = "productId", value = "产品id")
      private Long productId;

      /**
       * 服务应用id
       */
      @ApiModelProperty(name = "serviceId", value = "服务应用id")
      private Long serviceId;


      /**
       * NetTriggerModeEnum
       * 触发方式; 1-设备上线，2-设备离线，3-属性上报，4-事件上报
       */
      @ApiModelProperty(name = "triggerMode", value = "触发方式; 1-设备上线，2-设备离线，3-属性上报，4-事件上报")
      private Integer triggerMode;

      /**
       * 事件id
       */
      @ApiModelProperty(name = "eventId", value = "事件id")
      private Long eventId;

      /**
       * 触发条件json
       */
      @ApiModelProperty(name = "triggerConditionJson", value = "触发条件json")
      private String triggerConditionJson;

      /**
       * 执行动作json
       */
      @ApiModelProperty(name = "executeActionJson", value = "执行动作json")
      private String executeActionJson;

      /**
       * 状态；0-停用，1-启用
       */
      @ApiModelProperty(name = "status", value = "状态；0-停用，1-启用")
      private Integer status;
      /**
       * 应用所属业务规则id
       */
      @ApiModelProperty(name = "ruleId", value = "应用所属业务规则id")
      private Long ruleId;
      /**
       * 场景id
       */
      @ApiModelProperty(name = "sceneId", value = "场景id")
      private Long sceneId;
      /**
       * 第三方平台id
       */
      @ApiModelProperty(name = "thirdPartyId", value = "第三方平台id")
      private Long thirdPartyId;
      /**
       * 产品key
       */
      @ApiModelProperty(name = "productKey", value = "产品key")
      private String productKey;
      /**
       * 设备标识
       */
      @ApiModelProperty(name = "deviceName", value = "设备标识")
      private String deviceName;

      /**
       * 是否删除；0-否，1-是
       */
      @TableLogic
      private Integer isDeleted;

      /**
       * 创建时间
       */
      private Date createTime;

      /**
       * 更新时间
       */
      private Date updateTime;
      /**
       * 事件标识
       */
      @ApiModelProperty(name = "eventCode", value = "事件标识")
      private String eventCode;


      /**
       * 品牌型号
       */
      @ApiModelProperty(name = "brandModel", value = "品牌型号")
      private String brandModel;

      /**
       * 设备类型ID
       */
      @ApiModelProperty(name = "deviceTypeId", value = "设备类型ID")
      private Long deviceTypeId;

      @ApiModelProperty(name = "silentTime", value = "静默时间 单位分钟")
      private Integer silentTime;

}
