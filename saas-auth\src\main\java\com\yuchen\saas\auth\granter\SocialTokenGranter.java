package com.yuchen.saas.auth.granter;

import com.yuchen.saas.auth.constant.AuthConstant;
import com.yuchen.saas.auth.service.NestUserDetails;
import com.yuchen.saas.auth.utils.TokenUtil;
import com.yuchen.saas.manage.user.api.entity.User;
import com.yuchen.saas.manage.user.api.entity.UserInfo;
import com.yuchen.saas.manage.user.api.entity.UserOauth;
import com.yuchen.saas.manage.user.api.feign.IUserClient;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import org.nest.springwrap.core.social.props.SocialProperties;
import org.nest.springwrap.core.social.utils.SocialUtil;
import org.nest.springwrap.core.tool.api.R;
import org.nest.springwrap.core.tool.support.Kv;
import org.nest.springwrap.core.tool.utils.BeanUtil;
import org.nest.springwrap.core.tool.utils.Func;
import org.nest.springwrap.core.tool.utils.StringPool;
import org.nest.springwrap.core.tool.utils.WebUtil;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AccountStatusException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

public class SocialTokenGranter extends AbstractTokenGranter {
    private static final String GRANT_TYPE = "social";
    private static final Integer AUTH_SUCCESS_CODE = 2000;

    private final IUserClient userClient;
    private final SocialProperties socialProperties;

    protected SocialTokenGranter(AuthorizationServerTokenServices tokenServices,
                                 ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory,
                                 IUserClient userClient, SocialProperties socialProperties) {
        super(tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
        this.userClient = userClient;
        this.socialProperties = socialProperties;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {
        // 请求头租户信息
        HttpServletRequest request = WebUtil.getRequest();
        String tenantId = Func.toStr(request.getHeader(TokenUtil.TENANT_HEADER_KEY), TokenUtil.DEFAULT_TENANT_ID);

        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());
        // 开放平台来源
        String sourceParameter = parameters.get("source");
        // 匹配是否有别名定义
        String source = socialProperties.getAlias().getOrDefault(sourceParameter, sourceParameter);
        // 开放平台授权码
        String code = parameters.get("code");
        // 开放平台状态吗
        String state = parameters.get("state");

        // 获取开放平台授权数据
        AuthRequest authRequest = SocialUtil.getAuthRequest(source, socialProperties);
        AuthCallback authCallback = new AuthCallback();
        authCallback.setCode(code);
        authCallback.setState(state);
        AuthResponse authResponse = authRequest.login(authCallback);
        AuthUser authUser;
        if (authResponse.getCode() == AUTH_SUCCESS_CODE) {
            authUser = (AuthUser) authResponse.getData();
        } else {
            throw new InvalidGrantException("social grant failure, auth response is not success");
        }

        // 组装数据
        UserOauth userOauth = Objects.requireNonNull(BeanUtil.copy(authUser, UserOauth.class));
        userOauth.setSource(authUser.getSource());
        userOauth.setTenantId(tenantId);
        userOauth.setUuid(authUser.getUuid());

        // 远程调用，获取认证信息
        R<UserInfo> result = userClient.userAuthInfo(userOauth);
        NestUserDetails nestUserDetails;
        if (result.isSuccess()) {
         //   User user = result.getData().getUser();
            Kv detail = result.getData().getDetail();
           /* if (user == null) {
                throw new InvalidGrantException("social grant failure, user is null");
            }*/
            Long orgUserId=Func.isNotEmpty(result.getData().getOrgUser())?result.getData().getOrgUser().getId():null;
            nestUserDetails = new NestUserDetails(result.getData().getId(),
                    tenantId, result.getData().getOauthId(), result.getData().getName(), result.getData().getRealName(), result.getData().getDeptId(),
                    result.getData().getPostId(), result.getData().getRoleId(), Func.join(result.getData().getRoles()),
                    Func.toStr(userOauth.getAvatar(), TokenUtil.DEFAULT_AVATAR),
                    userOauth.getUsername(), AuthConstant.ENCRYPT + result.getData().getPassword(),Func.toStr(result.getData().getParkId(), TokenUtil.DEFAULT_AVATAR), detail, true, true, true, true,
                    AuthorityUtils.commaSeparatedStringToAuthorityList(Func.join(result.getData().getRoles())),result.getData().getUserInfoId(),result.getData().getIsAdmin(),result.getData().getMenuIds(),result.getData().getDeptRoleIds(), result.getData().getUserDetailId(),null,orgUserId,
                    result.getData().getCustomerManageId(),result.getData().getIsSuperAdmin(), StringPool.EMPTY);
        } else {
            throw new InvalidGrantException("social grant failure, feign client return error");
        }

        // 组装认证数据，关闭密码校验
        Authentication userAuth = new UsernamePasswordAuthenticationToken(nestUserDetails, null,
                nestUserDetails.getAuthorities());
        ((AbstractAuthenticationToken) userAuth).setDetails(parameters);
        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);

        // 返回 OAuth2Authentication
        return new OAuth2Authentication(storedOAuth2Request, userAuth);
    }

}
