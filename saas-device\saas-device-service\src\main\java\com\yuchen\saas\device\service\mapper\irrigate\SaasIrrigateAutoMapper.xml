<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.irrigate.SaasIrrigateAutoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.irrigate.SaasIrrigateAuto">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="irrigate_name" property="irrigateName" />
        <result column="trigger_mode" property="triggerMode" />
        <result column="remark" property="remark" />
        <result column="irrigate_point" property="irrigatePoint" />
        <result column="irrigate_rule_cycle" property="irrigateRuleCycle" />
        <result column="irrigate_rule_cycle_value" property="irrigateRuleCycleValue" />
        <result column="irrigate_times" property="irrigateTimes" />
        <result column="product_key" property="productKey" />
        <result column="param_name" property="paramName" />
        <result column="param_value" property="paramValue" />
        <result column="end_condition" property="endCondition" />
        <result column="irrigate_duration" property="irrigateDuration" />
        <result column="humidity" property="humidity" />
        <result column="enable_status" property="enableStatus" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, irrigate_name, trigger_mode, remark, irrigate_point, irrigate_rule_cycle, irrigate_rule_cycle_value, irrigate_times, product_key, param_name, param_value, end_condition, irrigate_duration, humidity, enable_status, create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

</mapper>
