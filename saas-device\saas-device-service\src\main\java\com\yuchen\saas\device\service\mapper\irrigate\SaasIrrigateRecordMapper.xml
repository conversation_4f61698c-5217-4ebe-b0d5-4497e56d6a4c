<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.irrigate.SaasIrrigateRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuchen.saas.device.api.entity.irrigate.SaasIrrigateRecord">
        <id column="id" property="id" />
        <result column="irrigate_area_id" property="irrigateAreaId" />
        <result column="irrigate_type" property="irrigateType" />
        <result column="irrigate_device_id" property="irrigateDeviceId" />
        <result column="irrigate_auto_id" property="irrigateAutoId" />
        <result column="irrigate_time" property="irrigateTime" />
        <result column="irrigate_end_time" property="irrigateEndTime" />
        <result column="project_id" property="projectId" />
        <result column="status" property="status" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, irrigate_area_id, irrigate_type, irrigate_device_id, irrigate_auto_id, irrigate_time, irrigate_end_time, project_id, status, create_org_user, create_user, create_dept, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
