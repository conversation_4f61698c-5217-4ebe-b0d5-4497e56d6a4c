package com.thinkunion.park.service.common.iot.capability.devicemodel.loader;

import com.google.gson.reflect.TypeToken;
import com.thinkunion.park.service.common.iot.capability.devicemodel.DeviceModel;
import com.thinkunion.park.service.common.iot.util.ModelGsonUtils;

public abstract class DeviceModelSerializer {

    public abstract String serialize(DeviceModel deviceModel);

    public abstract DeviceModel deserialize(String json);

    protected DeviceModel deserializeInner(String json) {
        return (DeviceModel) ModelGsonUtils.fromJson(json, new TypeToken<DeviceModel>() {
        }.getType());
    }

    protected String serializeInner(DeviceModel model) {
        return ModelGsonUtils.toJson(model);
    }
}

