package com.yuchen.saas.device.api.dto.scene;

import com.yuchen.saas.device.api.dto.scene.SceneExecuteAction;
import com.yuchen.saas.device.api.dto.scene.SceneExecuteCondition;
import com.yuchen.saas.device.api.dto.scene.SceneTriggerCondition;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class NetSceneRuleDto {

    /**
     * 触发器列表
     */
    private List<SceneTriggerCondition> triggerConditionList = new ArrayList<>();

    /**
     * 执行条件
     */
    private List<SceneExecuteCondition> executeConditionList = new ArrayList<>();

    /**
     * 执行动作
     */
    private List<SceneExecuteAction> executeActionList = new ArrayList<>();

}
