package com.yuchen.saas.consumer.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.tenant.mp.TenantEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年3月17日 16:28:59
 * 用户发票抬头记录表
 */
@Data
@TableName("pmg_bat_park_invoice_title")
@ApiModel(value = "发票抬头表", description = "发票抬头表")
public class PmgBatParkInvoiceTitle extends TenantEntity {

    /**
     * 微信或者支付宝用户ID
     */
    @ApiModelProperty(name = "payUserId", value = "微信或者支付宝用户ID")
    private String payUserId;
    /**
     * 公司名称
     */
    @ApiModelProperty(name = "companyName", value = "公司名称")
    private String companyName;
    /**
     * 	公司税号
     */
    @ApiModelProperty(name = "companyTaxNumber", value = "公司税号")
    private String companyTaxNumber;
    /**
     * 公司地址
     */
    @ApiModelProperty(name = "companyAddress", value = "公司地址")
    private String companyAddress;
    /**
     * 公司电话
     */
    @ApiModelProperty(name = "companyTel", value = "公司电话")
    private String companyTel;
    /**
     * 公司开户银行
     */
    @ApiModelProperty(name = "companyBank", value = "公司开户银行")
    private String companyBank;
    /**
     * 开户银行账号
     */
    @ApiModelProperty(name = "companyBankAccount", value = "开户银行账号")
    private String companyBankAccount;
    /**
     * 抬头类型 | 1、企业 2、个人
     */
    @ApiModelProperty(name = "titleType", value = "抬头类型 | 1、企业 2、个人")
    private Integer titleType;
    /**
     * 默认状态
     */
    @ApiModelProperty(name = "defaultState", value = "默认状态")
    private Integer defaultState;


    /**
     * 邮箱
     */
    @TableField(exist = false)
    private String email;

    /**
     * 需要开票的账单标识
     */
    @TableField(exist = false)
    private String billIds;
    /**
     * 备注
     */
    @TableField(exist = false)
    private String remark;
    /**
     * 账单类型
     */
    @TableField(exist = false)
    private Integer billType;
}
