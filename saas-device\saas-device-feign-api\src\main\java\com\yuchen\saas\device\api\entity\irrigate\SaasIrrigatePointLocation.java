package com.yuchen.saas.device.api.entity.irrigate;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * <p>
 * 浇灌点位信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_irrigate_point_location")
@ApiModel(value="SaasIrrigatePointLocation对象", description="浇灌点位信息表")
public class SaasIrrigatePointLocation extends BaseEntity {

    /**
     * 点位名称
     */
    @ApiModelProperty(name = "irrigatePointLocationName", value = "点位名称")
    private String irrigatePointLocationName;
    /**
     * 区域id
     */
    @ApiModelProperty(name = "irrigateAreaId", value = "区域id")
    private Long irrigateAreaId;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remarks", value = "备注信息")
    private String remarks;
    /**
     * 经度
     */
    @ApiModelProperty(name = "longitude", value = "经度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(name = "latitude", value = "纬度")
    private String latitude;
    /**
     * 地理位置
     */
    @ApiModelProperty(name = "address", value = "地理位置")
    private String address;
    /**
     * 园区点位id
     */
    @ApiModelProperty(name = "parkPointLocationId", value = "园区点位id")
    private Long parkPointLocationId;
    /**
     * 园区ID（项目id）
     */
    @ApiModelProperty(name = "parkId", value = "园区ID（项目id）")
    private Long parkId;
    /**
     * 绑定阀门设备ID
     */
    @ApiModelProperty(name = "valveDeviceId", value = "绑定阀门设备ID")
    private Long valveDeviceId;
    /**
     * 绑定阀门设备编码
     */
    @ApiModelProperty(name = "valveDeviceName", value = "绑定阀门设备编码")
    private String valveDeviceName;
    /**
     * 绑定传感设备ID
     */
    @ApiModelProperty(name = "sensorDeviceId", value = "绑定传感设备ID")
    private Long sensorDeviceId;
    /**
     * 绑定传感设备编码
     */
    @ApiModelProperty(name = "sensorDeviceName", value = "绑定传感设备编码")
    private String sensorDeviceName;
    /**
     * 是否绑定自动浇灌(0:否 1:是)
     */
    @ApiModelProperty(name = "bindAutoIrrigate", value = "是否绑定自动浇灌(0:否 1:是)")
    private Integer bindAutoIrrigate;
    /**
     * 自动浇灌ID
     */
    @ApiModelProperty(name = "irrigateAutoId", value = "自动浇灌ID")
    private Long irrigateAutoId;
    /**
     * 创建人
     */
    @ApiModelProperty(name = "createOrgUser", value = "创建人")
    private Long createOrgUser;

    @ApiModelProperty(name = "planePosition", value = "点位")
    private String planePosition;

    /**
     * 视频ID
     */
    @ApiModelProperty(name = "videoId", value = "视频ID")
    private Long videoId;

}
