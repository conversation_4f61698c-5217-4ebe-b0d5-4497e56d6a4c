package com.yuchen.saas.device.api.entity.irrigate;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * <p>
 * 浇灌地图信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_irrigate_map")
@ApiModel(value="SaasIrrigateMap对象", description="浇灌地图信息表")
public class SaasIrrigateMap extends BaseEntity {

    /**
     * 浇灌点位ID
     */
    @ApiModelProperty(name = "irrigatePointId", value = "浇灌点位ID")
    private String irrigatePointId;
    /**
     * 浇灌点位名称
     */
    @ApiModelProperty(name = "irrigatePointName", value = "浇灌点位名称")
    private String irrigatePointName;
    /**
     * 浇灌区域id
     */
    @ApiModelProperty(name = "irrigateAreaId", value = "浇灌区域id")
    private Long irrigateAreaId;
    /**
     * 备注信息
     */
    @ApiModelProperty(name = "remarks", value = "备注信息")
    private String remarks;
    /**
     * x坐标
     */
    @ApiModelProperty(name = "x", value = "x坐标")
    private String x;
    /**
     * y坐标
     */
    @ApiModelProperty(name = "y", value = "y坐标")
    private String y;
    /**
     * 地理位置
     */
    @ApiModelProperty(name = "address", value = "地理位置")
    private String address;
    /**
     * 园区点位ID
     */
    @ApiModelProperty(name = "parkPointLocationId", value = "园区点位ID")
    private Long parkPointLocationId;
    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;


}
