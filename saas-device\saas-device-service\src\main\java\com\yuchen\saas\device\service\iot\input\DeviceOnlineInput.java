package com.yuchen.saas.device.service.iot.input;

import com.alibaba.fastjson.JSONObject;
import com.yuchen.saas.device.api.feign.NetOperateDeviceFeignService;
import com.yuchen.saas.park.api.entity.fault.ConnectEvent;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class DeviceOnlineInput {

    @Resource
    private NestRedis nestRedis;
    @Resource
    private NetOperateDeviceFeignService netOperateDeviceFeignService;
    /**
     * 处理 IOT平台推送的上下线数据
     *
     * @param connectEvent 上下线入参
     */
    @Async
    public void inputData(ConnectEvent connectEvent) {
        // 获取设备的 productKey 和 deviceName
        String productKey = connectEvent.getProductKey();
        String deviceName = connectEvent.getDeviceName();
        log.info("productKey：{}", productKey);
        log.info("deviceName：{}", deviceName);
        String online = connectEvent.getOnline()? "1" : "0";
        // 缓存设备上下线信息
        nestRedis.set("productKey_" + productKey + ":deviceName_" + deviceName + ":online" + online, JSONObject.toJSONString(connectEvent));
        // 可以存库，需要更换其他sql存储
        //更新到运营平台设备数据
        netOperateDeviceFeignService.updateDeviceOnlineStatus(connectEvent);

    }
}