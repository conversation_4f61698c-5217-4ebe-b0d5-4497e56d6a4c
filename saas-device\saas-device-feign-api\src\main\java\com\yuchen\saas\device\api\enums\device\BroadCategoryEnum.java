package com.yuchen.saas.device.api.enums.device;

import com.yuchen.saas.device.api.enums.assets.ImmobilizationAssetsStateEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/8/27 15:56
 */
@Getter
@AllArgsConstructor
public enum BroadCategoryEnum {

    VIDEO_MONITOR("1", "视频监控"),
    SMART_HARDWARE("2", "智能硬件"),
    SENSOR("3", "传感设备"),
    ;

    private final String code;
    private final String desc;

    /**
     * 获取所有大类
     * @return list
     */
    public static List<String> getBroadCategoryList() {
        List<String> productKeyList = new ArrayList<>();
        BroadCategoryEnum[] allEnums = BroadCategoryEnum.values();
        for (BroadCategoryEnum enu : allEnums) {
            productKeyList.add(enu.getCode());
        }
        return productKeyList;
    }

    public static String getDescByCode(String code) {
        if (Objects.nonNull(code)) {
            for (BroadCategoryEnum e : BroadCategoryEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e.getDesc();
                }
            }
        }
        return "未知类型";
    }
}
