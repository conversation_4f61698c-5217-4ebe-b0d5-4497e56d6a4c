package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;
import org.nest.springwrap.core.tenant.mp.TenantEntity;

/**
 * <p>
 * 告警配置子表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("net_alarm_detail")
@ApiModel(value="NetAlarmDetail对象", description="告警配置子表")
public class NetAlarmDetail extends BaseEntity {

    /**
     * 项目id
     */
    @ApiModelProperty(name = "netAlarmId", value = "告警配置id")
    private Long netAlarmId;
    /**
     * 设备标识
     */
    @ApiModelProperty(name = "deviceName", value = "设备标识")
    private String deviceName;
    /**
     * 产品key
     */
    @ApiModelProperty(name = "productKey", value = "产品key")
    private String productKey;
    /**
     * 项目id
     */
    @ApiModelProperty(name = "projectId", value = "项目id")
    private Long projectId;



}
