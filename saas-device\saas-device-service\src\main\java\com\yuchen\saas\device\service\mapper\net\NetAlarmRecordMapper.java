package com.yuchen.saas.device.service.mapper.net;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuchen.saas.device.api.dto.NetAlarmStatisticsPageDto;
import com.yuchen.saas.device.api.dto.NetAlarmTrendDto;
import com.yuchen.saas.device.api.entity.NetAlarmRecord;
import com.yuchen.saas.device.api.vo.*;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface NetAlarmRecordMapper extends BaseMapper<NetAlarmRecord> {
    NetAlarmLevelStatisticsVO levelStatistics(@Param("beginTime") Date beginTime,@Param("endTime") Date endTime,
                                              @Param("customerManageId") Long customerManageId, @Param("projectId") Long projectId);

    Integer todayCount(@Param("customerManageId") Long customerManageId, @Param("projectId") Long projectId);

    List<NetAlarmTrendVo> trendYear(@Param("qryDto") NetAlarmTrendDto qryDto);
    List<NetAlarmTrendVo> trendMonth(@Param("qryDto") NetAlarmTrendDto qryDto);
    List<NetAlarmTrendVo> trendDay(@Param("qryDto") NetAlarmTrendDto qryDto);

    List<NetAlarmBarGraphVO> barGraph(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime,
                                      @Param("customerManageId") Long customerManageId, @Param("projectId") Long projectId);
    List<NetAlarmServiceCountVO> pieChart(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime,
                                          @Param("customerManageId") Long customerManageId, @Param("projectId") Long projectId);

    Page<NetAlarmStatisticsPageVO> pageByDay(Page<NetAlarmStatisticsPageVO> resultPage,@Param("qryDto") NetAlarmStatisticsPageDto qryDto);

    List<NetAlarmCountAllServiceVO> countAllService();
}
