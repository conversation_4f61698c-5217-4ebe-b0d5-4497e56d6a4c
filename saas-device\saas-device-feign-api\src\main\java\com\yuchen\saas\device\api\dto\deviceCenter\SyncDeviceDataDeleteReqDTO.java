package com.yuchen.saas.device.api.dto.deviceCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2024/8/28 9:58
 */
@Data
public class SyncDeviceDataDeleteReqDTO {

    /**
     * 大类（1：视频监控，2：智能硬件，3：传感设备）
     */
    @ApiModelProperty(name = "broadCategory", value = "大类（1：视频监控，2：智能硬件，3：传感设备）")
    @NotBlank(message = "硬件大类不能为空")
    private String broadCategory;

    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;
}
