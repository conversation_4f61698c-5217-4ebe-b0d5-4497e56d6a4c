package com.yuchen.saas.auth.granter;

import com.yuchen.saas.manage.user.api.feign.ISaasWechatSubscribeDataClient;
import com.yuchen.saas.manage.user.api.feign.IUserClient;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.nest.springwrap.core.social.props.SocialProperties;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.provider.CompositeTokenGranter;
import org.springframework.security.oauth2.provider.TokenGranter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class NestTokenGranter {

    public static TokenGranter getTokenGranter(final AuthenticationManager authenticationManager, final AuthorizationServerEndpointsConfigurer endpoints,
                                               NestRedis nestRedis, IUserClient userClient,
                                               SocialProperties socialProperties,
                                               UserDetailsService userDetailsService, ISaasWechatSubscribeDataClient subscribeDataClient) {
        // 默认tokenGranter集合
        List<TokenGranter> granters = new ArrayList<>(Collections.singletonList(endpoints.getTokenGranter()));
        // 增加图形验证码模式
        granters.add(new CaptchaTokenGranter(authenticationManager, endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), nestRedis));
        // 增加第三方登陆模式
        granters.add(new SocialTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), userClient, socialProperties));
        // 增加手机验证码登录方式
        granters.add(new SmsCodeTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(),userDetailsService,nestRedis,userClient));
        //微信静默登录
        granters.add(new WeChatTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(),userDetailsService,nestRedis,userClient,subscribeDataClient));
        granters.add(new OfficialaccountGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(),userDetailsService,nestRedis,userClient));
        //pc端切换token
        granters.add(new HandoverTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(),userDetailsService,nestRedis,userClient));


        // 组合tokenGranter集合
        return new CompositeTokenGranter(granters);
    }
}
