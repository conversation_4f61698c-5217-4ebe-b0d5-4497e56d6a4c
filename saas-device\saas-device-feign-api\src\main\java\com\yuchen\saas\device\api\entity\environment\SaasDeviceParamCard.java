package com.yuchen.saas.device.api.entity.environment;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nest.springwrap.core.mp.base.BaseEntity;

/**
 * <p>
 * 参数卡片表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("saas_device_param_card")
@ApiModel(value="SaasDeviceParamCard对象", description="参数卡片表")
public class SaasDeviceParamCard extends BaseEntity {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 设备ID
     */
    @ApiModelProperty(name = "deviceId", value = "设备ID")
    private Long deviceId;
    /**
     * 设备编码
     */
    @ApiModelProperty(name = "deviceName", value = "设备编码")
    private String deviceName;
    /**
     * 参数ID
     */
    @ApiModelProperty(name = "paramId", value = "参数ID")
    private Long paramId;
    /**
     * 参数名称
     */
    @ApiModelProperty(name = "paramName", value = "参数名称")
    private String paramName;
    /**
     * 参数别名
     */
    @ApiModelProperty(name = "paramAlias", value = "参数别名")
    private String paramAlias;
    /**
     * 展示方式(1:开关控制 2:状态切换 3:状态显示 4:数值显示)
     */
    @ApiModelProperty(name = "showType", value = "展示方式(1:开关控制 2:状态切换 3:状态显示 4:数值显示)")
    private Integer showType;
    /**
     * 小数位数
     */
    @ApiModelProperty(name = "decimalDigit", value = "小数位数")
    private Integer decimalDigit;
    /**
     * 字体大小
     */
    @ApiModelProperty(name = "fontSize", value = "字体大小")
    private Integer fontSize;
    /**
     * 字体颜色
     */
    @ApiModelProperty(name = "fontColor", value = "字体颜色")
    private String fontColor;
    /**
     * 是否显示图标(0:否 1:是)
     */
    @ApiModelProperty(name = "isShowIcon", value = "是否显示图标(0:否 1:是)")
    private Integer isShowIcon;
    /**
     * 图标
     */
    @ApiModelProperty(name = "iconUrl", value = "图标")
    private String iconUrl;
    /**
     * 启用状态(0:禁用 1:启用)
     */
    @ApiModelProperty(name = "enableStatus", value = "启用状态(0:禁用 1:启用)")
    private Integer enableStatus;
    /**
     * 参数状态明细
     */
    @ApiModelProperty(name = "paramStatusDetail", value = "参数状态明细")
    private String paramStatusDetail;

}
