package com.yuchen.saas.auth;

import org.nest.springwrap.core.cloud.feign.EnableNestFeign;
import org.nest.springwrap.core.launch.NestApplication;
import org.nest.springwrap.core.launch.app.AppNameConstant;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.context.annotation.ComponentScan;

@EnableNestFeign
@SpringCloudApplication
@ComponentScan("com.yuchen")
public class AuthApplication {

    public static void main(String[] args) {
        NestApplication.run(AppNameConstant.SAAS_AUTH, AuthApplication.class, args);
    }
}
