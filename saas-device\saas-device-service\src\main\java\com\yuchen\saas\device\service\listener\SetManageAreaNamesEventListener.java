package com.yuchen.saas.device.service.listener;

import com.yuchen.saas.device.service.event.SetManageAreaNamesEvent;
import com.yuchen.saas.payment.api.dto.SiteListDataDTO;
import com.yuchen.saas.payment.api.feign.OrderManageFeignService;
import lombok.extern.slf4j.Slf4j;
import org.nest.springwrap.core.tool.api.R;
import org.nest.springwrap.core.tool.utils.ClassUtil;
import org.nest.springwrap.core.tool.utils.Func;
import org.nest.springwrap.core.tool.utils.ObjectUtil;
import org.nest.springwrap.core.tool.utils.ReflectUtil;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class SetManageAreaNamesEventListener {
    @Resource
    private OrderManageFeignService orderManageFeignService;


    @EventListener
    public void setManageAreaNames(SetManageAreaNamesEvent event) throws Exception {
        List<String> manageAreaIdsList = event.getManageAreaIdsList();
        List<?> dataList = event.getDataList();
        if (Func.isEmpty(manageAreaIdsList) || Func.isEmpty(dataList)) {
            return;
        }
        R<List<SiteListDataDTO>> clientRes = orderManageFeignService.getAllSiteListByOperator(null);
        if (!clientRes.isSuccess() || clientRes.getData() == null || clientRes.getData().size() == 0) {
            return;
        }
        log.info("NetOperateDeviceService-selectPage:clientRes = " + clientRes.toString());

        List<SiteListDataDTO.SiteDataDTO> allList = new ArrayList<>();
        for (SiteListDataDTO resData : clientRes.getData()) {
            if (Func.isNotEmpty(resData.getSiteDataDTOList())) {
                allList.addAll(resData.getSiteDataDTOList());
            }
        }

        try {
            Object tmpData = dataList.get(0);
            Field manageAreaIdsField = ReflectUtil.getField(tmpData.getClass(), "manageAreaIds");
            Field manageAreaNamesField = ReflectUtil.getField(tmpData.getClass(), "manageAreaNames");
            if (ObjectUtil.isEmpty(manageAreaIdsField) || ObjectUtil.isEmpty(manageAreaNamesField)) {
                return;
            }

            for (Object data : dataList) {
                Method getManageAreaIds = ClassUtil.getMethod(data.getClass(), "getManageAreaIds", new Class[0]);
                String dataManageAreaIds = String.valueOf(getManageAreaIds.invoke(data));
                if (Func.isNotEmpty(dataManageAreaIds)) {
                    String manageAreaNames = "";
                    String[] ids = dataManageAreaIds.split(",");
                    for (String id : ids) {
                        for (SiteListDataDTO.SiteDataDTO dataDto : allList) {
                            if (Long.valueOf(id).equals(dataDto.getSiteId())) {
                                manageAreaNames += dataDto.getSiteName() + ",";
                            }
                        }
                    }
                    Method setManageAreaNames = ClassUtil.getMethod(data.getClass(), "setManageAreaNames", new Class[]{String.class});
                    setManageAreaNames.invoke(data, manageAreaNames);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }



}
