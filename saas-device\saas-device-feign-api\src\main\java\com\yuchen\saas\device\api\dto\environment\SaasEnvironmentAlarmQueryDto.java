package com.yuchen.saas.device.api.dto.environment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

import java.util.Date;

@Data
public class SaasEnvironmentAlarmQueryDto extends Query {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 告警等级
     */
    @ApiModelProperty(name = "alarmLevel", value = "告警等级")
    private Integer alarmLevel;
    /**
     * 告警类型
     */
    @ApiModelProperty(name = "alarmType", value = "告警类型")
    private Integer alarmType;
    /**
     * 设备名称
     */
    @ApiModelProperty(name = "deviceAlias", value = "设备名称")
    private String deviceAlias;
    /**
     * 设备标识
     */
    @ApiModelProperty(name = "deviceName", value = "设备标识")
    private String deviceName;
    /**
     * 告警位置
     */
    @ApiModelProperty(name = "alarmLocation", value = "告警位置")
    private String alarmLocation;
    /**
     * 设备位置
     */
    @ApiModelProperty(name = "deviceLocation", value = "设备位置")
    private String deviceLocation;
    /**
     * 处理状态(1:待处理 2:已处理 3:已关闭)
     */
    @ApiModelProperty(name = "handleStatus", value = "处理状态(1:待处理 2:已处理 3:已关闭)")
    private Integer handleStatus;
    /**
     * 告警开始时间
     */
    @ApiModelProperty(name = "alarmStartTime", value = "告警开始时间")
    private Date alarmStartTime;
    /**
     * 告警结束时间
     */
    @ApiModelProperty(name = "alarmEndTime", value = "告警结束时间")
    private Date alarmEndTime;
    /**
     * 关闭开始时间
     */
    @ApiModelProperty(name = "closeStartTime", value = "关闭开始时间")
    private Date closeStartTime;
    /**
     * 关闭结束时间
     */
    @ApiModelProperty(name = "closeEndTime", value = "关闭结束时间")
    private Date closeEndTime;

}
