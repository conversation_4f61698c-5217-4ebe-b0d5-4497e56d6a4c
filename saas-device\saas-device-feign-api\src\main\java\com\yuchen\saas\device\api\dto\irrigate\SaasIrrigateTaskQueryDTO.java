package com.yuchen.saas.device.api.dto.irrigate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;


@Data
public class SaasIrrigateTaskQueryDTO extends Query {

    /**
     * 项目ID
     */
    @ApiModelProperty(name = "projectId", value = "项目ID")
    private Long projectId;
    /**
     * 任务名称
     */
    @ApiModelProperty(name = "taskName", value = "任务名称")
    private String taskName;
    /**
     * 浇灌区域ID
     */
    @ApiModelProperty(name = "irrigateAreaId", value = "浇灌区域ID")
    private Long irrigateAreaId;
    /**
     * 浇灌点位ID
     */
    @ApiModelProperty(name = "irrigatePointId", value = "浇灌点位ID")
    private Long irrigatePointId;
    /**
     * 任务状态(1:待开始 2:处理中 3:已完成 4:已关闭)
     */
    @ApiModelProperty(name = "taskStatus", value = "任务状态(1:待开始 2:处理中 3:已完成 4:已关闭)")
    private Integer taskStatus;
    /**
     * 关闭原因(1:天气原因 2:养护调整 3:其他)
     */
    @ApiModelProperty(name = "closeReason", value = "关闭原因(1:天气原因 2:养护调整 3:其他)")
    private Integer closeReason;
    /**
     * 浇灌日期
     */
    @ApiModelProperty(name = "irrigateDate", value = "浇灌日期")
    private String irrigateDate;

}
