package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 组织申请来源（0后台添加、1小程序申请,2公众号申请）
 * <AUTHOR>
 */
@AllArgsConstructor
public enum DeptApplySourceEnum {


    BACKGROUND_ADD(0, "后台添加"),
    APPLET_REQUEST(1, "小程序申请"),
    PUBLIC_ACCOUNT_APPLICATION(2, "公众号申请"),
   ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        DeptApplySourceEnum[] enumArray = DeptApplySourceEnum.values();
        for (DeptApplySourceEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
