package com.yuchen.saas.consumer.service.service;

import com.yuchen.saas.consumer.api.entity.*;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: 李涛
 * @date 2022/1/5 15:58
 * @description:
 */
public interface TakeMessageService {

    /**
     * 发送审核结果通知
     * @param subMsgAuditResultsDTO 审核结果实体类
     */
    void sendAuditResults(SubMsgAuditResultsDTO subMsgAuditResultsDTO);


    /**
     * 充电结果通知
     * @param subChargeResultsDTO 充电结果实体类
     */
    void sendChargeResults(SubChargeResultsDTO subChargeResultsDTO);


    /**
     * 充电告警通知
     */
    void sendChargeWarnMsg(SubChargeWarnDTO subChargeWarnDTO);

    //--------------------------------- 暂时弃用 -------------------------------------

    /**
     * 月卡到期提醒订阅消息发送
     *
     * @param monthCardExpired
     * @return
     */
    void sendMonthCardExpired(MonthCardExpired monthCardExpired);

    /**
     * 退款成功消息发送
     *
     * @param refundInform
     * @return
     */
    void sendRefundSucceed(@RequestBody RefundInform refundInform);

    /**
     * 退款失败消息发送
     *
     * @param refundInform
     * @return
     */
    void sendRefundFailed(@RequestBody RefundInform refundInform);

    /**
     * 停车缴费成功通知
     *
     * @param parkingPayment
     * @return
     */
    void sendParkingPayment(@RequestBody ParkingPayment parkingPayment);

    /**
     * 车辆出场通知
     *
     * @param vehicleExit
     * @return
     */
    void sendVehicleExitOut(@RequestBody VehicleExit vehicleExit);

    /**
     * 车辆入场通知
     *
     * @param vehicleExit
     * @return
     */
    void sendVehicleExitIn(@RequestBody VehicleExit vehicleExit);

    /**
     * 电子发票提醒
     *
     * @param invoiceRemind
     * @return
     */
    void sendInvoiceRemind(@RequestBody InvoiceRemind invoiceRemind);

    /**
     * @description  访客预约(平台用户)通知
     * @param	visitorUserDTO	
     * @return  void
     */
    void sendVisitorUser(TempMessageVisitorUserDTO visitorUserDTO);

    /**
     * @description  访客预约(管理员)通知
     * @param	visitorManageDTO
     * @return  void
     */
    void sendVisitorManage(TempMessageVisitorManageDTO visitorManageDTO);

    /**
     * @description  访客预约（类目模板）通知
     * @param	visitorMessageDTO
     * @return  void
     */
    void sendVisitorMessage(TempVisitorMessageDTO visitorMessageDTO);

    /**
     * @description  访客取消预约（类目模板）通知
     * @param	visitorCancelMessageDTO
     * @return  void
     */
    void cancelVisitorMessage(TempVisitorCancelMessageDTO visitorCancelMessageDTO);

    void sendOccupyEventMsg(SubOccupyEventDTO subOccupyEventDTO);

    /**
     * @description  黑名单车辆过闸消息通知模板
     * @param	blacklistMessageDTO
     * @return  void
     */
    void vehicleBlacklistMessage(TempVehicleBlacklistMessageDTO blacklistMessageDTO);

    /**
     * 占位黑名单车辆过闸消息通知模板
     * @param occupyBlacklistMessageDTO
     */
    void occupyBlacklistMessage(TempOccupyBlacklistMessageDTO occupyBlacklistMessageDTO);
}
