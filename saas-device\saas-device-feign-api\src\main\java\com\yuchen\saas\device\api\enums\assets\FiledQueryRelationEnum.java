package com.yuchen.saas.device.api.enums.assets;

import com.yuchen.saas.device.api.enums.SaasDeviceErrorEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.nest.springwrap.core.log.exception.ServiceException;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/2/27 19:45
 */
@Getter
@AllArgsConstructor
public enum FiledQueryRelationEnum {

    /**
     * 等于
     */
    EQUAL("1", "等于"),
    /**
     * 包含
     */
    CONTAIN("2", "包含"),
    /**
     * 区间
     */
    SECTION("3", "区间"),
    /**
     * 在列表
     */
    LIST("4", "在列表"),
    ;

    private final String code;
    private final String desc;

    public static FiledQueryRelationEnum getEnumByCode(String code) {
        if (Objects.nonNull(code)) {
            for (FiledQueryRelationEnum e : FiledQueryRelationEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
        }
        throw new ServiceException(SaasDeviceErrorEnums.ENUMS_NOT_EXIST_ERROR.getMessage());
    }
}
