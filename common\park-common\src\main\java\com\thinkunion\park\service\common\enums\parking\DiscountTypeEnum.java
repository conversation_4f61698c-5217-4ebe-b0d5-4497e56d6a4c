package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 优惠类型
 * 1：固定优惠金额
 * 2：全免优惠金额
 * <AUTHOR>
 */
@AllArgsConstructor
public enum DiscountTypeEnum {


    FIXED_DISCOUNT_AMOUNT(1, "固定优惠金额"),
    FULL_DISCOUNT_AMOUNT_FREE(2, "全免优惠金额"),
   ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        DiscountTypeEnum[] enumArray = DiscountTypeEnum.values();
        for (DiscountTypeEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
