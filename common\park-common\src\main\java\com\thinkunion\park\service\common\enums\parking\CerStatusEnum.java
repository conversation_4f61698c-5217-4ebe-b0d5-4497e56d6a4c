package com.thinkunion.park.service.common.enums.parking;

import lombok.AllArgsConstructor;

/**
 * 认证状态:0未认证,1已认证,2已过期(V-rebuild1.0新增),3认证中
 * <AUTHOR>
 */
@AllArgsConstructor
public enum CerStatusEnum {


    UNCERTIFIED(0, "未认证"),
    CERTIFIED(1, "已认证"),
    EXPIRED(2,"已过期"),
    AUTHENTICATING(3,"认证中"),
   ;

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String description;

    public static String getDescByCode(Integer code) {
        CerStatusEnum[] enumArray = CerStatusEnum.values();
        for (CerStatusEnum e : enumArray) {
            if (e.getCode().equals(code)) {
                return e.getDescription();
            }
        }
        return "";
    }
}
