package com.yuchen.saas.device.api.dto.operatingEnd;

import com.thinkunion.park.service.common.constant.GroupAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @create 2024/5/21 11:49
 */
@Data
public class NetProductSubmitReqDTO {


    @NotNull(groups = GroupAction.UpdateAction.class, message = "id不能为空")
    @Null(groups = GroupAction.InsertAction.class, message = "id必须为空")
    private Long id;
    /**
     * 产品来源（1：IOT产品，2：第三方产品）
     */
   /* @ApiModelProperty(name = "productSource", value = "产品来源（1：IOT产品，2：第三方产品）")
    @NotBlank(message = "请选择产品来源")
    private String productSource;*/
   /* *//**
     * 第三方名称ID
     *//*
    @ApiModelProperty(name = "thirdPartyId", value = "第三方名称ID")
    private Long thirdPartyId;*/
    /**
     * 产品分类id
     */
    @ApiModelProperty(name = "productCategoryId", value = "产品分类id")
    @NotNull(message = "产品分类不能为空")
    private Long productCategoryId;
    /**
     * 产品标识
     */
    @ApiModelProperty(name = "productKey", value = "产品标识")
    private String productKey;
    /**
     * 产品名称
     */
    @ApiModelProperty(name = "productName", value = "产品名称")
    @NotBlank(message = "产品名称不能为空")
    private String productName;

    @ApiModelProperty(name = "isSyncIot", value = "是否同步iot平台产品物模型 0否1是")
    @NotNull(message = "同步iot平台产品物模型不能为空")
    private Integer isSyncIot;

    /**
     * iot产品标识
     */
    @ApiModelProperty(name = "iotProductKey", value = "iot产品标识")
    private String iotProductKey;

    @ApiModelProperty(name = "iotProductName", value = "iot产品名称")
    private String iotProductName;
    /**
     * 产品类型
     */
    @ApiModelProperty(name = "productType", value = "产品类型")
    private Integer productType;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
    /**
     * 业务状态 1开启，0关闭
     */
    @ApiModelProperty("业务状态 业务状态 1开启，0关闭")
    private Integer status;
}
