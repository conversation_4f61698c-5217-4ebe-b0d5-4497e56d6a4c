<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuchen.saas.device.service.mapper.net.NetAlarmMapper">

    <select id="qryAlarmVo" resultType="com.yuchen.saas.device.api.vo.NetAlarmVo">
        select * from net_alarm where status = 1 and is_deleted = 0
        <if test="alarmName != null and alarmName != ''">
            and alarm_name = #{alarmName}
        </if>
        <if test="alarmSource != null">
            and alarm_source = #{alarmSource}
        </if>
        <if test="productKey != null and productKey != ''">
            and product_key = #{productKey}
        </if>
        <if test="deviceName != null and deviceName != ''">
            and device_name = #{deviceName}
        </if>
        <if test="triggerMode != null">
            and trigger_mode = #{triggerMode}
        </if>
        <if test="serviceId != null">
            and service_id = #{serviceId}
        </if>
        <if test="sceneId != null">
            and scene_id = #{sceneId}
        </if>
        <if test="thirdPartyId != null">
            and third_party_id = #{thirdPartyId}
        </if>
        <if test="alarmTypeCode != null and alarmTypeCode != ''">
            and alarm_type_code = #{alarmTypeCode}
        </if>
        <if test="eventCode != null and eventCode != ''">
            and event_code = #{eventCode}
        </if>
    </select>
</mapper>