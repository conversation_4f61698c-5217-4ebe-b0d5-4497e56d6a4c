package com.yuchen.saas.device.api.dto.operatingEnd.alarm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nest.springwrap.core.mp.support.Query;

/**
 * <AUTHOR>
 * @create 2024/6/25 10:04
 */
@Data
public class NetAlarmLevelQryReqDTO extends Query {
    /**
     * 所属客户
     */
    @ApiModelProperty(name = "customerId", value = "所属客户")
    private Long customerId;
//    /**
//     * 告警名称
//     */
//    @ApiModelProperty(name = "levelName", value = "告警名称")
//    private String levelName;
//    /**
//     * 告警编码
//     */
//    @ApiModelProperty(name = "levelCode", value = "告警编码")
//    private String levelCode;
}
