package com.yuchen.saas.auth.granter;

import com.thinkunion.park.service.common.cache.CacheNames;
import com.yuchen.saas.auth.utils.TokenUtil;
import org.nest.springwrap.core.redis.cache.NestRedis;
import org.nest.springwrap.core.tool.utils.Func;
import org.nest.springwrap.core.tool.utils.StringUtil;
import org.nest.springwrap.core.tool.utils.WebUtil;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.Map;

public class CaptchaTokenGranter extends AbstractTokenGranter {
    private Integer defaultTimes=3;

    private static final String GRANT_TYPE = "captcha";

    private final AuthenticationManager authenticationManager;

    private NestRedis nestRedis;

    public CaptchaTokenGranter(AuthenticationManager authenticationManager,
                               AuthorizationServerTokenServices tokenServices,
                               ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory,
                               NestRedis nestRedis) {
        this(authenticationManager, tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
        this.nestRedis = nestRedis;
    }

    protected CaptchaTokenGranter(AuthenticationManager authenticationManager,
                                  AuthorizationServerTokenServices tokenServices,
                                  ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory,
                                  String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.authenticationManager = authenticationManager;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {
        HttpServletRequest request = WebUtil.getRequest();
        // 增加验证码判断
        //获取登录次数缓存

        /*String key = request.getHeader(TokenUtil.CAPTCHA_HEADER_KEY);
        String code = request.getHeader(TokenUtil.CAPTCHA_HEADER_CODE);
        // 获取验证码
        String redisCode = nestRedis.get(CacheNames.CAPTCHA_KEY + key);
        // 判断验证码
        if (code == null || !StringUtil.equalsIgnoreCase(redisCode, code)) {
            throw new UserDeniedAuthorizationException(TokenUtil.CAPTCHA_NOT_CORRECT);
        }*/

        Map<String, String> parameters = new LinkedHashMap<String, String>(tokenRequest.getRequestParameters());
        String username = parameters.get("username");
        String password = parameters.get("password");
        // Protect from downstream leaks of password
        parameters.remove("password");

        //获取登录次数缓存
        //验证码二次检验,第一次先校验了后续不再校验先注释
        /*Integer times = nestRedis.get(String.format(CacheNames.CAPTCHA_KEY_TIMES,username));
        if(Func.isNotEmpty(times)&&times>defaultTimes){
            String code = parameters.get("code");
            String redisCode = nestRedis.get(CacheNames.CAPTCHA_KEY + username);
            nestRedis.del(CacheNames.CAPTCHA_KEY + username);
            if(Func.isEmpty(redisCode)||!StringUtil.equalsIgnoreCase(redisCode, code)){
                throw new UserDeniedAuthorizationException(TokenUtil.CAPTCHA_NOT_CORRECT);
            }

        }*/

        Authentication userAuth = new UsernamePasswordAuthenticationToken(username, password);
        ((AbstractAuthenticationToken) userAuth).setDetails(parameters);
        try {
            userAuth = authenticationManager.authenticate(userAuth);
        } catch (AccountStatusException | BadCredentialsException ase) {
            /*if(Func.isEmpty(times)){
                times=1;
            }else{
                times=times+1;
            }
            nestRedis.setEx(String.format(CacheNames.CAPTCHA_KEY_TIMES,username),times, Duration.ofMinutes(60));
            if(times>defaultTimes){
                throw new UserDeniedAuthorizationException(TokenUtil.CAPTCHA_NEED);
            }*/
            //covers expired, locked, disabled cases (mentioned in section 5.2, draft 31)
            throw new InvalidGrantException(ase.getMessage());
        }
        // If the username/password are wrong the spec says we should send 400/invalid grant

        if (userAuth == null || !userAuth.isAuthenticated()) {
            throw new InvalidGrantException("Could not authenticate user: " + username);
        }
        //验证通过的话清掉次数
       // nestRedis.del(String.format(CacheNames.CAPTCHA_KEY_TIMES,username));


        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, userAuth);
    }
}
