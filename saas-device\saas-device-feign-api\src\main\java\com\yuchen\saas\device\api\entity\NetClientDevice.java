package com.yuchen.saas.device.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 客户端设备管理
 */
@Data
@TableName("net_client_device")
public class NetClientDevice {

    /**
     * id主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 所属客户(组织)
     */
    @NotNull(message = "所属客户id不能为空")
    private Long customerManageId;
    /**
     * 管理区ids（逗号拼接）
     */
    private String manageAreaIds;
    /**
     * 空间位置
     */
    private String spatialPosition;
    /**
     * 产品id
     */
    @NotNull(message = "产品名称不能为空")
    private Long productId;
    /**
     * 运营端维护的设备id
     */
    @NotNull(message = "设备id不能为空")
    private Long operateDeviceId;
    /**
     * 设备名称
     */
    @NotNull(message = "设备名称不能为")
    private String deviceName;
    /**
     * 设备唯一标识
     */
    @NotNull(message = "设备唯一标识不能为")
    private String deviceKey;
    /**
     * 分组id
     */
    private Long groupingId;
    /**
     * 设备别名
     */
    private String deviceAlias;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 型号
     */
    private String model;
    /**
     * 地理位置
     */
    private String location;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 三维空间id
     */
    private String spatialId;
    /**
     * 设备图片
     */
    private String filePath;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private String customerManageName;
    @TableField(exist = false)
    private String productName;

}
